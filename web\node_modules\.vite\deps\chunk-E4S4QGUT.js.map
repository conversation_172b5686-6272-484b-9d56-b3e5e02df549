{"version": 3, "sources": ["../../@vidstack/react/dev/chunks/vidstack-C8ZxSSGF.js"], "sourcesContent": ["\"use client\"\n\nimport { IS_CHROME, IS_IOS, canGoogleCastSrc, IS_SERVER, loadScript } from './vidstack-C-WrcxmD.js';\nimport { listenEvent, peek } from './vidstack-CH225ns1.js';\n\nfunction getCastFrameworkURL() {\n  return \"https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1\";\n}\nfunction hasLoadedCastFramework() {\n  return !!window.cast?.framework;\n}\nfunction isCastAvailable() {\n  return !!window.chrome?.cast?.isAvailable;\n}\nfunction isCastConnected() {\n  return getCastContext().getCastState() === cast.framework.CastState.CONNECTED;\n}\nfunction getCastContext() {\n  return window.cast.framework.CastContext.getInstance();\n}\nfunction getCastSession() {\n  return getCastContext().getCurrentSession();\n}\nfunction getCastSessionMedia() {\n  return getCastSession()?.getSessionObj().media[0];\n}\nfunction hasActiveCastSession(src) {\n  const contentId = getCastSessionMedia()?.media.contentId;\n  return contentId === src?.src;\n}\nfunction getDefaultCastOptions() {\n  return {\n    language: \"en-US\",\n    autoJoinPolicy: chrome.cast.AutoJoinPolicy.ORIGIN_SCOPED,\n    receiverApplicationId: chrome.cast.media.DEFAULT_MEDIA_RECEIVER_APP_ID,\n    resumeSavedSession: true,\n    androidReceiverCompatible: true\n  };\n}\nfunction getCastErrorMessage(code) {\n  const defaultMessage = `Google Cast Error Code: ${code}`;\n  {\n    switch (code) {\n      case chrome.cast.ErrorCode.API_NOT_INITIALIZED:\n        return \"The API is not initialized.\";\n      case chrome.cast.ErrorCode.CANCEL:\n        return \"The operation was canceled by the user\";\n      case chrome.cast.ErrorCode.CHANNEL_ERROR:\n        return \"A channel to the receiver is not available.\";\n      case chrome.cast.ErrorCode.EXTENSION_MISSING:\n        return \"The Cast extension is not available.\";\n      case chrome.cast.ErrorCode.INVALID_PARAMETER:\n        return \"The parameters to the operation were not valid.\";\n      case chrome.cast.ErrorCode.RECEIVER_UNAVAILABLE:\n        return \"No receiver was compatible with the session request.\";\n      case chrome.cast.ErrorCode.SESSION_ERROR:\n        return \"A session could not be created, or a session was invalid.\";\n      case chrome.cast.ErrorCode.TIMEOUT:\n        return \"The operation timed out.\";\n      default:\n        return defaultMessage;\n    }\n  }\n}\nfunction listenCastContextEvent(type, handler) {\n  return listenEvent(getCastContext(), type, handler);\n}\n\nclass GoogleCastLoader {\n  name = \"google-cast\";\n  target;\n  #player;\n  /**\n   * @see {@link https://developers.google.com/cast/docs/reference/web_sender/cast.framework.CastContext}\n   */\n  get cast() {\n    return getCastContext();\n  }\n  mediaType() {\n    return \"video\";\n  }\n  canPlay(src) {\n    return IS_CHROME && !IS_IOS && canGoogleCastSrc(src);\n  }\n  async prompt(ctx) {\n    let loadEvent, openEvent, errorEvent;\n    try {\n      loadEvent = await this.#loadCastFramework(ctx);\n      if (!this.#player) {\n        this.#player = new cast.framework.RemotePlayer();\n        new cast.framework.RemotePlayerController(this.#player);\n      }\n      openEvent = ctx.player.createEvent(\"google-cast-prompt-open\", {\n        trigger: loadEvent\n      });\n      ctx.player.dispatchEvent(openEvent);\n      this.#notifyRemoteStateChange(ctx, \"connecting\", openEvent);\n      await this.#showPrompt(peek(ctx.$props.googleCast));\n      ctx.$state.remotePlaybackInfo.set({\n        deviceName: getCastSession()?.getCastDevice().friendlyName\n      });\n      if (isCastConnected()) this.#notifyRemoteStateChange(ctx, \"connected\", openEvent);\n    } catch (code) {\n      const error = code instanceof Error ? code : this.#createError(\n        (code + \"\").toUpperCase(),\n        \"Prompt failed.\"\n      );\n      errorEvent = ctx.player.createEvent(\"google-cast-prompt-error\", {\n        detail: error,\n        trigger: openEvent ?? loadEvent,\n        cancelable: true\n      });\n      ctx.player.dispatch(errorEvent);\n      this.#notifyRemoteStateChange(\n        ctx,\n        isCastConnected() ? \"connected\" : \"disconnected\",\n        errorEvent\n      );\n      throw error;\n    } finally {\n      ctx.player.dispatch(\"google-cast-prompt-close\", {\n        trigger: errorEvent ?? openEvent ?? loadEvent\n      });\n    }\n  }\n  async load(ctx) {\n    if (IS_SERVER) {\n      throw Error(\"[vidstack] can not load google cast provider server-side\");\n    }\n    if (!this.#player) {\n      throw Error(\"[vidstack] google cast player was not initialized\");\n    }\n    return new (await import('./vidstack-Dybq7b7g.js')).GoogleCastProvider(this.#player, ctx);\n  }\n  async #loadCastFramework(ctx) {\n    if (hasLoadedCastFramework()) return;\n    const loadStartEvent = ctx.player.createEvent(\"google-cast-load-start\");\n    ctx.player.dispatch(loadStartEvent);\n    await loadScript(getCastFrameworkURL());\n    await customElements.whenDefined(\"google-cast-launcher\");\n    const loadedEvent = ctx.player.createEvent(\"google-cast-loaded\", { trigger: loadStartEvent });\n    ctx.player.dispatch(loadedEvent);\n    if (!isCastAvailable()) {\n      throw this.#createError(\"CAST_NOT_AVAILABLE\", \"Google Cast not available on this platform.\");\n    }\n    return loadedEvent;\n  }\n  async #showPrompt(options) {\n    this.#setOptions(options);\n    const errorCode = await this.cast.requestSession();\n    if (errorCode) {\n      throw this.#createError(\n        errorCode.toUpperCase(),\n        getCastErrorMessage(errorCode)\n      );\n    }\n  }\n  #setOptions(options) {\n    this.cast?.setOptions({\n      ...getDefaultCastOptions(),\n      ...options\n    });\n  }\n  #notifyRemoteStateChange(ctx, state, trigger) {\n    const detail = { type: \"google-cast\", state };\n    ctx.notify(\"remote-playback-change\", detail, trigger);\n  }\n  #createError(code, message) {\n    const error = Error(message);\n    error.code = code;\n    return error;\n  }\n}\n\nvar loader = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  GoogleCastLoader: GoogleCastLoader\n});\n\nexport { getCastContext, getCastErrorMessage, getCastSession, getCastSessionMedia, hasActiveCastSession, listenCastContextEvent, loader };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAKA,SAAS,sBAAsB;AAC7B,SAAO;AACT;AACA,SAAS,yBAAyB;AARlC;AASE,SAAO,CAAC,GAAC,YAAO,SAAP,mBAAa;AACxB;AACA,SAAS,kBAAkB;AAX3B;AAYE,SAAO,CAAC,GAAC,kBAAO,WAAP,mBAAe,SAAf,mBAAqB;AAChC;AACA,SAAS,kBAAkB;AACzB,SAAO,eAAe,EAAE,aAAa,MAAM,KAAK,UAAU,UAAU;AACtE;AACA,SAAS,iBAAiB;AACxB,SAAO,OAAO,KAAK,UAAU,YAAY,YAAY;AACvD;AACA,SAAS,iBAAiB;AACxB,SAAO,eAAe,EAAE,kBAAkB;AAC5C;AACA,SAAS,sBAAsB;AAvB/B;AAwBE,UAAO,oBAAe,MAAf,mBAAkB,gBAAgB,MAAM;AACjD;AACA,SAAS,qBAAqB,KAAK;AA1BnC;AA2BE,QAAM,aAAY,yBAAoB,MAApB,mBAAuB,MAAM;AAC/C,SAAO,eAAc,2BAAK;AAC5B;AACA,SAAS,wBAAwB;AAC/B,SAAO;AAAA,IACL,UAAU;AAAA,IACV,gBAAgB,OAAO,KAAK,eAAe;AAAA,IAC3C,uBAAuB,OAAO,KAAK,MAAM;AAAA,IACzC,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,EAC7B;AACF;AACA,SAAS,oBAAoB,MAAM;AACjC,QAAM,iBAAiB,2BAA2B,IAAI;AACtD;AACE,YAAQ,MAAM;AAAA,MACZ,KAAK,OAAO,KAAK,UAAU;AACzB,eAAO;AAAA,MACT,KAAK,OAAO,KAAK,UAAU;AACzB,eAAO;AAAA,MACT,KAAK,OAAO,KAAK,UAAU;AACzB,eAAO;AAAA,MACT,KAAK,OAAO,KAAK,UAAU;AACzB,eAAO;AAAA,MACT,KAAK,OAAO,KAAK,UAAU;AACzB,eAAO;AAAA,MACT,KAAK,OAAO,KAAK,UAAU;AACzB,eAAO;AAAA,MACT,KAAK,OAAO,KAAK,UAAU;AACzB,eAAO;AAAA,MACT,KAAK,OAAO,KAAK,UAAU;AACzB,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AACF;AACA,SAAS,uBAAuB,MAAM,SAAS;AAC7C,SAAO,YAAY,eAAe,GAAG,MAAM,OAAO;AACpD;AAlEA;AAoEA,IAAM,mBAAN,MAAuB;AAAA,EAAvB;AAAA;AACE,gCAAO;AACP;AACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,YAAY;AACV,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,aAAa,CAAC,UAAU,iBAAiB,GAAG;AAAA,EACrD;AAAA,EACA,MAAM,OAAO,KAAK;AApFpB;AAqFI,QAAI,WAAW,WAAW;AAC1B,QAAI;AACF,kBAAY,MAAM,sBAAK,mDAAL,WAAwB;AAC1C,UAAI,CAAC,mBAAK,UAAS;AACjB,2BAAK,SAAU,IAAI,KAAK,UAAU,aAAa;AAC/C,YAAI,KAAK,UAAU,uBAAuB,mBAAK,QAAO;AAAA,MACxD;AACA,kBAAY,IAAI,OAAO,YAAY,2BAA2B;AAAA,QAC5D,SAAS;AAAA,MACX,CAAC;AACD,UAAI,OAAO,cAAc,SAAS;AAClC,4BAAK,yDAAL,WAA8B,KAAK,cAAc;AACjD,YAAM,sBAAK,4CAAL,WAAiB,KAAK,IAAI,OAAO,UAAU;AACjD,UAAI,OAAO,mBAAmB,IAAI;AAAA,QAChC,aAAY,oBAAe,MAAf,mBAAkB,gBAAgB;AAAA,MAChD,CAAC;AACD,UAAI,gBAAgB,EAAG,uBAAK,yDAAL,WAA8B,KAAK,aAAa;AAAA,IACzE,SAAS,MAAM;AACb,YAAM,QAAQ,gBAAgB,QAAQ,OAAO,sBAAK,6CAAL,YAC1C,OAAO,IAAI,YAAY,GACxB;AAEF,mBAAa,IAAI,OAAO,YAAY,4BAA4B;AAAA,QAC9D,QAAQ;AAAA,QACR,SAAS,aAAa;AAAA,QACtB,YAAY;AAAA,MACd,CAAC;AACD,UAAI,OAAO,SAAS,UAAU;AAC9B,4BAAK,yDAAL,WACE,KACA,gBAAgB,IAAI,cAAc,gBAClC;AAEF,YAAM;AAAA,IACR,UAAE;AACA,UAAI,OAAO,SAAS,4BAA4B;AAAA,QAC9C,SAAS,cAAc,aAAa;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,KAAK,KAAK;AACd,QAAI,WAAW;AACb,YAAM,MAAM,0DAA0D;AAAA,IACxE;AACA,QAAI,CAAC,mBAAK,UAAS;AACjB,YAAM,MAAM,mDAAmD;AAAA,IACjE;AACA,WAAO,KAAK,MAAM,OAAO,iCAAwB,GAAG,mBAAmB,mBAAK,UAAS,GAAG;AAAA,EAC1F;AAuCF;AArGE;AAHF;AAkEQ,uBAAkB,eAAC,KAAK;AAC5B,MAAI,uBAAuB,EAAG;AAC9B,QAAM,iBAAiB,IAAI,OAAO,YAAY,wBAAwB;AACtE,MAAI,OAAO,SAAS,cAAc;AAClC,QAAM,WAAW,oBAAoB,CAAC;AACtC,QAAM,eAAe,YAAY,sBAAsB;AACvD,QAAM,cAAc,IAAI,OAAO,YAAY,sBAAsB,EAAE,SAAS,eAAe,CAAC;AAC5F,MAAI,OAAO,SAAS,WAAW;AAC/B,MAAI,CAAC,gBAAgB,GAAG;AACtB,UAAM,sBAAK,6CAAL,WAAkB,sBAAsB;AAAA,EAChD;AACA,SAAO;AACT;AACM,gBAAW,eAAC,SAAS;AACzB,wBAAK,4CAAL,WAAiB;AACjB,QAAM,YAAY,MAAM,KAAK,KAAK,eAAe;AACjD,MAAI,WAAW;AACb,UAAM,sBAAK,6CAAL,WACJ,UAAU,YAAY,GACtB,oBAAoB,SAAS;AAAA,EAEjC;AACF;AACA,gBAAW,SAAC,SAAS;AA7JvB;AA8JI,aAAK,SAAL,mBAAW,WAAW;AAAA,IACpB,GAAG,sBAAsB;AAAA,IACzB,GAAG;AAAA,EACL;AACF;AACA,6BAAwB,SAAC,KAAK,OAAO,SAAS;AAC5C,QAAM,SAAS,EAAE,MAAM,eAAe,MAAM;AAC5C,MAAI,OAAO,0BAA0B,QAAQ,OAAO;AACtD;AACA,iBAAY,SAAC,MAAM,SAAS;AAC1B,QAAM,QAAQ,MAAM,OAAO;AAC3B,QAAM,OAAO;AACb,SAAO;AACT;AAGF,IAAI,SAAsB,OAAO,OAAO;AAAA,EACtC,WAAW;AAAA,EACX;AACF,CAAC;", "names": []}