{"version": 3, "sources": ["../../@vidstack/react/dev/chunks/vidstack-Zc3I7oOd.js"], "sourcesContent": ["\"use client\"\n\nconst videoIdRE = /(?:youtu\\.be|youtube|youtube\\.com|youtube-nocookie\\.com)\\/(?:embed\\/|v\\/|watch\\?v=|watch\\?.+&v=|)((?:\\w|-){11})/;\nconst posterCache = /* @__PURE__ */ new Map();\nconst pendingFetch = /* @__PURE__ */ new Map();\nfunction resolveYouTubeVideoId(src) {\n  return src.match(videoIdRE)?.[1];\n}\nasync function findYouTubePoster(videoId, abort) {\n  if (posterCache.has(videoId)) return posterCache.get(videoId);\n  if (pendingFetch.has(videoId)) return pendingFetch.get(videoId);\n  const pending = new Promise(async (resolve) => {\n    const sizes = [\"maxresdefault\", \"sddefault\", \"hqdefault\"];\n    for (const size of sizes) {\n      for (const webp of [true, false]) {\n        const url = resolveYouTubePosterURL(videoId, size, webp), response = await fetch(url, {\n          mode: \"no-cors\",\n          signal: abort.signal\n        });\n        if (response.status < 400) {\n          posterCache.set(videoId, url);\n          resolve(url);\n          return;\n        }\n      }\n    }\n  }).catch(() => \"\").finally(() => pendingFetch.delete(videoId));\n  pendingFetch.set(videoId, pending);\n  return pending;\n}\nfunction resolveYouTubePosterURL(videoId, size, webp) {\n  const type = webp ? \"webp\" : \"jpg\";\n  return `https://i.ytimg.com/${webp ? \"vi_webp\" : \"vi\"}/${videoId}/${size}.${type}`;\n}\n\nexport { findYouTubePoster, resolveYouTubeVideoId };\n"], "mappings": ";AAEA,IAAM,YAAY;AAClB,IAAM,cAA8B,oBAAI,IAAI;AAC5C,IAAM,eAA+B,oBAAI,IAAI;AAC7C,SAAS,sBAAsB,KAAK;AALpC;AAME,UAAO,SAAI,MAAM,SAAS,MAAnB,mBAAuB;AAChC;AACA,eAAe,kBAAkB,SAAS,OAAO;AAC/C,MAAI,YAAY,IAAI,OAAO,EAAG,QAAO,YAAY,IAAI,OAAO;AAC5D,MAAI,aAAa,IAAI,OAAO,EAAG,QAAO,aAAa,IAAI,OAAO;AAC9D,QAAM,UAAU,IAAI,QAAQ,OAAO,YAAY;AAC7C,UAAM,QAAQ,CAAC,iBAAiB,aAAa,WAAW;AACxD,eAAW,QAAQ,OAAO;AACxB,iBAAW,QAAQ,CAAC,MAAM,KAAK,GAAG;AAChC,cAAM,MAAM,wBAAwB,SAAS,MAAM,IAAI,GAAG,WAAW,MAAM,MAAM,KAAK;AAAA,UACpF,MAAM;AAAA,UACN,QAAQ,MAAM;AAAA,QAChB,CAAC;AACD,YAAI,SAAS,SAAS,KAAK;AACzB,sBAAY,IAAI,SAAS,GAAG;AAC5B,kBAAQ,GAAG;AACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC,EAAE,MAAM,MAAM,EAAE,EAAE,QAAQ,MAAM,aAAa,OAAO,OAAO,CAAC;AAC7D,eAAa,IAAI,SAAS,OAAO;AACjC,SAAO;AACT;AACA,SAAS,wBAAwB,SAAS,MAAM,MAAM;AACpD,QAAM,OAAO,OAAO,SAAS;AAC7B,SAAO,uBAAuB,OAAO,YAAY,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,IAAI;AAClF;", "names": []}