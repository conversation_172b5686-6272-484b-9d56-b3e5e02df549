"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.reconstructChangeLedgerClass = exports.UnknownChange = exports.UpdateClassesChange = exports.RedoChange = exports.UndoChange = exports.EditTextChange = exports.RemoveClassChange = exports.AddClassChange = exports.ChangeTagChange = exports.DuplicateChange = exports.WrapDivChange = exports.ChangePropChange = exports.RemoveJsxChange = exports.MoveJsxChange = exports.AddJsxChange = exports.StylingChange = exports.ChangeLedgerItem = exports.CHANGE_TYPES_WITH_INSTANT_UNDO = exports.ChangeType = exports.StylingFramework = exports.DUPLICATE_PLACEHOLDER_PREFIX = exports.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID = void 0;
const tempoElement_1 = require("./tempoElement");
const uuid_1 = require("uuid");
exports.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID = 'tempo-wrap-in-div-placeholder';
exports.DUPLICATE_PLACEHOLDER_PREFIX = 'tempo-duplicate-placeholder-';
// Matches the file in tempo-devtools
var StylingFramework;
(function (StylingFramework) {
    StylingFramework["INLINE"] = "Inline";
    StylingFramework["CSS"] = "CSS";
    StylingFramework["TAILWIND"] = "Tailwind";
})(StylingFramework || (exports.StylingFramework = StylingFramework = {}));
var ChangeType;
(function (ChangeType) {
    ChangeType["STYLING"] = "STYLING";
    ChangeType["ADD_JSX"] = "ADD_JSX";
    ChangeType["MOVE_JSX"] = "MOVE_JSX";
    ChangeType["REMOVE_JSX"] = "REMOVE_JSX";
    ChangeType["CHANGE_PROP"] = "CHANGE_PROP";
    ChangeType["ADD_CLASS"] = "ADD_CLASS";
    ChangeType["REMOVE_CLASS"] = "REMOVE_CLASS";
    ChangeType["UPDATE_CLASSES"] = "UPDATE_CLASSES";
    ChangeType["EDIT_TEXT"] = "EDIT_TEXT";
    ChangeType["WRAP_DIV"] = "WRAP_DIV";
    ChangeType["CHANGE_TAG"] = "CHANGE_TAG";
    ChangeType["DUPLICATE"] = "DUPLICATE";
    ChangeType["UNDO"] = "UNDO";
    ChangeType["REDO"] = "REDO";
    ChangeType["UNKNOWN"] = "UNKNOWN";
})(ChangeType || (exports.ChangeType = ChangeType = {}));
// Make sure to match this in both tempo-devtools & ** tempo-api ** (in the undo/redo file)
exports.CHANGE_TYPES_WITH_INSTANT_UNDO = [
    ChangeType.REMOVE_JSX,
    ChangeType.ADD_CLASS,
    ChangeType.STYLING,
    ChangeType.UPDATE_CLASSES,
];
class ChangeLedgerItem {
    constructor(type, changeName, changeFields, id) {
        this.prevIdToNewIdMap = {};
        this.id = id || (0, uuid_1.v4)();
        this.type = type;
        this.changeFields = changeFields;
        this.changeName = changeName;
        this._consumed = false;
        this._failed = false;
        this._instantUpdateSent = false;
        this._instantUpdateFinished = false;
        this._instantUpdateSuccessful = false;
        this._sendInstantUpdate = true;
        this.canInstantUpdateWhileFlushing = false;
        this._apiPromise = new Promise((resolve, reject) => {
            this._resolveApi = resolve;
            this._rejectApi = reject;
        });
    }
    resolveApi(data) {
        var _a;
        (_a = this._resolveApi) === null || _a === void 0 ? void 0 : _a.call(this, data);
    }
    rejectApi(reason) {
        var _a;
        if (this._apiRejectionAdded) {
            (_a = this._rejectApi) === null || _a === void 0 ? void 0 : _a.call(this, reason);
        }
    }
    needsToSendInstantUpdate() {
        return !this._instantUpdateSent && this._sendInstantUpdate;
    }
    markInstantUpdateSent() {
        this._instantUpdateSent = true;
    }
    getInstantUpdateSent() {
        return this._instantUpdateSent;
    }
    markInstantUpdateFinished(instantUpdateData, instantUpdateSuccessful) {
        this._instantUpdateFinished = true;
        this._instantUpdateSuccessful = instantUpdateSuccessful;
        this._instantUpdateData = instantUpdateData;
    }
    getInstantUpdateData() {
        return this._instantUpdateData;
    }
    wasInstantUpdateSuccessful() {
        return this._instantUpdateSuccessful;
    }
    isInstantUpdateFinished() {
        return this._instantUpdateFinished;
    }
    markProcessedSucceeded() {
        this._consumed = true;
    }
    markProcessedFailed() {
        this._failed = true;
        this._consumed = true;
    }
    isFailed() {
        return this._failed;
    }
    needToProcessChange() {
        return !this._consumed;
    }
    onApiResolve(onFulfilled) {
        return this._apiPromise.then(onFulfilled);
    }
    onApiReject(onRejected) {
        this._apiRejectionAdded = true;
        return this._apiPromise.catch(onRejected);
    }
    doNotSendInstantUpdate() {
        this._sendInstantUpdate = false;
    }
    // For selecting/deslecting new elements after instant updates
    clearSelectedElementsAfterInstantUpdate() {
        this.elementKeyToSelectAfterInstantUpdate = null;
        this.elementKeysToMultiselectAfterInstantUpdate = null;
    }
    setSelectedElementsAfterInstantUpdate(selectedElementKey, multiselectedElementKeys) {
        this.elementKeyToSelectAfterInstantUpdate = selectedElementKey;
        this.elementKeysToMultiselectAfterInstantUpdate = multiselectedElementKeys;
    }
    clearSelectedElementsAfterUndoInstantUpdate() {
        this.elementKeyToSelectAfterUndoInstantUpdate = null;
        this.elementKeysToMultiselectAfterUndoInstantUpdate = null;
    }
    setSelectedElementsAfterUndoInstantUpdate(selectedElementKey, multiselectedElementKeys) {
        this.elementKeyToSelectAfterUndoInstantUpdate = selectedElementKey;
        this.elementKeysToMultiselectAfterUndoInstantUpdate =
            multiselectedElementKeys;
    }
    getElementKeyToSelectAfterInstantUpdate() {
        return this.elementKeyToSelectAfterInstantUpdate;
    }
    getElementKeysToMultiselectAfterInstantUpdate() {
        return this.elementKeysToMultiselectAfterInstantUpdate;
    }
    getElementKeyToSelectAfterUndoInstantUpdate() {
        return this.elementKeyToSelectAfterUndoInstantUpdate;
    }
    getElementKeysToMultiselectAfterUndoInstantUpdate() {
        return this.elementKeysToMultiselectAfterUndoInstantUpdate;
    }
    applyAllCodebaseIdChanges(prevIdToNewIdMap) {
        var _a, _b;
        const getNewKey = (prevKey) => {
            if (!prevKey) {
                return null;
            }
            const tempoElement = tempoElement_1.TempoElement.fromKey(prevKey);
            const codebaseId = tempoElement.codebaseId;
            const newCodebaseId = prevIdToNewIdMap[codebaseId];
            if (newCodebaseId) {
                return new tempoElement_1.TempoElement(newCodebaseId, tempoElement.storyboardId, tempoElement.uniquePath).getKey();
            }
            return null;
        };
        /*
         * Instant update fields
         */
        if (this.elementKeyToSelectAfterInstantUpdate) {
            const newElementKey = getNewKey(this.elementKeyToSelectAfterInstantUpdate);
            this.elementKeyToSelectAfterInstantUpdate =
                newElementKey || this.elementKeyToSelectAfterInstantUpdate;
        }
        if (this.elementKeysToMultiselectAfterInstantUpdate) {
            this.elementKeysToMultiselectAfterInstantUpdate =
                (_a = this.elementKeysToMultiselectAfterInstantUpdate) === null || _a === void 0 ? void 0 : _a.map((key) => {
                    const newKey = getNewKey(key);
                    return newKey || key;
                });
        }
        /*
         * Undo instant update fields
         */
        if (this.elementKeyToSelectAfterUndoInstantUpdate) {
            const newElementKey = getNewKey(this.elementKeyToSelectAfterUndoInstantUpdate);
            this.elementKeyToSelectAfterUndoInstantUpdate =
                newElementKey || this.elementKeyToSelectAfterUndoInstantUpdate;
        }
        if (this.elementKeysToMultiselectAfterUndoInstantUpdate) {
            this.elementKeysToMultiselectAfterUndoInstantUpdate =
                (_b = this.elementKeysToMultiselectAfterUndoInstantUpdate) === null || _b === void 0 ? void 0 : _b.map((key) => {
                    const newKey = getNewKey(key);
                    return newKey || key;
                });
        }
        this.applyCodebaseIdChanges(prevIdToNewIdMap);
    }
}
exports.ChangeLedgerItem = ChangeLedgerItem;
class StylingChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.STYLING, 'Styling', changeFields, id);
        // Allow instant updates while flushing
        this.canInstantUpdateWhileFlushing = true;
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseId, stylingChanges, stylingFramework, modifiers, customProperties, } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/styling`,
            body: {
                reactElement: treeElementLookup[codebaseId],
                styling: stylingChanges,
                stylingFramework,
                modifiers,
                customProperties,
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        const newCodebaseId = prevIdToNewIdMap[this.changeFields.codebaseId];
        if (newCodebaseId) {
            this.changeFields.codebaseId = newCodebaseId;
        }
    }
}
exports.StylingChange = StylingChange;
class AddJsxChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.ADD_JSX, 'Add Element', changeFields, id);
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdToAddTo, beforeCodebaseId, afterCodebaseId, addCodebaseId, addNativeTag, fileContentsToSourceFrom, fileContentsSourceFilename, propsToSet, deletedStoryboardId, htmlForInstantUpdate, extraPathToContents, } = this.changeFields;
        const body = {
            destinationElement: treeElementLookup[codebaseIdToAddTo],
            beforeElement: treeElementLookup[beforeCodebaseId || ''],
            afterElement: treeElementLookup[afterCodebaseId || ''],
            newElement: {},
            canvasId,
            deletedStoryboardId,
            fileContentsToSourceFrom,
            fileContentsSourceFilename,
            extraFiles: extraPathToContents,
        };
        if (addCodebaseId) {
            body.newElement = Object.assign({}, treeElementLookup[addCodebaseId]);
        }
        else if (addNativeTag) {
            body.newElement['type'] = 'native';
            body.newElement['nativeTag'] = addNativeTag;
            body.newElement['componentName'] = addNativeTag;
        }
        if (propsToSet) {
            body.newElement['propsToSet'] = propsToSet;
        }
        if (!Object.keys(body.newElement).length) {
            delete body.newElement;
        }
        const hasInstantUpdate = Boolean(htmlForInstantUpdate);
        body['hasInstantUpdate'] = hasInstantUpdate;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/addJsxElement`,
            body,
            // Only show the success message if we do not have instant updates
            successToastMessage: hasInstantUpdate ? undefined : 'Successfully added',
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        const fieldsToApply = [
            'codebaseIdToAddTo',
            'beforeCodebaseId',
            'afterCodebaseId',
            'addCodebaseId',
        ];
        fieldsToApply.forEach((field) => {
            // @ts-ignore
            const newCodebaseId = prevIdToNewIdMap[this.changeFields[field]];
            if (newCodebaseId) {
                // @ts-ignore
                this.changeFields[field] = newCodebaseId;
            }
        });
    }
}
exports.AddJsxChange = AddJsxChange;
class MoveJsxChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.MOVE_JSX, 'Move Element', changeFields, id);
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdToMoveTo, codebaseIdToMove, afterCodebaseId, beforeCodebaseId, expectedCurrentParentCodebaseId, } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/moveJsxElement`,
            body: {
                elementToMove: treeElementLookup[codebaseIdToMove],
                newContainerElement: treeElementLookup[codebaseIdToMoveTo],
                afterElement: treeElementLookup[afterCodebaseId || ''],
                beforeElement: treeElementLookup[beforeCodebaseId || ''],
                expectedCurrentParent: treeElementLookup[expectedCurrentParentCodebaseId || ''],
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        const fieldsToApply = [
            'codebaseIdToMoveTo',
            'codebaseIdToMove',
            'afterCodebaseId',
            'beforeCodebaseId',
            'expectedCurrentParentCodebaseId',
        ];
        fieldsToApply.forEach((field) => {
            // @ts-ignore
            const newCodebaseId = prevIdToNewIdMap[this.changeFields[field]];
            if (newCodebaseId) {
                // @ts-ignore
                this.changeFields[field] = newCodebaseId;
            }
        });
    }
}
exports.MoveJsxChange = MoveJsxChange;
class RemoveJsxChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        // Deduplicate the codebaseIdsToRemove
        changeFields.codebaseIdsToRemove = Array.from(new Set(changeFields.codebaseIdsToRemove));
        super(ChangeType.REMOVE_JSX, 'Delete Element', changeFields, id);
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdsToRemove } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/removeJsxElement`,
            body: {
                elementsToRemove: codebaseIdsToRemove
                    .map((codebaseId) => treeElementLookup[codebaseId])
                    .filter((element) => element),
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        this.changeFields.codebaseIdsToRemove =
            this.changeFields.codebaseIdsToRemove.map((codebaseId) => {
                const newCodebaseId = prevIdToNewIdMap[codebaseId];
                if (newCodebaseId) {
                    return newCodebaseId;
                }
                return codebaseId;
            });
    }
}
exports.RemoveJsxChange = RemoveJsxChange;
class ChangePropChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.CHANGE_PROP, 'Change Prop', changeFields, id);
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdToChange, propName, propValue } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/changePropValue`,
            body: {
                elementToModify: treeElementLookup[codebaseIdToChange],
                propName,
                propValue,
            },
            successToastMessage: 'Prop changed',
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        const newCodebaseId = prevIdToNewIdMap[this.changeFields.codebaseIdToChange];
        if (newCodebaseId) {
            this.changeFields.codebaseIdToChange = newCodebaseId;
        }
    }
}
exports.ChangePropChange = ChangePropChange;
class WrapDivChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        // Deduplicate the codebaseIdsToWrap
        changeFields.codebaseIdsToWrap = Array.from(new Set(changeFields.codebaseIdsToWrap));
        super(ChangeType.WRAP_DIV, 'Wrap In Div', changeFields, id);
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdsToWrap } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/wrapInDiv`,
            body: {
                reactElements: codebaseIdsToWrap.map((codebaseId) => treeElementLookup[codebaseId]),
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        this.changeFields.codebaseIdsToWrap =
            this.changeFields.codebaseIdsToWrap.map((codebaseId) => {
                const newCodebaseId = prevIdToNewIdMap[codebaseId];
                if (newCodebaseId) {
                    return newCodebaseId;
                }
                return codebaseId;
            });
    }
}
exports.WrapDivChange = WrapDivChange;
class DuplicateChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        // Deduplicate the codebaseIdsToDuplicate
        changeFields.codebaseIdsToDuplicate = Array.from(new Set(changeFields.codebaseIdsToDuplicate));
        super(ChangeType.DUPLICATE, 'Duplicate', changeFields, id);
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdsToDuplicate } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/duplicate`,
            body: {
                reactElements: codebaseIdsToDuplicate.map((codebaseId) => treeElementLookup[codebaseId]),
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        this.changeFields.codebaseIdsToDuplicate =
            this.changeFields.codebaseIdsToDuplicate.map((codebaseId) => {
                const newCodebaseId = prevIdToNewIdMap[codebaseId];
                if (newCodebaseId) {
                    return newCodebaseId;
                }
                return codebaseId;
            });
    }
}
exports.DuplicateChange = DuplicateChange;
class ChangeTagChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.CHANGE_TAG, 'Change Tag Name', changeFields, id);
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdToChange, newTagName } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/changeElementTag`,
            body: {
                elementToModify: treeElementLookup[codebaseIdToChange],
                newTag: newTagName,
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        const newCodebaseId = prevIdToNewIdMap[this.changeFields.codebaseIdToChange];
        if (newCodebaseId) {
            this.changeFields.codebaseIdToChange = newCodebaseId;
        }
    }
}
exports.ChangeTagChange = ChangeTagChange;
class AddClassChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.ADD_CLASS, 'Add Class', changeFields, id);
        this.canInstantUpdateWhileFlushing = true;
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdToAddClass, className, addingTailwindClass, modifiers, customProperties, } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/addClass`,
            body: {
                reactElement: treeElementLookup[codebaseIdToAddClass],
                className,
                stylingFramework: addingTailwindClass
                    ? StylingFramework.TAILWIND
                    : null,
                modifiers,
                customProperties,
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        const newCodebaseId = prevIdToNewIdMap[this.changeFields.codebaseIdToAddClass];
        if (newCodebaseId) {
            this.changeFields.codebaseIdToAddClass = newCodebaseId;
        }
    }
}
exports.AddClassChange = AddClassChange;
class RemoveClassChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.REMOVE_CLASS, 'Remove Class', changeFields, id);
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdToRemoveClass, className } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/removeClass`,
            body: {
                reactElement: treeElementLookup[codebaseIdToRemoveClass],
                className,
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        const newCodebaseId = prevIdToNewIdMap[this.changeFields.codebaseIdToRemoveClass];
        if (newCodebaseId) {
            this.changeFields.codebaseIdToRemoveClass = newCodebaseId;
        }
    }
}
exports.RemoveClassChange = RemoveClassChange;
class EditTextChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.EDIT_TEXT, 'Edit Text', changeFields, id);
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdToEditText, newText, oldText } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/editText`,
            body: {
                element: treeElementLookup[codebaseIdToEditText],
                newText,
                oldText,
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        const newCodebaseId = prevIdToNewIdMap[this.changeFields.codebaseIdToEditText];
        if (newCodebaseId) {
            this.changeFields.codebaseIdToEditText = newCodebaseId;
        }
    }
}
exports.EditTextChange = EditTextChange;
class UndoChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        var _a;
        super(ChangeType.UNDO, 'Undo', changeFields, id);
        if ((_a = changeFields.changeToUndo) === null || _a === void 0 ? void 0 : _a.canInstantUpdateWhileFlushing) {
            this.canInstantUpdateWhileFlushing = true;
        }
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/activities/undoChangeToFiles`,
            body: {},
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        // Do nothing
    }
}
exports.UndoChange = UndoChange;
class RedoChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        var _a;
        super(ChangeType.REDO, 'Redo', changeFields, id);
        if ((_a = changeFields.changeToRedo) === null || _a === void 0 ? void 0 : _a.canInstantUpdateWhileFlushing) {
            this.canInstantUpdateWhileFlushing = true;
        }
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/activities/redoChangeToFiles`,
            body: {},
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        // Do nothing
    }
}
exports.RedoChange = RedoChange;
class UpdateClassesChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.UPDATE_CLASSES, 'Update Classes', changeFields, id);
        this.canInstantUpdateWhileFlushing = true;
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        const { codebaseIdToUpdateClass, newClasses, oldClasses } = this.changeFields;
        return {
            urlPath: `canvases/${canvasId}/parseAndMutate/mutate/updateClasses`,
            body: {
                reactElement: treeElementLookup[codebaseIdToUpdateClass],
                newClasses,
                oldClasses,
            },
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        const newCodebaseId = prevIdToNewIdMap[this.changeFields.codebaseIdToUpdateClass];
        if (newCodebaseId) {
            this.changeFields.codebaseIdToUpdateClass = newCodebaseId;
        }
    }
}
exports.UpdateClassesChange = UpdateClassesChange;
class UnknownChange extends ChangeLedgerItem {
    constructor(changeFields, id) {
        super(ChangeType.UNKNOWN, '', changeFields, id);
        // Do not process unknown changes
        this.markProcessedSucceeded();
        this.doNotSendInstantUpdate();
    }
    prepareApiRequest(canvasId, treeElementLookup) {
        throw Error('Unsupported operation');
        // For typing
        return {
            urlPath: ``,
            body: {},
        };
    }
    applyCodebaseIdChanges(prevIdToNewIdMap) {
        // Do nothing
    }
}
exports.UnknownChange = UnknownChange;
/**
 * When serializing a change ledger item to a plain JS object, the class functions
 * are lost. This recreates the change item that was lost
 */
const reconstructChangeLedgerClass = (plainJsObject) => {
    if (!plainJsObject || !plainJsObject.type) {
        return null;
    }
    const changeType = plainJsObject.type;
    const changeFields = plainJsObject.changeFields;
    const id = plainJsObject.id;
    const getChangeForType = () => {
        switch (changeType) {
            case ChangeType.STYLING:
                return new StylingChange(changeFields, id);
            case ChangeType.ADD_JSX:
                return new AddJsxChange(changeFields, id);
            case ChangeType.REMOVE_JSX:
                return new RemoveJsxChange(changeFields, id);
            case ChangeType.MOVE_JSX:
                return new MoveJsxChange(changeFields, id);
            case ChangeType.CHANGE_PROP:
                return new ChangePropChange(changeFields, id);
            case ChangeType.ADD_CLASS:
                return new AddClassChange(changeFields, id);
            case ChangeType.REMOVE_CLASS:
                return new RemoveClassChange(changeFields, id);
            case ChangeType.UPDATE_CLASSES:
                return new UpdateClassesChange(changeFields, id);
            case ChangeType.WRAP_DIV:
                return new WrapDivChange(changeFields, id);
            case ChangeType.CHANGE_TAG:
                return new ChangeTagChange(changeFields, id);
            case ChangeType.DUPLICATE:
                return new DuplicateChange(changeFields, id);
            case ChangeType.EDIT_TEXT:
                return new EditTextChange(changeFields, id);
            case ChangeType.UNDO:
                changeFields.changeToUndo = (0, exports.reconstructChangeLedgerClass)(changeFields.changeToUndo);
                return new UndoChange(changeFields, id);
            case ChangeType.REDO:
                changeFields.changeToRedo = (0, exports.reconstructChangeLedgerClass)(changeFields.changeToRedo);
                return new RedoChange(changeFields, id);
            case ChangeType.UNKNOWN:
                return new UnknownChange(changeFields, id);
            default:
                throw new Error(`Unknown change type: ${changeType}`);
        }
    };
    // Set all the other fields on the change object
    const change = getChangeForType();
    Object.keys(plainJsObject).forEach((key) => {
        if (['type', 'changeFields', 'id'].includes(key)) {
            return;
        }
        // @ts-ignore
        change[key] = plainJsObject[key];
    });
    return change;
};
exports.reconstructChangeLedgerClass = reconstructChangeLedgerClass;
//# sourceMappingURL=data:application/json;base64,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