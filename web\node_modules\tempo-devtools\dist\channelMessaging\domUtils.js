"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sleep = exports.defaultUIUpdateRunner = void 0;
const defaultUIUpdateRunner = (innerScope) => {
    innerScope();
};
exports.defaultUIUpdateRunner = defaultUIUpdateRunner;
/**
 * Generic sleep utility that returns a promise that resolves after the specified delay
 * @param ms Number of milliseconds to sleep
 * @returns Promise that resolves after the delay
 */
const sleep = (ms) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
};
exports.sleep = sleep;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZG9tVXRpbHMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvY2hhbm5lbE1lc3NhZ2luZy9kb21VdGlscy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFFTyxNQUFNLHFCQUFxQixHQUFtQixDQUFDLFVBQVUsRUFBRSxFQUFFO0lBQ2xFLFVBQVUsRUFBRSxDQUFDO0FBQ2YsQ0FBQyxDQUFDO0FBRlcsUUFBQSxxQkFBcUIseUJBRWhDO0FBRUY7Ozs7R0FJRztBQUNJLE1BQU0sS0FBSyxHQUFHLENBQUMsRUFBVSxFQUFpQixFQUFFO0lBQ2pELE9BQU8sSUFBSSxPQUFPLENBQUMsQ0FBQyxPQUFPLEVBQUUsRUFBRSxDQUFDLFVBQVUsQ0FBQyxPQUFPLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQztBQUMzRCxDQUFDLENBQUM7QUFGVyxRQUFBLEtBQUssU0FFaEIiLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdHlwZSBVSVVwZGF0ZVJ1bm5lciA9IChpbm5lclNjb3BlOiAoKSA9PiB2b2lkKSA9PiB2b2lkO1xuXG5leHBvcnQgY29uc3QgZGVmYXVsdFVJVXBkYXRlUnVubmVyOiBVSVVwZGF0ZVJ1bm5lciA9IChpbm5lclNjb3BlKSA9PiB7XG4gIGlubmVyU2NvcGUoKTtcbn07XG5cbi8qKlxuICogR2VuZXJpYyBzbGVlcCB1dGlsaXR5IHRoYXQgcmV0dXJucyBhIHByb21pc2UgdGhhdCByZXNvbHZlcyBhZnRlciB0aGUgc3BlY2lmaWVkIGRlbGF5XG4gKiBAcGFyYW0gbXMgTnVtYmVyIG9mIG1pbGxpc2Vjb25kcyB0byBzbGVlcFxuICogQHJldHVybnMgUHJvbWlzZSB0aGF0IHJlc29sdmVzIGFmdGVyIHRoZSBkZWxheVxuICovXG5leHBvcnQgY29uc3Qgc2xlZXAgPSAobXM6IG51bWJlcik6IFByb21pc2U8dm9pZD4gPT4ge1xuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgbXMpKTtcbn07XG4iXX0=