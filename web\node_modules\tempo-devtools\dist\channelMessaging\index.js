"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initChannelMessaging = void 0;
// KEEP THIS IN SYNC WITH sessionStorageUtils.*.tsx
const resqUtils_1 = require("./resqUtils");
const lz_string_1 = __importDefault(require("lz-string"));
const posthog_js_1 = __importDefault(require("../posthog.js"));
const channelMessagingFunctions_1 = require("./channelMessagingFunctions");
// For vite only, show errors in the browser
if (typeof window !== 'undefined' &&
    window.location.href.includes('framework=VITE')) {
    const showErrorOverlay = (err) => {
        // must be within function call because that's when the element is defined for sure.
        const ErrorOverlay = customElements.get('vite-error-overlay');
        // don't open outside vite environment
        if (!ErrorOverlay) {
            return;
        }
        const overlay = new ErrorOverlay(err);
        document.body.appendChild(overlay);
    };
    window.addEventListener('error', showErrorOverlay);
    window.addEventListener('unhandledrejection', ({ reason }) => showErrorOverlay(reason));
}
const debugLogInDev = (...str) => {
    var _a;
    // Only in dev
    if ((_a = window.location.search) === null || _a === void 0 ? void 0 : _a.includes('debugLog=true')) {
        console.debug(...str);
    }
};
function initChannelMessaging() {
    var _a;
    if (typeof window !== 'undefined') {
        (0, channelMessagingFunctions_1.initChannelMessagingFunctions)();
        // Only in prod
        if (window.location.hostname.endsWith('dev.tempolabs.ai') &&
            !window.location.hostname.endsWith('staging-dev.tempolabs.ai')) {
            if (posthog_js_1.default) {
                posthog_js_1.default.init();
            }
        }
    }
    if (typeof window !== 'undefined') {
        window.addEventListener('message', (event) => {
            const { data } = event;
            if (data.type === 'GET_STATE_AND_PROPS') {
                const { componentName } = data;
                // TODO: Support custom root that user provides
                let rootSelector = '#root';
                if (!document.querySelector(rootSelector)) {
                    rootSelector = '#__next';
                }
                const rootReactElement = (0, resqUtils_1.getRootReactElement)();
                const tree = (0, resqUtils_1.buildNodeTree)(rootReactElement, null);
                const foundNodes = (0, resqUtils_1.findElementInTree)(tree, (node) => {
                    if (componentName && node.name == componentName) {
                        return true;
                    }
                    return false;
                });
                if (!(foundNodes === null || foundNodes === void 0 ? void 0 : foundNodes.length)) {
                    const message = {
                        error: 'No component found',
                    };
                    console.log('STATE_AND_PROPS ', JSON.stringify(message));
                    return;
                }
                if (foundNodes.length > 1) {
                    console.log(foundNodes);
                    console.log('Warning: more than 1 component found');
                }
                const foundNode = foundNodes[0];
                const sendDataForNode = (node) => {
                    debugLogInDev('NODE FOUND: ', node);
                    const PROPS_TO_EXCLUDE = {
                        tempoelementid: true,
                        'data-testid': true,
                    };
                    const propsToSend = {};
                    if (node.props) {
                        Object.keys(node.props).forEach((key) => {
                            if (!PROPS_TO_EXCLUDE[key]) {
                                if (typeof node.props[key] === 'object') {
                                    propsToSend[key] = 'TEMPO_OBJECT_TYPE';
                                }
                                else if (typeof node.props[key] === 'function') {
                                    propsToSend[key] = 'TEMPO_FUNCTION_TYPE';
                                }
                                else {
                                    propsToSend[key] = node.props[key];
                                }
                            }
                        });
                    }
                    // TODO: This doesn't fully work because of this bug: https://github.com/baruchvlz/resq/issues/85
                    let stateToSend = {};
                    if (node.state) {
                        if (typeof node.state === 'string') {
                            stateToSend = {
                                state: node.state,
                            };
                        }
                        else {
                            Object.keys(node.state).forEach((key) => {
                                if (typeof node.state[key] === 'object') {
                                    stateToSend[key] = 'TEMPO_OBJECT_TYPE';
                                }
                                else if (typeof node.state[key] === 'function') {
                                    stateToSend[key] = 'TEMPO_FUNCTION_TYPE';
                                }
                                else {
                                    stateToSend[key] = node.state[key];
                                }
                            });
                        }
                    }
                    const message = {
                        id: data.id,
                        props: propsToSend,
                        state: stateToSend,
                    };
                    console.log('STATE_AND_PROPS ', JSON.stringify(message));
                };
                sendDataForNode(foundNode);
            }
        });
    }
    if (typeof window !== 'undefined') {
        if ((_a = window.location.search) === null || _a === void 0 ? void 0 : _a.includes('storyboard=true')) {
            let rootEl = document.getElementById('root');
            if (!rootEl) {
                rootEl = document.getElementById('__next');
            }
            if (rootEl) {
                if (window.location.search.includes('type=STORY') ||
                    window.location.search.includes('type=COMPONENT')) {
                    [rootEl, document.body, document.documentElement].forEach((el) => {
                        el.style.backgroundColor = 'transparent';
                        el.style.width = '100vw';
                        el.style.height = '100vh';
                        el.style.overflow = 'hidden';
                    });
                }
                else {
                    rootEl.style.width = '100vw';
                    rootEl.style.height = '100vh';
                }
            }
        }
        (function () {
            let port2 = null;
            let storyboardId = null;
            // Setup the transfered port
            const initPort = (e) => {
                if (e.data === 'init') {
                    port2 = e.ports[0];
                    port2.onmessage = onMessage;
                }
                else {
                    var msgObj = e.data;
                    onMessage({
                        data: msgObj,
                    });
                }
            };
            // Listen for the intial port transfer message
            window.addEventListener('message', initPort);
            const onInspectElement = (data) => __awaiter(this, void 0, void 0, function* () {
                if (!data.payload.componentName) {
                    console.log('NO COMPONENT NAME');
                    const message = {
                        id: data.id,
                        error: 'No component name',
                    };
                    port2 === null || port2 === void 0 ? void 0 : port2.postMessage(message);
                    return;
                }
                // TODO: Support custom root that user provides
                const rootReactElement = (0, resqUtils_1.getRootReactElement)();
                const tree = (0, resqUtils_1.buildNodeTree)(rootReactElement, null);
                const { isComponent, componentName, tempoElementID, componentElementId, } = data.payload;
                if (!isComponent && !tempoElementID) {
                    console.log('No tempo element ID provided');
                    const message = {
                        id: data.id,
                        error: 'Could not find element',
                    };
                    port2 === null || port2 === void 0 ? void 0 : port2.postMessage(message);
                    return;
                }
                if (isComponent && !tempoElementID && !componentName) {
                    console.log('No tempo element ID or component name provided');
                    const message = {
                        id: data.id,
                        error: 'Could not find component',
                    };
                    port2 === null || port2 === void 0 ? void 0 : port2.postMessage(message);
                    return;
                }
                const foundNodes = (0, resqUtils_1.findElementInTree)(tree, (node) => {
                    var _a, _b, _c, _d, _e, _f;
                    if (isComponent) {
                        // Check tempoElementID, and if it's not provided, use the component name
                        if (tempoElementID &&
                            (((_a = node.props) === null || _a === void 0 ? void 0 : _a.tempoelementid) == tempoElementID ||
                                ((_b = node.props) === null || _b === void 0 ? void 0 : _b['data-testid']) == tempoElementID)) {
                            return true;
                        }
                        if (!tempoElementID &&
                            componentName &&
                            node.name == componentName) {
                            return true;
                        }
                    }
                    else {
                        // The tempo element ID must always match
                        if (tempoElementID &&
                            ((_c = node.props) === null || _c === void 0 ? void 0 : _c.tempoelementid) !== tempoElementID &&
                            ((_d = node.props) === null || _d === void 0 ? void 0 : _d['data-testid']) !== tempoElementID) {
                            return false;
                        }
                        // If the component instance ID is provided, go up the chain to check if there are any parents with this component instance ID set
                        if (componentElementId) {
                            let nodeToCheck = node.parent;
                            let foundMatchingComponent = false;
                            while (nodeToCheck) {
                                if (((_e = nodeToCheck.props) === null || _e === void 0 ? void 0 : _e.tempoelementid) === componentElementId ||
                                    ((_f = nodeToCheck.props) === null || _f === void 0 ? void 0 : _f['data-testid']) === componentElementId) {
                                    foundMatchingComponent = true;
                                    break;
                                }
                                nodeToCheck = nodeToCheck.parent;
                            }
                            if (!foundMatchingComponent) {
                                return false;
                            }
                        }
                        return true;
                    }
                    return false;
                });
                if (!(foundNodes === null || foundNodes === void 0 ? void 0 : foundNodes.length)) {
                    debugLogInDev('NO COMPONENT FOUND');
                    const message = {
                        id: data.id,
                        error: 'No component found',
                    };
                    port2 === null || port2 === void 0 ? void 0 : port2.postMessage(message);
                    return;
                }
                if (foundNodes.length > 1) {
                    console.log(foundNodes);
                    console.log('Warning: more than 1 component found');
                }
                const foundNode = foundNodes[0];
                const sendDataForNode = (node) => {
                    debugLogInDev('NODE FOUND: ', node);
                    const propsToSend = {};
                    if (node.props) {
                        Object.keys(node.props).forEach((key) => {
                            if (typeof node.props[key] === 'object') {
                                propsToSend[key] = 'TEMPO_OBJECT_TYPE';
                            }
                            else if (typeof node.props[key] === 'function') {
                                propsToSend[key] = 'TEMPO_FUNCTION_TYPE';
                            }
                            else {
                                propsToSend[key] = node.props[key];
                            }
                        });
                    }
                    // TODO: This doesn't fully work because of this bug: https://github.com/baruchvlz/resq/issues/85
                    let stateToSend = {};
                    if (node.state) {
                        if (typeof node.state === 'string') {
                            stateToSend = {
                                state: node.state,
                            };
                        }
                        else {
                            Object.keys(node.state).forEach((key) => {
                                if (typeof node.state[key] === 'object') {
                                    stateToSend[key] = 'TEMPO_OBJECT_TYPE';
                                }
                                else if (typeof node.state[key] === 'function') {
                                    stateToSend[key] = 'TEMPO_FUNCTION_TYPE';
                                }
                                else {
                                    stateToSend[key] = node.state[key];
                                }
                            });
                        }
                    }
                    const message = {
                        id: data.id,
                        props: propsToSend,
                        state: stateToSend,
                    };
                    debugLogInDev('RESPONDING WITH: ', message);
                    port2 === null || port2 === void 0 ? void 0 : port2.postMessage(message);
                };
                sendDataForNode(foundNode);
            });
            // Handle messages received on port2
            const onMessage = (e) => __awaiter(this, void 0, void 0, function* () {
                var _a, _b;
                try {
                    const data = e.data;
                    const dataToLog = Object.assign({}, data);
                    if ((_a = data === null || data === void 0 ? void 0 : data.payload) === null || _a === void 0 ? void 0 : _a.compressedArgs) {
                        dataToLog.payload = Object.assign(Object.assign({}, data.payload), { compressedArgs: 'COMPRESSED' });
                    }
                    // These contain args that are too large to log
                    const LOGS_TO_SKIP_ARGS = [
                        'initProject',
                        'setNewLookups',
                        'processRulesForSelectedElement',
                    ];
                    if (((_b = data === null || data === void 0 ? void 0 : data.payload) === null || _b === void 0 ? void 0 : _b.functionName) &&
                        LOGS_TO_SKIP_ARGS.includes(data.payload.functionName)) {
                        dataToLog.payload = Object.assign(Object.assign({}, data.payload), { args: 'ARGS_SKIPPED' });
                    }
                    debugLogInDev('INNER FRAME: Received message from parent: ', JSON.stringify(dataToLog));
                    if (!data || !data.payload) {
                        debugLogInDev('NO PAYLOAD');
                        return;
                    }
                    if (!data.id) {
                        debugLogInDev('NO ID');
                        return;
                    }
                    if (data.type === 'inspectElement') {
                        onInspectElement(data);
                    }
                    else if (data.type === 'executeFunction') {
                        const fn = window[data.payload.functionName];
                        if (typeof fn === 'function') {
                            // Special case to register the storyboardId
                            let args = data.payload.args;
                            if (data.payload.compressedArgs) {
                                args = JSON.parse(lz_string_1.default.decompress(data.payload.compressedArgs));
                            }
                            if (data.payload.functionName === 'initProject') {
                                storyboardId = args[0];
                                args = args.slice(1);
                            }
                            if (data.payload.args) {
                                // @ts-ignore
                                const result = fn(port2, storyboardId, ...args);
                                if (result instanceof Promise) {
                                    yield result;
                                }
                            }
                            else {
                                // @ts-ignore
                                const result = fn(port2, storyboardId);
                                if (result instanceof Promise) {
                                    yield result;
                                }
                            }
                        }
                        else {
                            console.log('INNER FRAME ERROR: Function to execute not found');
                        }
                    }
                }
                catch (error) {
                    console.log('INNER FRAME ERROR: ', error);
                    // TODO: Send error back to parent?
                }
            });
        })();
    }
}
exports.initChannelMessaging = initChannelMessaging;
//# sourceMappingURL=data:application/json;base64,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