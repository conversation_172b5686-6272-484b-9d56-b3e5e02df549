{"version": 3, "sources": ["../../@vidstack/react/dev/chunks/vidstack-krOAtKMi.js"], "sourcesContent": ["\"use client\"\n\nconst videoIdRE = /(?:https:\\/\\/)?(?:player\\.)?vimeo(?:\\.com)?\\/(?:video\\/)?(\\d+)(?:(?:\\?hash=|\\?h=|\\/)(.*))?/;\nconst infoCache = /* @__PURE__ */ new Map();\nconst pendingFetch = /* @__PURE__ */ new Map();\nfunction resolveVimeoVideoId(src) {\n  const matches = src.match(videoIdRE);\n  return { videoId: matches?.[1], hash: matches?.[2] };\n}\nasync function getVimeoVideoInfo(videoId, abort, videoHash) {\n  if (infoCache.has(videoId)) return infoCache.get(videoId);\n  if (pendingFetch.has(videoId)) return pendingFetch.get(videoId);\n  let oembedSrc = `https://vimeo.com/api/oembed.json?url=https://player.vimeo.com/video/${videoId}`;\n  if (videoHash) {\n    oembedSrc = oembedSrc.concat(`?h=${videoHash}`);\n  }\n  const promise = window.fetch(oembedSrc, {\n    mode: \"cors\",\n    signal: abort.signal\n  }).then((response) => response.json()).then((data) => {\n    const thumnailRegex = /vimeocdn.com\\/video\\/(.*)?_/, thumbnailId = data?.thumbnail_url?.match(thumnailRegex)?.[1], poster = thumbnailId ? `https://i.vimeocdn.com/video/${thumbnailId}_1920x1080.webp` : \"\", info = {\n      title: data?.title ?? \"\",\n      duration: data?.duration ?? 0,\n      poster,\n      pro: data.account_type !== \"basic\"\n    };\n    infoCache.set(videoId, info);\n    return info;\n  }).finally(() => pendingFetch.delete(videoId));\n  pendingFetch.set(videoId, promise);\n  return promise;\n}\n\nexport { getVimeoVideoInfo, resolveVimeoVideoId };\n"], "mappings": ";AAEA,IAAM,YAAY;AAClB,IAAM,YAA4B,oBAAI,IAAI;AAC1C,IAAM,eAA+B,oBAAI,IAAI;AAC7C,SAAS,oBAAoB,KAAK;AAChC,QAAM,UAAU,IAAI,MAAM,SAAS;AACnC,SAAO,EAAE,SAAS,mCAAU,IAAI,MAAM,mCAAU,GAAG;AACrD;AACA,eAAe,kBAAkB,SAAS,OAAO,WAAW;AAC1D,MAAI,UAAU,IAAI,OAAO,EAAG,QAAO,UAAU,IAAI,OAAO;AACxD,MAAI,aAAa,IAAI,OAAO,EAAG,QAAO,aAAa,IAAI,OAAO;AAC9D,MAAI,YAAY,wEAAwE,OAAO;AAC/F,MAAI,WAAW;AACb,gBAAY,UAAU,OAAO,MAAM,SAAS,EAAE;AAAA,EAChD;AACA,QAAM,UAAU,OAAO,MAAM,WAAW;AAAA,IACtC,MAAM;AAAA,IACN,QAAQ,MAAM;AAAA,EAChB,CAAC,EAAE,KAAK,CAAC,aAAa,SAAS,KAAK,CAAC,EAAE,KAAK,CAAC,SAAS;AAnBxD;AAoBI,UAAM,gBAAgB,+BAA+B,eAAc,wCAAM,kBAAN,mBAAqB,MAAM,mBAA3B,mBAA4C,IAAI,SAAS,cAAc,gCAAgC,WAAW,oBAAoB,IAAI,OAAO;AAAA,MAClN,QAAO,6BAAM,UAAS;AAAA,MACtB,WAAU,6BAAM,aAAY;AAAA,MAC5B;AAAA,MACA,KAAK,KAAK,iBAAiB;AAAA,IAC7B;AACA,cAAU,IAAI,SAAS,IAAI;AAC3B,WAAO;AAAA,EACT,CAAC,EAAE,QAAQ,MAAM,aAAa,OAAO,OAAO,CAAC;AAC7C,eAAa,IAAI,SAAS,OAAO;AACjC,SAAO;AACT;", "names": []}