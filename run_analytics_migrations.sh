#!/bin/bash

# Script to run the analytics database migrations

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
    echo -e "\n${YELLOW}==== $1 ====${NC}\n"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# Database connection parameters
# Replace these with your actual database connection details
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="streamscale"
DB_USER="postgres"
DB_PASSWORD="postgres"

# Path to migration files
MIGRATIONS_DIR="./cmd/migrations"

print_header "Running analytics database migrations"

# Check if migrate tool is installed
if ! command -v migrate &> /dev/null; then
    print_error "migrate command not found. Please install golang-migrate."
    echo "You can install it with: go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest"
    exit 1
fi

# Create database URL
DB_URL="postgres://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME?sslmode=disable"

# Run the migrations
print_header "Running migrations"
migrate -path $MIGRATIONS_DIR -database $DB_URL up

if [ $? -eq 0 ]; then
    print_success "Migrations completed successfully"
else
    print_error "Migration failed"
    exit 1
fi

print_header "Verifying analytics tables"

# Verify that the analytics tables were created
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "\dt video_views"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "\dt video_watch_sessions"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "\dt video_engagement"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "\d video_performance"

print_header "Migration process completed"
