# StreamScale Auto-Scaling Feature

StreamScale now includes an auto-scaling feature for worker servers. This feature automatically scales worker servers up when there are jobs to process and scales them down when there are no jobs, optimizing resource usage and reducing costs.

## How It Works

The auto-scaling system works differently depending on your deployment environment:

### Kubernetes Deployment

In a Kubernetes environment, the auto-scaling is handled by the Kubernetes Horizontal Pod Autoscaler (HPA) based on custom metrics from the Redis job queue:

1. A metrics exporter service monitors the Redis queue length and exposes it as a Prometheus metric.
2. The Prometheus Adapter makes this metric available to Kubernetes as a custom metric.
3. The HPA watches this metric and scales the worker deployment up or down based on the queue length.
4. When jobs are added to the queue, the HPA detects the increase in queue length and scales up worker pods.
5. When all jobs are processed and the queue is empty, the HPA scales down the worker pods to zero after a stabilization period.

### Docker Compose Deployment

For local development or simpler deployments using Docker Compose, we provide a custom autoscaler service:

1. The metrics exporter service monitors the Redis queue length and exposes it as a Prometheus metric.
2. The autoscaler service periodically checks the queue length and scales the worker service up or down using Docker Compose commands.
3. When jobs are added to the queue, the autoscaler increases the number of worker containers.
4. When the queue is empty, the autoscaler reduces the number of worker containers after a cooldown period.

## Deployment

### Kubernetes Deployment

To deploy the auto-scaling system on Kubernetes:

1. Make sure you have a Kubernetes cluster with the Prometheus Operator and Prometheus Adapter installed.
2. Copy the `.env.example` file to `.env.dev` (or another environment name) and fill in the required values.
3. Run the deployment script:

```bash
./scripts/deploy.sh
```

For more detailed information, see the [AUTO_SCALING.md](AUTO_SCALING.md) document.

### Docker Compose Deployment

To deploy the auto-scaling system using Docker Compose:

1. Make sure you have Docker and Docker Compose installed.
2. Create a `.env` file with the required environment variables (see `.env.example`).
3. Run Docker Compose:

```bash
docker-compose up -d
```

## Configuration

The auto-scaling behavior can be configured through environment variables:

- `MIN_REPLICAS`: Minimum number of worker instances (default: 0).
- `MAX_REPLICAS`: Maximum number of worker instances (default: 10).
- `QUEUE_THRESHOLD`: Number of jobs in the queue that triggers scaling up (default: 1).
- `SCALE_UP_COOLDOWN`: Time in seconds to wait between scaling up operations (default: 10).
- `SCALE_DOWN_COOLDOWN`: Time in seconds to wait before scaling down (default: 300).

For Kubernetes deployments, these settings are configured in the `k8s/worker-hpa.yaml` file.

## Monitoring

You can monitor the auto-scaling behavior using the following methods:

### Kubernetes

```bash
# Check the HPA status
kubectl get hpa streamscale-worker-hpa

# Check the worker deployment
kubectl get deployment streamscale-worker

# Check the metrics exporter logs
kubectl logs -l app=streamscale,component=metrics-exporter
```

### Docker Compose

```bash
# Check the autoscaler logs
docker-compose logs -f autoscaler

# Check the metrics exporter logs
docker-compose logs -f metrics-exporter

# Check the current number of worker containers
docker-compose ps worker
```

## Troubleshooting

If the auto-scaling is not working as expected, check the following:

1. Make sure the metrics exporter can connect to Redis and is reporting metrics correctly.
2. Verify that the autoscaler or HPA is correctly configured.
3. Check the logs for any error messages.
4. Ensure that the worker service/deployment is correctly configured and can be scaled.
