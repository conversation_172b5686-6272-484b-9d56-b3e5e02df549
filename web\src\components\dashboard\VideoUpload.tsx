import { useState, useCallback, useRef, memo } from "react";
import { useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { videoApi } from "@/lib/videoApi";
import { useCreateJob } from "@/hooks/useVideos";
import {
  Upload,
  X,
  Check,
  FileVideo,
  AlertCircle,
  ArrowLeft,
  Film,
  Clock,
  Zap,
  Shield,
  Info,
  Settings,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  VideoUploadSettings,
  VideoUploadSettings as IVideoUploadSettings,
} from "./VideoUploadSettings";
type UploadStatus = "idle" | "uploading" | "in_progress" | "complete" | "error";
const VideoUpload = memo(() => {
  const navigate = useNavigate();
  const [file, setFile] = useState<File | null>(null);
  const [duration, setDuration] = useState<number>(0);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [status, setStatus] = useState<UploadStatus>("idle");
  const [error, setError] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [uploadSettings, setUploadSettings] =
    useState<IVideoUploadSettings | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const createJobMutation = useCreateJob();
  const getVideoDuration = (file: File): Promise<number> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement("video");
      video.preload = "metadata";
      video.onloadedmetadata = () => {
        window.URL.revokeObjectURL(video.src);
        const durationInSeconds = Math.round(video.duration);
        resolve(durationInSeconds);
      };
      video.onerror = () => {
        reject("Error loading video file");
      };
      video.src = URL.createObjectURL(file);
    });
  };
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);
  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile && droppedFile.type.startsWith("video/")) {
      try {
        const videoDuration = await getVideoDuration(droppedFile);
        setDuration(videoDuration);
        setFile(droppedFile);
        setError(null);
      } catch (err) {
        setError("Error reading video file");
      }
    } else {
      setError("Please upload a valid video file");
    }
  }, []);
  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type.startsWith("video/")) {
        try {
          const videoDuration = await getVideoDuration(selectedFile);
          setDuration(videoDuration);
          setFile(selectedFile);
          setError(null);
        } catch (err) {
          setError("Error reading video file");
        }
      } else {
        setError("Please upload a valid video file");
      }
    }
  };
  const handleChooseVideo = () => {
    fileInputRef.current?.click();
  };
  const handleSettingsSubmit = (settings: IVideoUploadSettings) => {
    setUploadSettings(settings);
  };
  const handleUpload = async () => {
    if (!file) return;
    try {
      setStatus("uploading");
      setError(null);
      const presignedData = await videoApi.getUploadUrl({
        name: file.name,
        mime_type: file.type,
        size: file.size,
      });
      const xhr = new XMLHttpRequest();
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100;
          setUploadProgress(progress);
        }
      };
      xhr.onload = async () => {
        if (xhr.status === 200) {
          setStatus("in_progress");
          const jobParams = {
            filename: file.name,
            file_size: file.size,
            duration: duration,
            format: file.type.split("/")[1],
            codec: uploadSettings?.codec || "h264",
            qualities: uploadSettings?.qualities || [
              {
                resolution: "1080p",
                bitrate: 5000000,
              },
            ],
            output_formats: uploadSettings?.outputFormats || ["hls"],
            enable_per_title_encoding:
              uploadSettings?.enablePerTitleEncoding || false,
          };
          createJobMutation.mutate(jobParams, {
            onSuccess: () => {
              setStatus("complete");
              setTimeout(() => {
                navigate("/dashboard");
              }, 2000);
            },
            onError: (error) => {
              setStatus("error");
              setError(
                error instanceof Error
                  ? error.message
                  : "Failed to create transcoding job"
              );
            },
          });
        } else {
          setStatus("error");
          setError(`Upload failed with status: ${xhr.status}`);
        }
      };
      xhr.onerror = (error) => {
        console.error("XHR Error:", error);
        setStatus("error");
        setError("Network error occurred during upload");
      };
      xhr.onabort = () => {
        setStatus("error");
        setError("Upload was aborted");
      };
      if (!presignedData.presignUrl) {
        throw new Error("No upload URL provided");
      }
      xhr.open("PUT", presignedData.presignUrl, true);
      xhr.setRequestHeader("Content-Type", file.type);
      xhr.withCredentials = false;
      xhr.send(file);
    } catch (err) {
      console.error("Upload error:", err);
      setStatus("error");
      setError(
        err instanceof Error ? err.message : "An error occurred during upload"
      );
    }
  };
  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight mb-2 flex items-center gap-3">
            <span className="text-white">Upload</span>
            <span className="text-indigo-400">New Video</span>
          </h1>
          <p className="text-gray-400 text-sm">
            Upload and process your video in one go
          </p>
        </div>
        <Button
          variant="link"
          onClick={() => navigate("/dashboard")}
          className="text-gray-400 hover:text-white flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Dashboard
        </Button>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {}
        <div className="lg:col-span-2">
          {error && (
            <Alert
              variant="destructive"
              className="mb-6 bg-red-500/10 text-red-400 border-red-500/20"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <Card className="bg-gradient-to-b from-gray-900 to-gray-950 border border-dashed border-gray-800 overflow-hidden shadow-xl">
            {status === "idle" && !file && (
              <div
                className={`relative transition-colors ${
                  isDragging
                    ? "bg-indigo-500/5 border-indigo-400"
                    : "hover:border-indigo-500/50"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <div className="p-12 text-center">
                  <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-br from-indigo-500/20 to-violet-500/20 flex items-center justify-center border border-indigo-500/20">
                    <Upload className="w-8 h-8 text-indigo-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    Drop your video here
                  </h3>
                  <p className="text-gray-400 mb-6">
                    or click to browse your files
                  </p>
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white"
                  >
                    Choose File
                  </Button>
                </div>
              </div>
            )}
          </Card>
        </div>
        <div className="space-y-6">
          <Card className="bg-gradient-to-b from-gray-900 to-gray-950 border-gray-800 p-6 shadow-xl">
            <h3 className="text-base font-medium text-white mb-4 flex items-center gap-2">
              <Info className="w-4 h-4 text-indigo-400" />
              Upload Guidelines
            </h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 rounded-full bg-indigo-500/10 flex items-center justify-center flex-shrink-0 border border-indigo-500/20">
                  <Check className="w-3 h-3 text-indigo-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-white">
                    Supported Formats
                  </p>
                  <p className="text-xs text-gray-400">
                    MP4, MOV, AVI, MKV (H.264/H.265)
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 rounded-full bg-indigo-500/10 flex items-center justify-center flex-shrink-0 border border-indigo-500/20">
                  <Check className="w-3 h-3 text-indigo-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-white">
                    Maximum File Size
                  </p>
                  <p className="text-xs text-gray-400">Up to 2GB per video</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 rounded-full bg-indigo-500/10 flex items-center justify-center flex-shrink-0 border border-indigo-500/20">
                  <Check className="w-3 h-3 text-indigo-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-white">Resolution</p>
                  <p className="text-xs text-gray-400">Up to 4K (3840×2160)</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 rounded-full bg-indigo-500/10 flex items-center justify-center flex-shrink-0 border border-indigo-500/20">
                  <Check className="w-3 h-3 text-indigo-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-white">
                    Processing Time
                  </p>
                  <p className="text-xs text-gray-400">
                    2-5 minutes for most videos
                  </p>
                </div>
              </div>
            </div>
          </Card>
          <Card className="bg-indigo-500/5 backdrop-blur-xl border-indigo-500/10 p-6 shadow-xl">
            <h3 className="text-base font-medium text-white mb-4 flex items-center gap-2">
              <Zap className="w-4 h-4 text-indigo-400" />
              Processing Features
            </h3>
            <ul className="space-y-3">
              <li className="flex items-center gap-2 text-sm text-gray-300">
                <div className="w-1 h-1 rounded-full bg-indigo-400"></div>
                Adaptive bitrate streaming
              </li>
              <li className="flex items-center gap-2 text-sm text-gray-300">
                <div className="w-1 h-1 rounded-full bg-indigo-400"></div>
                Multiple resolution outputs
              </li>
              <li className="flex items-center gap-2 text-sm text-gray-300">
                <div className="w-1 h-1 rounded-full bg-indigo-400"></div>
                Thumbnail generation
              </li>
              <li className="flex items-center gap-2 text-sm text-gray-300">
                <div className="w-1 h-1 rounded-full bg-indigo-400"></div>
                Advanced codec options
              </li>
            </ul>
          </Card>
        </div>
      </div>
      <VideoUploadSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onSubmit={handleSettingsSubmit}
        defaultSettings={uploadSettings || undefined}
      />
    </div>
  );
});
VideoUpload.displayName = "VideoUpload";
export default VideoUpload;
