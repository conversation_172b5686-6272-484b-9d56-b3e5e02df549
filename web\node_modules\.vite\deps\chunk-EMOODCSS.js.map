{"version": 3, "sources": ["../../@vidstack/react/dev/chunks/vidstack-CFWvJYI8.js"], "sourcesContent": ["\"use client\"\n\nimport { appendParamsToURL, IS_SERVER } from './vidstack-C-WrcxmD.js';\nimport { signal, listenEvent, effect, peek, isString } from './vidstack-CH225ns1.js';\n\nclass EmbedProvider {\n  #iframe;\n  src = signal(\"\");\n  /**\n   * Defines which referrer is sent when fetching the resource.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLIFrameElement/referrerPolicy}\n   */\n  referrerPolicy = null;\n  get iframe() {\n    return this.#iframe;\n  }\n  constructor(iframe) {\n    this.#iframe = iframe;\n    iframe.setAttribute(\"frameBorder\", \"0\");\n    iframe.setAttribute(\"aria-hidden\", \"true\");\n    iframe.setAttribute(\n      \"allow\",\n      \"autoplay; fullscreen; encrypted-media; picture-in-picture; accelerometer; gyroscope\"\n    );\n    if (this.referrerPolicy !== null) {\n      iframe.setAttribute(\"referrerpolicy\", this.referrerPolicy);\n    }\n  }\n  setup() {\n    listenEvent(window, \"message\", this.#onWindowMessage.bind(this));\n    listenEvent(this.#iframe, \"load\", this.onLoad.bind(this));\n    effect(this.#watchSrc.bind(this));\n  }\n  #watchSrc() {\n    const src = this.src();\n    if (!src.length) {\n      this.#iframe.setAttribute(\"src\", \"\");\n      return;\n    }\n    const params = peek(() => this.buildParams());\n    this.#iframe.setAttribute(\"src\", appendParamsToURL(src, params));\n  }\n  postMessage(message, target) {\n    if (IS_SERVER) return;\n    this.#iframe.contentWindow?.postMessage(JSON.stringify(message), target ?? \"*\");\n  }\n  #onWindowMessage(event) {\n    const origin = this.getOrigin(), isOriginMatch = (event.source === null || event.source === this.#iframe?.contentWindow) && (!isString(origin) || origin === event.origin);\n    if (!isOriginMatch) return;\n    try {\n      const message = JSON.parse(event.data);\n      if (message) this.onMessage(message, event);\n      return;\n    } catch (e) {\n    }\n    if (event.data) this.onMessage(event.data, event);\n  }\n}\n\nexport { EmbedProvider };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAKA,IAAM,gBAAN,MAAoB;AAAA,EAYlB,YAAY,QAAQ;AAZtB;AACE;AACA,+BAAM,OAAO,EAAE;AAMf;AAAA;AAAA;AAAA;AAAA;AAAA,0CAAiB;AAKf,uBAAK,SAAU;AACf,WAAO,aAAa,eAAe,GAAG;AACtC,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,MAAM;AAChC,aAAO,aAAa,kBAAkB,KAAK,cAAc;AAAA,IAC3D;AAAA,EACF;AAAA,EAdA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAaA,QAAQ;AACN,gBAAY,QAAQ,WAAW,sBAAK,8CAAiB,KAAK,IAAI,CAAC;AAC/D,gBAAY,mBAAK,UAAS,QAAQ,KAAK,OAAO,KAAK,IAAI,CAAC;AACxD,WAAO,sBAAK,uCAAU,KAAK,IAAI,CAAC;AAAA,EAClC;AAAA,EAUA,YAAY,SAAS,QAAQ;AA3C/B;AA4CI,QAAI,UAAW;AACf,6BAAK,SAAQ,kBAAb,mBAA4B,YAAY,KAAK,UAAU,OAAO,GAAG,UAAU;AAAA,EAC7E;AAYF;AApDE;AADF;AA6BE,cAAS,WAAG;AACV,QAAM,MAAM,KAAK,IAAI;AACrB,MAAI,CAAC,IAAI,QAAQ;AACf,uBAAK,SAAQ,aAAa,OAAO,EAAE;AACnC;AAAA,EACF;AACA,QAAM,SAAS,KAAK,MAAM,KAAK,YAAY,CAAC;AAC5C,qBAAK,SAAQ,aAAa,OAAO,kBAAkB,KAAK,MAAM,CAAC;AACjE;AAKA,qBAAgB,SAAC,OAAO;AA/C1B;AAgDI,QAAM,SAAS,KAAK,UAAU,GAAG,iBAAiB,MAAM,WAAW,QAAQ,MAAM,aAAW,wBAAK,aAAL,mBAAc,oBAAmB,CAAC,SAAS,MAAM,KAAK,WAAW,MAAM;AACnK,MAAI,CAAC,cAAe;AACpB,MAAI;AACF,UAAM,UAAU,KAAK,MAAM,MAAM,IAAI;AACrC,QAAI,QAAS,MAAK,UAAU,SAAS,KAAK;AAC1C;AAAA,EACF,SAAS,GAAG;AAAA,EACZ;AACA,MAAI,MAAM,KAAM,MAAK,UAAU,MAAM,MAAM,KAAK;AAClD;", "names": []}