"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const PostHog = {
    init: () => {
        !(function (t, e) {
            var o, n, p, r;
            e.__SV ||
                ((window.posthog = e),
                    (e._i = []),
                    (e.init = function (i, s, a) {
                        function g(t, e) {
                            var o = e.split('.');
                            2 == o.length && ((t = t[o[0]]), (e = o[1])),
                                (t[e] = function () {
                                    t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
                                });
                        }
                        ((p = t.createElement('script')).type = 'text/javascript'),
                            (p.async = !0),
                            (p.src = s.api_host + '/static/array.js'),
                            (r = t.getElementsByTagName('script')[0]).parentNode.insertBefore(p, r);
                        var u = e;
                        for (void 0 !== a ? (u = e[a] = []) : (a = 'posthog'),
                            u.people = u.people || [],
                            u.toString = function (t) {
                                var e = 'posthog';
                                return ('posthog' !== a && (e += '.' + a), t || (e += ' (stub)'), e);
                            },
                            u.people.toString = function () {
                                return u.toString(1) + '.people (stub)';
                            },
                            o =
                                'capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep onSessionId'.split(' '),
                            n = 0; n < o.length; n++)
                            g(u, o[n]);
                        e._i.push([i, s, a]);
                    }),
                    (e.__SV = 1));
        })(document, window.posthog || []);
        posthog.init('phc_jjpEvBVV0R2mp44ePAL8Yt4jdtX5HW1lc493rkpUwwa', {
            api_host: 'https://us.i.posthog.com',
            person_profiles: 'identified_only',
            session_recording: {
                recordCrossOriginIframes: true,
                maskAllInputs: false,
                maskInputOptions: {
                    password: true,
                },
            },
        });
    },
};
exports.default = PostHog;
//# sourceMappingURL=data:application/json;base64,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