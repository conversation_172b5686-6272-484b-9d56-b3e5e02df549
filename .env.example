# Redis Configuration
REDIS_ADDR=redis-service:6379
REDIS_PASSWORD=your-redis-password

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY=your-aws-access-key
AWS_SECRET_KEY=your-aws-secret-key

# S3 Configuration
S3_ENDPOINT=https://s3.amazonaws.com
S3_INPUT_BUCKET=streamscale-input
S3_OUTPUT_BUCKET=streamscale-output
S3_CDN_ENDPOINT=https://cdn.streamscale.com

# PostgreSQL Configuration
POSTGRES_HOST=postgres-service
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_DB=streamscale

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key

# Docker Images
WORKER_IMAGE_NAME=streamscale/worker
WORKER_IMAGE_TAG=latest
METRICS_EXPORTER_IMAGE=streamscale/metrics-exporter
METRICS_EXPORTER_TAG=latest
