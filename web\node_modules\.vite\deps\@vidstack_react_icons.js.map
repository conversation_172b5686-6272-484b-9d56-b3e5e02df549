{"version": 3, "sources": ["../../@vidstack/react/prod/vidstack-icons.js", "../../@vidstack/react/prod/chunks/vidstack-CBF7iUqu.js"], "sourcesContent": ["import { accessibilityPaths, addNotePaths, addPlaylistPaths, addUserPaths, addPaths, airplayPaths, arrowCollapseInPaths, arrowCollapsePaths, arrowDownPaths, arrowExpandOutPaths, arrowExpandPaths, arrowLeftPaths, arrowRightPaths, arrowUpPaths, bookmarkPaths, cameraPaths, chaptersPaths, chatCollapsePaths, chatPaths, checkPaths, chevronDownPaths, chevronLeftPaths, chevronRightPaths, chevronUpPaths, chromecastPaths, clipPaths, closedCaptionsOnPaths, closedCaptionsPaths, commentPaths, computerPaths, devicePaths, downloadPaths, episodesPaths, eyePaths, fastBackwardPaths, fastForwardPaths, flagPaths, fullscreenArrowExitPaths, fullscreenArrowPaths, fullscreenExitPaths, fullscreenPaths, heartPaths, infoPaths, languagePaths, linkPaths, lockClosedPaths, lockOpenPaths, menuHorizontalPaths, menuVerticalPaths, microphonePaths, mobilePaths, moonPaths, musicOffPaths, musicPaths, mutePaths, nextPaths, noEyePaths, notificationPaths, odometerPaths, pausePaths, pictureInPictureExitPaths, pictureInPicturePaths, playPaths, playbackSpeedCirclePaths, playlistPaths, previousPaths, questionMarkPaths, queueListPaths, radioButtonSelectedPaths, radioButtonPaths, repeatOnPaths, repeatSquareOnPaths, repeatSquarePaths, repeatPaths, replayPaths, rotatePaths, searchPaths, seekBackward10Paths, seekBackward15Paths, seekBackward30Paths, seekBackwardPaths, seekForward10Paths, seekForward15Paths, seekForward30Paths, seekForwardPaths, sendPaths, settingsMenuPaths, settingsSwitchPaths, settingsPaths, shareArrowPaths, sharePaths, shuffleOnPaths, shufflePaths, stopPaths, subtitlesPaths, sunPaths, theatreModeExitPaths, theatreModePaths, thumbsDownPaths, thumbsUpPaths, timerPaths, transcriptPaths, tvPaths, userPaths, volumeHighPaths, volumeLowPaths, xMarkPaths } from 'media-icons';\nimport { forwardRef, createElement } from 'react';\nimport { Icon } from './chunks/vidstack-CBF7iUqu.js';\n\nconst cn = (className) => className ? `${className} vds-icon` : \"vds-icon\";\nconst AccessibilityIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: accessibilityPaths });\n});\nAccessibilityIcon.displayName = \"VidstackAccessibilityIcon\";\nconst AddNoteIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: addNotePaths });\n});\nAddNoteIcon.displayName = \"VidstackAddNoteIcon\";\nconst AddPlaylistIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: addPlaylistPaths });\n});\nAddPlaylistIcon.displayName = \"VidstackAddPlaylistIcon\";\nconst AddUserIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: addUserPaths });\n});\nAddUserIcon.displayName = \"VidstackAddUserIcon\";\nconst AddIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: addPaths });\n});\nAddIcon.displayName = \"VidstackAddIcon\";\nconst AirPlayIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: airplayPaths });\n});\nAirPlayIcon.displayName = \"VidstackAirPlayIcon\";\nconst ArrowCollapseInIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: arrowCollapseInPaths });\n});\nArrowCollapseInIcon.displayName = \"VidstackArrowCollapseInIcon\";\nconst ArrowCollapseIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: arrowCollapsePaths });\n});\nArrowCollapseIcon.displayName = \"VidstackArrowCollapseIcon\";\nconst ArrowDownIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: arrowDownPaths });\n});\nArrowDownIcon.displayName = \"VidstackArrowDownIcon\";\nconst ArrowExpandOutIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: arrowExpandOutPaths });\n});\nArrowExpandOutIcon.displayName = \"VidstackArrowExpandOutIcon\";\nconst ArrowExpandIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: arrowExpandPaths });\n});\nArrowExpandIcon.displayName = \"VidstackArrowExpandIcon\";\nconst ArrowLeftIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: arrowLeftPaths });\n});\nArrowLeftIcon.displayName = \"VidstackArrowLeftIcon\";\nconst ArrowRightIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: arrowRightPaths });\n});\nArrowRightIcon.displayName = \"VidstackArrowRightIcon\";\nconst ArrowUpIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: arrowUpPaths });\n});\nArrowUpIcon.displayName = \"VidstackArrowUpIcon\";\nconst BookmarkIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: bookmarkPaths });\n});\nBookmarkIcon.displayName = \"VidstackBookmarkIcon\";\nconst CameraIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: cameraPaths });\n});\nCameraIcon.displayName = \"VidstackCameraIcon\";\nconst ChaptersIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: chaptersPaths });\n});\nChaptersIcon.displayName = \"VidstackChaptersIcon\";\nconst ChatCollapseIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: chatCollapsePaths });\n});\nChatCollapseIcon.displayName = \"VidstackChatCollapseIcon\";\nconst ChatIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: chatPaths });\n});\nChatIcon.displayName = \"VidstackChatIcon\";\nconst CheckIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: checkPaths });\n});\nCheckIcon.displayName = \"VidstackCheckIcon\";\nconst ChevronDownIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: chevronDownPaths });\n});\nChevronDownIcon.displayName = \"VidstackChevronDownIcon\";\nconst ChevronLeftIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: chevronLeftPaths });\n});\nChevronLeftIcon.displayName = \"VidstackChevronLeftIcon\";\nconst ChevronRightIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: chevronRightPaths });\n});\nChevronRightIcon.displayName = \"VidstackChevronRightIcon\";\nconst ChevronUpIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: chevronUpPaths });\n});\nChevronUpIcon.displayName = \"VidstackChevronUpIcon\";\nconst ChromecastIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: chromecastPaths });\n});\nChromecastIcon.displayName = \"VidstackChromecastIcon\";\nconst ClipIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: clipPaths });\n});\nClipIcon.displayName = \"VidstackClipIcon\";\nconst ClosedCaptionsOnIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: closedCaptionsOnPaths });\n});\nClosedCaptionsOnIcon.displayName = \"VidstackClosedCaptionsOnIcon\";\nconst ClosedCaptionsIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: closedCaptionsPaths });\n});\nClosedCaptionsIcon.displayName = \"VidstackClosedCaptionsIcon\";\nconst CommentIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: commentPaths });\n});\nCommentIcon.displayName = \"VidstackCommentIcon\";\nconst ComputerIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: computerPaths });\n});\nComputerIcon.displayName = \"VidstackComputerIcon\";\nconst DeviceIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: devicePaths });\n});\nDeviceIcon.displayName = \"VidstackDeviceIcon\";\nconst DownloadIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: downloadPaths });\n});\nDownloadIcon.displayName = \"VidstackDownloadIcon\";\nconst EpisodesIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: episodesPaths });\n});\nEpisodesIcon.displayName = \"VidstackEpisodesIcon\";\nconst EyeIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: eyePaths });\n});\nEyeIcon.displayName = \"VidstackEyeIcon\";\nconst FastBackwardIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: fastBackwardPaths });\n});\nFastBackwardIcon.displayName = \"VidstackFastBackwardIcon\";\nconst FastForwardIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: fastForwardPaths });\n});\nFastForwardIcon.displayName = \"VidstackFastForwardIcon\";\nconst FlagIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: flagPaths });\n});\nFlagIcon.displayName = \"VidstackFlagIcon\";\nconst FullscreenArrowExitIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: fullscreenArrowExitPaths });\n});\nFullscreenArrowExitIcon.displayName = \"VidstackFullscreenArrowExitIcon\";\nconst FullscreenArrowIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: fullscreenArrowPaths });\n});\nFullscreenArrowIcon.displayName = \"VidstackFullscreenArrowIcon\";\nconst FullscreenExitIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: fullscreenExitPaths });\n});\nFullscreenExitIcon.displayName = \"VidstackFullscreenExitIcon\";\nconst FullscreenIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: fullscreenPaths });\n});\nFullscreenIcon.displayName = \"VidstackFullscreenIcon\";\nconst HeartIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: heartPaths });\n});\nHeartIcon.displayName = \"VidstackHeartIcon\";\nconst InfoIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: infoPaths });\n});\nInfoIcon.displayName = \"VidstackInfoIcon\";\nconst LanguageIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: languagePaths });\n});\nLanguageIcon.displayName = \"VidstackLanguageIcon\";\nconst LinkIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: linkPaths });\n});\nLinkIcon.displayName = \"VidstackLinkIcon\";\nconst LockClosedIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: lockClosedPaths });\n});\nLockClosedIcon.displayName = \"VidstackLockClosedIcon\";\nconst LockOpenIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: lockOpenPaths });\n});\nLockOpenIcon.displayName = \"VidstackLockOpenIcon\";\nconst MenuHorizontalIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: menuHorizontalPaths });\n});\nMenuHorizontalIcon.displayName = \"VidstackMenuHorizontalIcon\";\nconst MenuVerticalIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: menuVerticalPaths });\n});\nMenuVerticalIcon.displayName = \"VidstackMenuVerticalIcon\";\nconst MicrophoneIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: microphonePaths });\n});\nMicrophoneIcon.displayName = \"VidstackMicrophoneIcon\";\nconst MobileIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: mobilePaths });\n});\nMobileIcon.displayName = \"VidstackMobileIcon\";\nconst MoonIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: moonPaths });\n});\nMoonIcon.displayName = \"VidstackMoonIcon\";\nconst MusicOffIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: musicOffPaths });\n});\nMusicOffIcon.displayName = \"VidstackMusicOffIcon\";\nconst MusicIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: musicPaths });\n});\nMusicIcon.displayName = \"VidstackMusicIcon\";\nconst MuteIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: mutePaths });\n});\nMuteIcon.displayName = \"VidstackMuteIcon\";\nconst NextIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: nextPaths });\n});\nNextIcon.displayName = \"VidstackNextIcon\";\nconst NoEyeIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: noEyePaths });\n});\nNoEyeIcon.displayName = \"VidstackNoEyeIcon\";\nconst NotificationIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: notificationPaths });\n});\nNotificationIcon.displayName = \"VidstackNotificationIcon\";\nconst OdometerIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: odometerPaths });\n});\nOdometerIcon.displayName = \"VidstackOdometerIcon\";\nconst PauseIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: pausePaths });\n});\nPauseIcon.displayName = \"VidstackPauseIcon\";\nconst PictureInPictureExitIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: pictureInPictureExitPaths });\n});\nPictureInPictureExitIcon.displayName = \"VidstackPictureInPictureExitIcon\";\nconst PictureInPictureIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: pictureInPicturePaths });\n});\nPictureInPictureIcon.displayName = \"VidstackPictureInPictureIcon\";\nconst PlayIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: playPaths });\n});\nPlayIcon.displayName = \"VidstackPlayIcon\";\nconst PlaybackSpeedCircleIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: playbackSpeedCirclePaths });\n});\nPlaybackSpeedCircleIcon.displayName = \"VidstackPlaybackSpeedCircleIcon\";\nconst PlaylistIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: playlistPaths });\n});\nPlaylistIcon.displayName = \"VidstackPlaylistIcon\";\nconst PreviousIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: previousPaths });\n});\nPreviousIcon.displayName = \"VidstackPreviousIcon\";\nconst QuestionMarkIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: questionMarkPaths });\n});\nQuestionMarkIcon.displayName = \"VidstackQuestionMarkIcon\";\nconst QueueListIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: queueListPaths });\n});\nQueueListIcon.displayName = \"VidstackQueueListIcon\";\nconst RadioButtonSelectedIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: radioButtonSelectedPaths });\n});\nRadioButtonSelectedIcon.displayName = \"VidstackRadioButtonSelectedIcon\";\nconst RadioButtonIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: radioButtonPaths });\n});\nRadioButtonIcon.displayName = \"VidstackRadioButtonIcon\";\nconst RepeatOnIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: repeatOnPaths });\n});\nRepeatOnIcon.displayName = \"VidstackRepeatOnIcon\";\nconst RepeatSquareOnIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: repeatSquareOnPaths });\n});\nRepeatSquareOnIcon.displayName = \"VidstackRepeatSquareOnIcon\";\nconst RepeatSquareIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: repeatSquarePaths });\n});\nRepeatSquareIcon.displayName = \"VidstackRepeatSquareIcon\";\nconst RepeatIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: repeatPaths });\n});\nRepeatIcon.displayName = \"VidstackRepeatIcon\";\nconst ReplayIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: replayPaths });\n});\nReplayIcon.displayName = \"VidstackReplayIcon\";\nconst RotateIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: rotatePaths });\n});\nRotateIcon.displayName = \"VidstackRotateIcon\";\nconst SearchIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: searchPaths });\n});\nSearchIcon.displayName = \"VidstackSearchIcon\";\nconst SeekBackward10Icon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: seekBackward10Paths });\n});\nSeekBackward10Icon.displayName = \"VidstackSeekBackward10Icon\";\nconst SeekBackward15Icon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: seekBackward15Paths });\n});\nSeekBackward15Icon.displayName = \"VidstackSeekBackward15Icon\";\nconst SeekBackward30Icon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: seekBackward30Paths });\n});\nSeekBackward30Icon.displayName = \"VidstackSeekBackward30Icon\";\nconst SeekBackwardIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: seekBackwardPaths });\n});\nSeekBackwardIcon.displayName = \"VidstackSeekBackwardIcon\";\nconst SeekForward10Icon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: seekForward10Paths });\n});\nSeekForward10Icon.displayName = \"VidstackSeekForward10Icon\";\nconst SeekForward15Icon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: seekForward15Paths });\n});\nSeekForward15Icon.displayName = \"VidstackSeekForward15Icon\";\nconst SeekForward30Icon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: seekForward30Paths });\n});\nSeekForward30Icon.displayName = \"VidstackSeekForward30Icon\";\nconst SeekForwardIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: seekForwardPaths });\n});\nSeekForwardIcon.displayName = \"VidstackSeekForwardIcon\";\nconst SendIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: sendPaths });\n});\nSendIcon.displayName = \"VidstackSendIcon\";\nconst SettingsMenuIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: settingsMenuPaths });\n});\nSettingsMenuIcon.displayName = \"VidstackSettingsMenuIcon\";\nconst SettingsSwitchIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: settingsSwitchPaths });\n});\nSettingsSwitchIcon.displayName = \"VidstackSettingsSwitchIcon\";\nconst SettingsIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: settingsPaths });\n});\nSettingsIcon.displayName = \"VidstackSettingsIcon\";\nconst ShareArrowIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: shareArrowPaths });\n});\nShareArrowIcon.displayName = \"VidstackShareArrowIcon\";\nconst ShareIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: sharePaths });\n});\nShareIcon.displayName = \"VidstackShareIcon\";\nconst ShuffleOnIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: shuffleOnPaths });\n});\nShuffleOnIcon.displayName = \"VidstackShuffleOnIcon\";\nconst ShuffleIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: shufflePaths });\n});\nShuffleIcon.displayName = \"VidstackShuffleIcon\";\nconst StopIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: stopPaths });\n});\nStopIcon.displayName = \"VidstackStopIcon\";\nconst SubtitlesIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: subtitlesPaths });\n});\nSubtitlesIcon.displayName = \"VidstackSubtitlesIcon\";\nconst SunIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: sunPaths });\n});\nSunIcon.displayName = \"VidstackSunIcon\";\nconst TheatreModeExitIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: theatreModeExitPaths });\n});\nTheatreModeExitIcon.displayName = \"VidstackTheatreModeExitIcon\";\nconst TheatreModeIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: theatreModePaths });\n});\nTheatreModeIcon.displayName = \"VidstackTheatreModeIcon\";\nconst ThumbsDownIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: thumbsDownPaths });\n});\nThumbsDownIcon.displayName = \"VidstackThumbsDownIcon\";\nconst ThumbsUpIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: thumbsUpPaths });\n});\nThumbsUpIcon.displayName = \"VidstackThumbsUpIcon\";\nconst TimerIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: timerPaths });\n});\nTimerIcon.displayName = \"VidstackTimerIcon\";\nconst TranscriptIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: transcriptPaths });\n});\nTranscriptIcon.displayName = \"VidstackTranscriptIcon\";\nconst TvIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: tvPaths });\n});\nTvIcon.displayName = \"VidstackTvIcon\";\nconst UserIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: userPaths });\n});\nUserIcon.displayName = \"VidstackUserIcon\";\nconst VolumeHighIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: volumeHighPaths });\n});\nVolumeHighIcon.displayName = \"VidstackVolumeHighIcon\";\nconst VolumeLowIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: volumeLowPaths });\n});\nVolumeLowIcon.displayName = \"VidstackVolumeLowIcon\";\nconst XMarkIcon = /* @__PURE__ */ forwardRef((props, ref) => {\n  return createElement(Icon, { ...props, className: cn(props.className), ref, paths: xMarkPaths });\n});\nXMarkIcon.displayName = \"VidstackXMarkIcon\";\n\nexport { AccessibilityIcon, AddIcon, AddNoteIcon, AddPlaylistIcon, AddUserIcon, AirPlayIcon, ArrowCollapseIcon, ArrowCollapseInIcon, ArrowDownIcon, ArrowExpandIcon, ArrowExpandOutIcon, ArrowLeftIcon, ArrowRightIcon, ArrowUpIcon, BookmarkIcon, CameraIcon, ChaptersIcon, ChatCollapseIcon, ChatIcon, CheckIcon, ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon, ChevronUpIcon, ChromecastIcon, ClipIcon, ClosedCaptionsIcon, ClosedCaptionsOnIcon, CommentIcon, ComputerIcon, DeviceIcon, DownloadIcon, EpisodesIcon, EyeIcon, FastBackwardIcon, FastForwardIcon, FlagIcon, FullscreenArrowExitIcon, FullscreenArrowIcon, FullscreenExitIcon, FullscreenIcon, HeartIcon, InfoIcon, LanguageIcon, LinkIcon, LockClosedIcon, LockOpenIcon, MenuHorizontalIcon, MenuVerticalIcon, MicrophoneIcon, MobileIcon, MoonIcon, MusicIcon, MusicOffIcon, MuteIcon, NextIcon, NoEyeIcon, NotificationIcon, OdometerIcon, PauseIcon, PictureInPictureExitIcon, PictureInPictureIcon, PlayIcon, PlaybackSpeedCircleIcon, PlaylistIcon, PreviousIcon, QuestionMarkIcon, QueueListIcon, RadioButtonIcon, RadioButtonSelectedIcon, RepeatIcon, RepeatOnIcon, RepeatSquareIcon, RepeatSquareOnIcon, ReplayIcon, RotateIcon, SearchIcon, SeekBackward10Icon, SeekBackward15Icon, SeekBackward30Icon, SeekBackwardIcon, SeekForward10Icon, SeekForward15Icon, SeekForward30Icon, SeekForwardIcon, SendIcon, SettingsIcon, SettingsMenuIcon, SettingsSwitchIcon, ShareArrowIcon, ShareIcon, ShuffleIcon, ShuffleOnIcon, StopIcon, SubtitlesIcon, SunIcon, TheatreModeExitIcon, TheatreModeIcon, ThumbsDownIcon, ThumbsUpIcon, TimerIcon, TranscriptIcon, TvIcon, UserIcon, VolumeHighIcon, VolumeLowIcon, XMarkIcon };\n", "\"use client\"\n\nimport * as React from 'react';\n\nconst Icon = /* @__PURE__ */ React.forwardRef((props, ref) => {\n  const { width, height, size = null, paths, ...restProps } = props;\n  return React.createElement(\"svg\", {\n    viewBox: \"0 0 32 32\",\n    ...restProps,\n    width: width ?? size,\n    height: height ?? size,\n    fill: \"none\",\n    \"aria-hidden\": \"true\",\n    focusable: \"false\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref,\n    dangerouslySetInnerHTML: { __html: paths }\n  });\n});\nIcon.displayName = \"VidstackIcon\";\n\nexport { Icon };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,mBAA0C;;;ACC1C,YAAuB;AAEvB,IAAM,OAA6B,iBAAW,CAAC,OAAO,QAAQ;AAC5D,QAAM,EAAE,OAAO,QAAQ,OAAO,MAAM,OAAO,GAAG,UAAU,IAAI;AAC5D,SAAa,oBAAc,OAAO;AAAA,IAChC,SAAS;AAAA,IACT,GAAG;AAAA,IACH,OAAO,SAAS;AAAA,IAChB,QAAQ,UAAU;AAAA,IAClB,MAAM;AAAA,IACN,eAAe;AAAA,IACf,WAAW;AAAA,IACX,OAAO;AAAA,IACP;AAAA,IACA,yBAAyB,EAAE,QAAQ,MAAM;AAAA,EAC3C,CAAC;AACH,CAAC;AACD,KAAK,cAAc;;;ADfnB,IAAM,KAAK,CAAC,cAAc,YAAY,GAAG,SAAS,cAAc;AAChE,IAAM,wBAAoC,yBAAW,CAAC,OAAO,QAAQ;AACnE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAmB,CAAC;AACzG,CAAC;AACD,kBAAkB,cAAc;AAChC,IAAM,kBAA8B,yBAAW,CAAC,OAAO,QAAQ;AAC7D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAa,CAAC;AACnG,CAAC;AACD,YAAY,cAAc;AAC1B,IAAM,sBAAkC,yBAAW,CAAC,OAAO,QAAQ;AACjE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAiB,CAAC;AACvG,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,kBAA8B,yBAAW,CAAC,OAAO,QAAQ;AAC7D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAa,CAAC;AACnG,CAAC;AACD,YAAY,cAAc;AAC1B,IAAM,cAA0B,yBAAW,CAAC,OAAO,QAAQ;AACzD,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAS,CAAC;AAC/F,CAAC;AACD,QAAQ,cAAc;AACtB,IAAM,kBAA8B,yBAAW,CAAC,OAAO,QAAQ;AAC7D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAa,CAAC;AACnG,CAAC;AACD,YAAY,cAAc;AAC1B,IAAM,0BAAsC,yBAAW,CAAC,OAAO,QAAQ;AACrE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAqB,CAAC;AAC3G,CAAC;AACD,oBAAoB,cAAc;AAClC,IAAM,wBAAoC,yBAAW,CAAC,OAAO,QAAQ;AACnE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAmB,CAAC;AACzG,CAAC;AACD,kBAAkB,cAAc;AAChC,IAAM,oBAAgC,yBAAW,CAAC,OAAO,QAAQ;AAC/D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAe,CAAC;AACrG,CAAC;AACD,cAAc,cAAc;AAC5B,IAAM,yBAAqC,yBAAW,CAAC,OAAO,QAAQ;AACpE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,OAAoB,CAAC;AAC1G,CAAC;AACD,mBAAmB,cAAc;AACjC,IAAM,sBAAkC,yBAAW,CAAC,OAAO,QAAQ;AACjE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAiB,CAAC;AACvG,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,oBAAgC,yBAAW,CAAC,OAAO,QAAQ;AAC/D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAe,CAAC;AACrG,CAAC;AACD,cAAc,cAAc;AAC5B,IAAM,qBAAiC,yBAAW,CAAC,OAAO,QAAQ;AAChE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAgB,CAAC;AACtG,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,kBAA8B,yBAAW,CAAC,OAAO,QAAQ;AAC7D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAa,CAAC;AACnG,CAAC;AACD,YAAY,cAAc;AAC1B,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,iBAA6B,yBAAW,CAAC,OAAO,QAAQ;AAC5D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAY,CAAC;AAClG,CAAC;AACD,WAAW,cAAc;AACzB,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,uBAAmC,yBAAW,CAAC,OAAO,QAAQ;AAClE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAkB,CAAC;AACxG,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,gBAA4B,yBAAW,CAAC,OAAO,QAAQ;AAC3D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAW,CAAC;AACjG,CAAC;AACD,UAAU,cAAc;AACxB,IAAM,sBAAkC,yBAAW,CAAC,OAAO,QAAQ;AACjE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAiB,CAAC;AACvG,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,sBAAkC,yBAAW,CAAC,OAAO,QAAQ;AACjE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAiB,CAAC;AACvG,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,uBAAmC,yBAAW,CAAC,OAAO,QAAQ;AAClE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAkB,CAAC;AACxG,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,oBAAgC,yBAAW,CAAC,OAAO,QAAQ;AAC/D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAe,CAAC;AACrG,CAAC;AACD,cAAc,cAAc;AAC5B,IAAM,qBAAiC,yBAAW,CAAC,OAAO,QAAQ;AAChE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAgB,CAAC;AACtG,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,2BAAuC,yBAAW,CAAC,OAAO,QAAQ;AACtE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAsB,CAAC;AAC5G,CAAC;AACD,qBAAqB,cAAc;AACnC,IAAM,yBAAqC,yBAAW,CAAC,OAAO,QAAQ;AACpE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAoB,CAAC;AAC1G,CAAC;AACD,mBAAmB,cAAc;AACjC,IAAM,kBAA8B,yBAAW,CAAC,OAAO,QAAQ;AAC7D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAa,CAAC;AACnG,CAAC;AACD,YAAY,cAAc;AAC1B,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,iBAA6B,yBAAW,CAAC,OAAO,QAAQ;AAC5D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAY,CAAC;AAClG,CAAC;AACD,WAAW,cAAc;AACzB,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,cAA0B,yBAAW,CAAC,OAAO,QAAQ;AACzD,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAS,CAAC;AAC/F,CAAC;AACD,QAAQ,cAAc;AACtB,IAAM,uBAAmC,yBAAW,CAAC,OAAO,QAAQ;AAClE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAkB,CAAC;AACxG,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,sBAAkC,yBAAW,CAAC,OAAO,QAAQ;AACjE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAiB,CAAC;AACvG,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,8BAA0C,yBAAW,CAAC,OAAO,QAAQ;AACzE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAyB,CAAC;AAC/G,CAAC;AACD,wBAAwB,cAAc;AACtC,IAAM,0BAAsC,yBAAW,CAAC,OAAO,QAAQ;AACrE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAqB,CAAC;AAC3G,CAAC;AACD,oBAAoB,cAAc;AAClC,IAAM,yBAAqC,yBAAW,CAAC,OAAO,QAAQ;AACpE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAoB,CAAC;AAC1G,CAAC;AACD,mBAAmB,cAAc;AACjC,IAAM,qBAAiC,yBAAW,CAAC,OAAO,QAAQ;AAChE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAgB,CAAC;AACtG,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,gBAA4B,yBAAW,CAAC,OAAO,QAAQ;AAC3D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAW,CAAC;AACjG,CAAC;AACD,UAAU,cAAc;AACxB,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,qBAAiC,yBAAW,CAAC,OAAO,QAAQ;AAChE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAgB,CAAC;AACtG,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,yBAAqC,yBAAW,CAAC,OAAO,QAAQ;AACpE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAoB,CAAC;AAC1G,CAAC;AACD,mBAAmB,cAAc;AACjC,IAAM,uBAAmC,yBAAW,CAAC,OAAO,QAAQ;AAClE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAkB,CAAC;AACxG,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,qBAAiC,yBAAW,CAAC,OAAO,QAAQ;AAChE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAgB,CAAC;AACtG,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,iBAA6B,yBAAW,CAAC,OAAO,QAAQ;AAC5D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAY,CAAC;AAClG,CAAC;AACD,WAAW,cAAc;AACzB,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,gBAA4B,yBAAW,CAAC,OAAO,QAAQ;AAC3D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAW,CAAC;AACjG,CAAC;AACD,UAAU,cAAc;AACxB,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,gBAA4B,yBAAW,CAAC,OAAO,QAAQ;AAC3D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAW,CAAC;AACjG,CAAC;AACD,UAAU,cAAc;AACxB,IAAM,uBAAmC,yBAAW,CAAC,OAAO,QAAQ;AAClE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAkB,CAAC;AACxG,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,gBAA4B,yBAAW,CAAC,OAAO,QAAQ;AAC3D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAW,CAAC;AACjG,CAAC;AACD,UAAU,cAAc;AACxB,IAAM,+BAA2C,yBAAW,CAAC,OAAO,QAAQ;AAC1E,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAA0B,CAAC;AAChH,CAAC;AACD,yBAAyB,cAAc;AACvC,IAAM,2BAAuC,yBAAW,CAAC,OAAO,QAAQ;AACtE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAsB,CAAC;AAC5G,CAAC;AACD,qBAAqB,cAAc;AACnC,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,8BAA0C,yBAAW,CAAC,OAAO,QAAQ;AACzE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAyB,CAAC;AAC/G,CAAC;AACD,wBAAwB,cAAc;AACtC,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,uBAAmC,yBAAW,CAAC,OAAO,QAAQ;AAClE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAkB,CAAC;AACxG,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,oBAAgC,yBAAW,CAAC,OAAO,QAAQ;AAC/D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAe,CAAC;AACrG,CAAC;AACD,cAAc,cAAc;AAC5B,IAAM,8BAA0C,yBAAW,CAAC,OAAO,QAAQ;AACzE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAyB,CAAC;AAC/G,CAAC;AACD,wBAAwB,cAAc;AACtC,IAAM,sBAAkC,yBAAW,CAAC,OAAO,QAAQ;AACjE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAiB,CAAC;AACvG,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,yBAAqC,yBAAW,CAAC,OAAO,QAAQ;AACpE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAoB,CAAC;AAC1G,CAAC;AACD,mBAAmB,cAAc;AACjC,IAAM,uBAAmC,yBAAW,CAAC,OAAO,QAAQ;AAClE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAkB,CAAC;AACxG,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,iBAA6B,yBAAW,CAAC,OAAO,QAAQ;AAC5D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAY,CAAC;AAClG,CAAC;AACD,WAAW,cAAc;AACzB,IAAM,iBAA6B,yBAAW,CAAC,OAAO,QAAQ;AAC5D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAY,CAAC;AAClG,CAAC;AACD,WAAW,cAAc;AACzB,IAAM,iBAA6B,yBAAW,CAAC,OAAO,QAAQ;AAC5D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAY,CAAC;AAClG,CAAC;AACD,WAAW,cAAc;AACzB,IAAM,iBAA6B,yBAAW,CAAC,OAAO,QAAQ;AAC5D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAY,CAAC;AAClG,CAAC;AACD,WAAW,cAAc;AACzB,IAAM,yBAAqC,yBAAW,CAAC,OAAO,QAAQ;AACpE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAoB,CAAC;AAC1G,CAAC;AACD,mBAAmB,cAAc;AACjC,IAAM,yBAAqC,yBAAW,CAAC,OAAO,QAAQ;AACpE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAoB,CAAC;AAC1G,CAAC;AACD,mBAAmB,cAAc;AACjC,IAAM,yBAAqC,yBAAW,CAAC,OAAO,QAAQ;AACpE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAoB,CAAC;AAC1G,CAAC;AACD,mBAAmB,cAAc;AACjC,IAAM,uBAAmC,yBAAW,CAAC,OAAO,QAAQ;AAClE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAkB,CAAC;AACxG,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,wBAAoC,yBAAW,CAAC,OAAO,QAAQ;AACnE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAmB,CAAC;AACzG,CAAC;AACD,kBAAkB,cAAc;AAChC,IAAM,wBAAoC,yBAAW,CAAC,OAAO,QAAQ;AACnE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAmB,CAAC;AACzG,CAAC;AACD,kBAAkB,cAAc;AAChC,IAAM,wBAAoC,yBAAW,CAAC,OAAO,QAAQ;AACnE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAmB,CAAC;AACzG,CAAC;AACD,kBAAkB,cAAc;AAChC,IAAM,sBAAkC,yBAAW,CAAC,OAAO,QAAQ;AACjE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAiB,CAAC;AACvG,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,uBAAmC,yBAAW,CAAC,OAAO,QAAQ;AAClE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAkB,CAAC;AACxG,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,yBAAqC,yBAAW,CAAC,OAAO,QAAQ;AACpE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAoB,CAAC;AAC1G,CAAC;AACD,mBAAmB,cAAc;AACjC,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,qBAAiC,yBAAW,CAAC,OAAO,QAAQ;AAChE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAgB,CAAC;AACtG,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,gBAA4B,yBAAW,CAAC,OAAO,QAAQ;AAC3D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAW,CAAC;AACjG,CAAC;AACD,UAAU,cAAc;AACxB,IAAM,oBAAgC,yBAAW,CAAC,OAAO,QAAQ;AAC/D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAe,CAAC;AACrG,CAAC;AACD,cAAc,cAAc;AAC5B,IAAM,kBAA8B,yBAAW,CAAC,OAAO,QAAQ;AAC7D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAa,CAAC;AACnG,CAAC;AACD,YAAY,cAAc;AAC1B,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,oBAAgC,yBAAW,CAAC,OAAO,QAAQ;AAC/D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAe,CAAC;AACrG,CAAC;AACD,cAAc,cAAc;AAC5B,IAAM,cAA0B,yBAAW,CAAC,OAAO,QAAQ;AACzD,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAS,CAAC;AAC/F,CAAC;AACD,QAAQ,cAAc;AACtB,IAAM,0BAAsC,yBAAW,CAAC,OAAO,QAAQ;AACrE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAqB,CAAC;AAC3G,CAAC;AACD,oBAAoB,cAAc;AAClC,IAAM,sBAAkC,yBAAW,CAAC,OAAO,QAAQ;AACjE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAiB,CAAC;AACvG,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,qBAAiC,yBAAW,CAAC,OAAO,QAAQ;AAChE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAgB,CAAC;AACtG,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,mBAA+B,yBAAW,CAAC,OAAO,QAAQ;AAC9D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,QAAc,CAAC;AACpG,CAAC;AACD,aAAa,cAAc;AAC3B,IAAM,gBAA4B,yBAAW,CAAC,OAAO,QAAQ;AAC3D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,SAAW,CAAC;AACjG,CAAC;AACD,UAAU,cAAc;AACxB,IAAM,qBAAiC,yBAAW,CAAC,OAAO,QAAQ;AAChE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,SAAgB,CAAC;AACtG,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,aAAyB,yBAAW,CAAC,OAAO,QAAQ;AACxD,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,SAAQ,CAAC;AAC9F,CAAC;AACD,OAAO,cAAc;AACrB,IAAM,eAA2B,yBAAW,CAAC,OAAO,QAAQ;AAC1D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,SAAU,CAAC;AAChG,CAAC;AACD,SAAS,cAAc;AACvB,IAAM,qBAAiC,yBAAW,CAAC,OAAO,QAAQ;AAChE,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,SAAgB,CAAC;AACtG,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,oBAAgC,yBAAW,CAAC,OAAO,QAAQ;AAC/D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,SAAe,CAAC;AACrG,CAAC;AACD,cAAc,cAAc;AAC5B,IAAM,gBAA4B,yBAAW,CAAC,OAAO,QAAQ;AAC3D,aAAO,4BAAc,MAAM,EAAE,GAAG,OAAO,WAAW,GAAG,MAAM,SAAS,GAAG,KAAK,OAAO,SAAW,CAAC;AACjG,CAAC;AACD,UAAU,cAAc;", "names": []}