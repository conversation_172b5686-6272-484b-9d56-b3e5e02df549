"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.teardownEditableText = exports.setupEditableText = exports.getEditingInfo = exports.currentlyEditing = exports.hasTextContents = exports.canEditText = void 0;
const identifierUtils_1 = require("./identifierUtils");
const sessionStorageUtils_1 = require("./sessionStorageUtils");
const constantsAndTypes_1 = require("./constantsAndTypes");
const jquery_1 = __importDefault(require("jquery"));
/**
 * Evaluates if the element's text can be edited in place.
 *
 * @param element
 */
const canEditText = (element) => {
    const treeElements = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.TREE_ELEMENT_LOOKUP) || {};
    const treeElement = treeElements[element.codebaseId];
    if (!treeElement) {
        return false;
    }
    return treeElement.staticTextContents;
};
exports.canEditText = canEditText;
/**
 * Returns if the node has text contents in the DOM
 */
const hasTextContents = (node) => {
    if (!node) {
        return false;
    }
    let hasText = false;
    let hasNonText = false;
    node.childNodes.forEach((child) => {
        if (child.nodeType === Node.TEXT_NODE) {
            hasText = true;
            return;
        }
        hasNonText = true;
    });
    return hasText && !hasNonText;
};
exports.hasTextContents = hasTextContents;
const currentlyEditing = () => {
    const item = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.TEXT_EDIT);
    return item !== null && item !== undefined;
};
exports.currentlyEditing = currentlyEditing;
const markAsEditing = (info) => {
    (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.TEXT_EDIT, info);
};
const getEditingInfo = () => {
    return (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.TEXT_EDIT);
};
exports.getEditingInfo = getEditingInfo;
const clearEditingInfo = () => {
    (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.TEXT_EDIT, null);
};
/**
 * Takes an element and registers it as an editable text element.
 * Mutates the DOM to make the element editable.
 */
const setupEditableText = (element, parentPort, storyboardId) => {
    const domElement = (0, identifierUtils_1.getNodeForElementKey)(element.getKey());
    if (!domElement) {
        return;
    }
    const originalText = (0, jquery_1.default)(domElement).text();
    markAsEditing({
        key: element.getKey(),
        originalText,
    });
    parentPort.postMessage({
        id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.START_EDITING_TEXT,
        data: {
            key: element.getKey(),
            oldText: originalText,
        },
    });
    (0, jquery_1.default)(domElement).attr('contenteditable', 'plaintext-only').trigger('focus');
    // Apply styling directly
    (0, jquery_1.default)(domElement).css({
        cursor: 'text',
        outline: 'none',
        border: 'none',
    });
    (0, jquery_1.default)(domElement).on('blur', () => (0, exports.teardownEditableText)(parentPort, storyboardId));
};
exports.setupEditableText = setupEditableText;
/**
 * Used to mark the completion of the editable text process.
 * Reverts the DOM to its original state.
 * Sends a message to the housing frame with updated text, if necessary.
 *
 */
const teardownEditableText = (parentPort, storyboardId) => {
    var _a;
    const editingInfo = (0, exports.getEditingInfo)();
    if (!(0, exports.currentlyEditing)()) {
        return;
    }
    clearEditingInfo();
    if (!editingInfo) {
        return;
    }
    const domElement = (0, identifierUtils_1.getNodeForElementKey)(editingInfo.key);
    if (!domElement) {
        return;
    }
    const updatedText = (0, jquery_1.default)(domElement).text();
    parentPort.postMessage({
        id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.EDITED_TEXT,
        data: {
            key: editingInfo.key,
            newText: updatedText,
            oldText: editingInfo.originalText,
        },
    });
    // Clear any selection
    (_a = window.getSelection()) === null || _a === void 0 ? void 0 : _a.removeAllRanges();
    // Cleanup
    (0, jquery_1.default)(domElement).removeAttr('contenteditable').off('blur').css({
        cursor: '',
        outline: '',
        border: '',
    });
    clearEditingInfo();
};
exports.teardownEditableText = teardownEditableText;
//# sourceMappingURL=data:application/json;base64,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