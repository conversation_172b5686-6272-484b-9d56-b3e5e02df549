import { memo, useCallback } from "react";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { VideoFile } from "@/lib/videoApi";
import {
  Play,
  MoreVertical,
  Calendar,
  Eye,
  VideoIcon,
  Download,
  Share2,
  Trash2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
interface VideoCardProps {
  video: VideoFile;
  index: number;
  onVideoSelect: (video: VideoFile) => void;
  onVideoAction: (action: string, video: VideoFile) => void;
  formatDate: (date: string) => string;
  formatDuration: (seconds: number) => string;
  getStatusIcon: (status: string) => React.ReactNode;
}
const VideoCard = memo(
  ({
    video,
    index,
    onVideoSelect,
    onVideoAction,
    formatDate,
    formatDuration,
    getStatusIcon,
  }: VideoCardProps) => {
    const handleVideoSelect = useCallback(() => {
      onVideoSelect(video);
    }, [onVideoSelect, video]);
    const handleAction = useCallback(
      (action: string) => {
        onVideoAction(action, video);
      },
      [onVideoAction, video]
    );
    return (
      <motion.div
        key={video.video_id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ delay: index * 0.05 }}
      >
        <Card className="bg-[#0f1729]/40 backdrop-blur-xl border-slate-800/60 overflow-hidden group hover:border-indigo-500/50 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-indigo-500/5">
          <div
            className="relative aspect-video bg-black/60 cursor-pointer"
            onClick={handleVideoSelect}
          >
            {video.playback_info?.thumbnail ? (
              <img
                src={video.playback_info.thumbnail}
                alt={video.file_name}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <VideoIcon className="w-8 h-8 text-violet-400/50" />
              </div>
            )}
            <div className="absolute bottom-2 right-2 px-2 py-1 bg-black/80 backdrop-blur-sm rounded text-xs text-white">
              {formatDuration(video.duration || 0)}
            </div>
            <div className="absolute inset-0 flex items-center justify-center bg-black/0 group-hover:bg-black/40 transition-colors">
              <div className="transform scale-0 group-hover:scale-100 transition-transform duration-200">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-indigo-500 to-violet-600 flex items-center justify-center shadow-lg shadow-indigo-500/20">
                  <Play className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          </div>
          <div className="p-4">
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1 min-w-0">
                <h3
                  className="font-medium text-white truncate"
                  title={video.file_name || "Untitled"}
                >
                  {video.file_name || "Untitled"}
                </h3>
                <div className="mt-1 flex items-center gap-2 text-xs text-slate-400">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    <span>
                      {formatDate(
                        video.uploaded_at || new Date().toISOString()
                      )}
                    </span>
                  </div>
                  <span className="text-slate-600">•</span>
                  <div className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    <span>{Math.round(video.progress || 0)}%</span>
                  </div>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-slate-400 hover:text-white"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="w-48 bg-[#0f1729]/95 backdrop-blur-xl border-slate-800/60 rounded-lg shadow-xl"
                >
                  <DropdownMenuItem
                    onClick={() => handleAction("download")}
                    className="text-slate-300 hover:text-white focus:text-white"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleAction("share")}
                    className="text-slate-300 hover:text-white focus:text-white"
                  >
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleAction("delete")}
                    className="text-red-400 hover:text-red-300 focus:text-red-300"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="mt-3 flex items-center justify-between">
              <div className="flex items-center gap-1">
                {getStatusIcon(video.status || "unknown")}
                <span
                  className={`text-xs capitalize ${
                    video.status === "completed"
                      ? "text-emerald-400"
                      : video.status === "processing"
                      ? "text-amber-400"
                      : video.status === "queued"
                      ? "text-blue-400"
                      : "text-red-400"
                  }`}
                >
                  {video.status || "unknown"}
                </span>
              </div>
              <span className="text-xs text-slate-500">
                {((video.file_size || 0) / (1024 * 1024)).toFixed(1)} MB
              </span>
            </div>
          </div>
        </Card>
      </motion.div>
    );
  }
);
VideoCard.displayName = "VideoCard";
export default VideoCard;
