{"version": 3, "sources": ["../../@vidstack/react/node_modules/media-captions/dist/dev/index.js"], "sourcesContent": ["const ParseErrorCode = {\n  LoadFail: 0,\n  BadSignature: 1,\n  BadTimestamp: 2,\n  BadSettingValue: 3,\n  BadFormat: 4,\n  UnknownSetting: 5\n};\nclass ParseError extends Error {\n  code;\n  line;\n  constructor(init) {\n    super(init.reason);\n    this.code = init.code;\n    this.line = init.line;\n  }\n}\n\nconst LINE_TERMINATOR_RE = /\\r?\\n|\\r/gm;\nclass TextLineTransformStream {\n  writable;\n  readable;\n  constructor(encoding) {\n    const transformer = new TextStreamLineIterator(encoding);\n    this.writable = new WritableStream({\n      write(chunk) {\n        transformer.transform(chunk);\n      },\n      close() {\n        transformer.close();\n      }\n    });\n    this.readable = new ReadableStream({\n      start(controller) {\n        transformer.onLine = (line) => controller.enqueue(line);\n        transformer.onClose = () => controller.close();\n      }\n    });\n  }\n}\nclass TextStreamLineIterator {\n  _buffer = \"\";\n  _decoder;\n  onLine;\n  onClose;\n  constructor(encoding) {\n    this._decoder = new TextDecoder(encoding);\n  }\n  transform(chunk) {\n    this._buffer += this._decoder.decode(chunk, { stream: true });\n    const lines = this._buffer.split(LINE_TERMINATOR_RE);\n    this._buffer = lines.pop() || \"\";\n    for (let i = 0; i < lines.length; i++)\n      this.onLine(lines[i].trim());\n  }\n  close() {\n    if (this._buffer)\n      this.onLine(this._buffer.trim());\n    this._buffer = \"\";\n    this.onClose();\n  }\n}\n\nasync function parseText(text, options) {\n  const stream = new ReadableStream({\n    start(controller) {\n      const lines = text.split(LINE_TERMINATOR_RE);\n      for (const line of lines)\n        controller.enqueue(line);\n      controller.close();\n    }\n  });\n  return parseTextStream(stream, options);\n}\nasync function parseTextStream(stream, options) {\n  const type = options?.type ?? \"vtt\";\n  let factory;\n  if (typeof type === \"string\") {\n    switch (type) {\n      case \"srt\":\n        factory = (await import('./srt-parser.js')).default;\n        break;\n      case \"ssa\":\n      case \"ass\":\n        factory = (await import('./ssa-parser.js')).default;\n        break;\n      default:\n        factory = (await Promise.resolve().then(function () { return vttParser; })).default;\n    }\n  } else {\n    factory = type;\n  }\n  let result;\n  const reader = stream.getReader(), parser = factory(), errors = options?.errors !== false || !!options?.strict || !!options?.errors;\n  await parser.init({\n    strict: false,\n    ...options,\n    errors,\n    type,\n    cancel() {\n      reader.cancel();\n      result = parser.done(true);\n    }\n  });\n  let i = 1;\n  while (true) {\n    const { value, done } = await reader.read();\n    if (done) {\n      parser.parse(\"\", i);\n      result = parser.done(false);\n      break;\n    }\n    parser.parse(value, i);\n    i++;\n  }\n  return result;\n}\n\nasync function parseResponse(response, options) {\n  const res = await response;\n  if (!res.ok || !res.body) {\n    let error;\n    {\n      error = new ParseError({\n        code: ParseErrorCode.LoadFail,\n        reason: !res.ok ? `response is not ok (status: ${res.status})` : `response body is missing (status: ${res.status})`,\n        line: -1\n      });\n      options?.onError?.(error);\n    }\n    return {\n      metadata: {},\n      cues: [],\n      regions: [],\n      errors: [error]\n    };\n  }\n  const contentType = res.headers.get(\"content-type\") || \"\", type = contentType.match(/text\\/(.*?)(?:;|$)/)?.[1], encoding = contentType.match(/charset=(.*?)(?:;|$)/)?.[1];\n  return parseByteStream(res.body, { type, encoding, ...options });\n}\nasync function parseByteStream(stream, { encoding = \"utf-8\", ...options } = {}) {\n  const textStream = stream.pipeThrough(new TextLineTransformStream(encoding));\n  return parseTextStream(textStream, options);\n}\n\nclass TextCue extends EventTarget {\n  /**\n   * A string that identifies the cue.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/TextTrackCue/id}\n   */\n  id = \"\";\n  /**\n   * A `double` that represents the video time that the cue will start being displayed, in seconds.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/TextTrackCue/startTime}\n   */\n  startTime;\n  /**\n   * A `double` that represents the video time that the cue will stop being displayed, in seconds.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/TextTrackCue/endTime}\n   */\n  endTime;\n  /**\n   * Returns a string with the contents of the cue.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/text}\n   */\n  text;\n  /**\n   * A `boolean` for whether the video will pause when this cue stops being displayed.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/TextTrackCue/pauseOnExit}\n   */\n  pauseOnExit = false;\n  constructor(startTime, endTime, text) {\n    super();\n    this.startTime = startTime;\n    this.endTime = endTime;\n    this.text = text;\n  }\n  addEventListener(type, listener, options) {\n    super.addEventListener(type, listener, options);\n  }\n  removeEventListener(type, listener, options) {\n    super.removeEventListener(type, listener, options);\n  }\n}\n\nconst IS_SERVER = typeof document === \"undefined\";\n\nconst CueBase = IS_SERVER ? TextCue : window.VTTCue;\nclass VTTCue extends CueBase {\n  /**\n   * A `VTTRegion` object describing the video's sub-region that the cue will be drawn onto,\n   * or `null` if none is assigned.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/region}\n   */\n  region = null;\n  /**\n   * The cue writing direction.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/vertical}\n   */\n  vertical = \"\";\n  /**\n   * Returns `true` if the `VTTCue.line` attribute is an integer number of lines or a percentage\n   * of the video size.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/snapToLines}\n   */\n  snapToLines = true;\n  /**\n   * Returns the line positioning of the cue. This can be the string `'auto'` or a number whose\n   * interpretation depends on the value of `VTTCue.snapToLines`.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/line}\n   */\n  line = \"auto\";\n  /**\n   * Returns an enum representing the alignment of the `VTTCue.line`.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/lineAlign}\n   */\n  lineAlign = \"start\";\n  /**\n   * Returns the indentation of the cue within the line. This can be the string `'auto'` or a\n   * number representing the percentage of the `VTTCue.region`, or the video size if `VTTCue`.region`\n   * is `null`.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/position}\n   */\n  position = \"auto\";\n  /**\n   * Returns an enum representing the alignment of the cue. This is used to determine what\n   * the `VTTCue.position` is anchored to. The default is `'auto'`.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/positionAlign}\n   */\n  positionAlign = \"auto\";\n  /**\n   * Returns a double representing the size of the cue, as a percentage of the video size.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/size}\n   */\n  size = 100;\n  /**\n   * Returns an enum representing the alignment of all the lines of text within the cue box.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/VTTCue/align}\n   */\n  align = \"center\";\n  /**\n   * Additional styles associated with the cue.\n   */\n  style;\n}\n\nclass VTTRegion {\n  /**\n   * A string that identifies the region.\n   */\n  id = \"\";\n  /**\n   * A `double` representing the width of the region, as a percentage of the video.\n   */\n  width = 100;\n  /**\n   * A `double` representing the height of the region, in number of lines.\n   */\n  lines = 3;\n  /**\n   * A `double` representing the region anchor X offset, as a percentage of the region.\n   */\n  regionAnchorX = 0;\n  /**\n   * A `double` representing the region anchor Y offset, as a percentage of the region.\n   */\n  regionAnchorY = 100;\n  /**\n   * A `double` representing the viewport anchor X offset, as a percentage of the video.\n   */\n  viewportAnchorX = 0;\n  /**\n   * A `double` representing the viewport anchor Y offset, as a percentage of the video.\n   */\n  viewportAnchorY = 100;\n  /**\n   * An enum representing how adding new cues will move existing cues.\n   */\n  scroll = \"\";\n}\n\nconst COMMA$1 = \",\", PERCENT_SIGN$1 = \"%\";\nfunction toNumber(text) {\n  const num = parseInt(text, 10);\n  return !Number.isNaN(num) ? num : null;\n}\nfunction toPercentage(text) {\n  const num = parseInt(text.replace(PERCENT_SIGN$1, \"\"), 10);\n  return !Number.isNaN(num) && num >= 0 && num <= 100 ? num : null;\n}\nfunction toCoords(text) {\n  if (!text.includes(COMMA$1))\n    return null;\n  const [x, y] = text.split(COMMA$1).map(toPercentage);\n  return x !== null && y !== null ? [x, y] : null;\n}\nfunction toFloat(text) {\n  const num = parseFloat(text);\n  return !Number.isNaN(num) ? num : null;\n}\n\nconst HEADER_MAGIC = \"WEBVTT\", COMMA = \",\", PERCENT_SIGN = \"%\", SETTING_SEP_RE = /[:=]/, SETTING_LINE_RE = /^[\\s\\t]*(region|vertical|line|position|size|align)[:=]/, NOTE_BLOCK_START = \"NOTE\", REGION_BLOCK_START = \"REGION\", REGION_BLOCK_START_RE = /^REGION:?[\\s\\t]+/, SPACE_RE = /[\\s\\t]+/, TIMESTAMP_SEP = \"-->\", TIMESTAMP_SEP_RE = /[\\s\\t]*-->[\\s\\t]+/, ALIGN_RE = /start|center|end|left|right/, LINE_ALIGN_RE = /start|center|end/, POS_ALIGN_RE = /line-(?:left|right)|center|auto/, TIMESTAMP_RE = /^(?:(\\d{1,2}):)?(\\d{2}):(\\d{2})(?:\\.(\\d{1,3}))?$/;\nvar VTTBlock = /* @__PURE__ */ ((VTTBlock2) => {\n  VTTBlock2[VTTBlock2[\"None\"] = 0] = \"None\";\n  VTTBlock2[VTTBlock2[\"Header\"] = 1] = \"Header\";\n  VTTBlock2[VTTBlock2[\"Cue\"] = 2] = \"Cue\";\n  VTTBlock2[VTTBlock2[\"Region\"] = 3] = \"Region\";\n  VTTBlock2[VTTBlock2[\"Note\"] = 4] = \"Note\";\n  return VTTBlock2;\n})(VTTBlock || {});\nclass VTTParser {\n  _init;\n  _block = 0 /* None */;\n  _metadata = {};\n  _regions = {};\n  _cues = [];\n  _cue = null;\n  _region = null;\n  _errors = [];\n  _errorBuilder;\n  _prevLine = \"\";\n  async init(init) {\n    this._init = init;\n    if (init.strict)\n      this._block = 1 /* Header */;\n    if (init.errors)\n      this._errorBuilder = (await import('./errors.js')).ParseErrorBuilder;\n  }\n  parse(line, lineCount) {\n    if (line === \"\") {\n      if (this._cue) {\n        this._cues.push(this._cue);\n        this._init.onCue?.(this._cue);\n        this._cue = null;\n      } else if (this._region) {\n        this._regions[this._region.id] = this._region;\n        this._init.onRegion?.(this._region);\n        this._region = null;\n      } else if (this._block === 1 /* Header */) {\n        this._parseHeader(line, lineCount);\n        this._init.onHeaderMetadata?.(this._metadata);\n      }\n      this._block = 0 /* None */;\n    } else if (this._block) {\n      switch (this._block) {\n        case 1 /* Header */:\n          this._parseHeader(line, lineCount);\n          break;\n        case 2 /* Cue */:\n          if (this._cue) {\n            const hasText = this._cue.text.length > 0;\n            if (!hasText && SETTING_LINE_RE.test(line)) {\n              this._parseCueSettings(line.split(SPACE_RE), lineCount);\n            } else {\n              this._cue.text += (hasText ? \"\\n\" : \"\") + line;\n            }\n          }\n          break;\n        case 3 /* Region */:\n          this._parseRegionSettings(line.split(SPACE_RE), lineCount);\n          break;\n      }\n    } else if (line.startsWith(NOTE_BLOCK_START)) {\n      this._block = 4 /* Note */;\n    } else if (line.startsWith(REGION_BLOCK_START)) {\n      this._block = 3 /* Region */;\n      this._region = new VTTRegion();\n      this._parseRegionSettings(line.replace(REGION_BLOCK_START_RE, \"\").split(SPACE_RE), lineCount);\n    } else if (line.includes(TIMESTAMP_SEP)) {\n      const result = this._parseTimestamp(line, lineCount);\n      if (result) {\n        this._cue = new VTTCue(result[0], result[1], \"\");\n        this._cue.id = this._prevLine;\n        this._parseCueSettings(result[2], lineCount);\n      }\n      this._block = 2 /* Cue */;\n    } else if (lineCount === 1) {\n      this._parseHeader(line, lineCount);\n    }\n    this._prevLine = line;\n  }\n  done() {\n    return {\n      metadata: this._metadata,\n      cues: this._cues,\n      regions: Object.values(this._regions),\n      errors: this._errors\n    };\n  }\n  _parseHeader(line, lineCount) {\n    if (lineCount > 1) {\n      if (SETTING_SEP_RE.test(line)) {\n        const [key, value] = line.split(SETTING_SEP_RE);\n        if (key)\n          this._metadata[key] = (value || \"\").replace(SPACE_RE, \"\");\n      }\n    } else if (line.startsWith(HEADER_MAGIC)) {\n      this._block = 1 /* Header */;\n    } else {\n      this._handleError(this._errorBuilder?._badVTTHeader());\n    }\n  }\n  _parseTimestamp(line, lineCount) {\n    const [startTimeText, trailingText = \"\"] = line.split(TIMESTAMP_SEP_RE), [endTimeText, ...settingsText] = trailingText.split(SPACE_RE), startTime = parseVTTTimestamp(startTimeText), endTime = parseVTTTimestamp(endTimeText);\n    if (startTime !== null && endTime !== null && endTime > startTime) {\n      return [startTime, endTime, settingsText];\n    } else {\n      if (startTime === null) {\n        this._handleError(this._errorBuilder?._badStartTimestamp(startTimeText, lineCount));\n      }\n      if (endTime === null) {\n        this._handleError(this._errorBuilder?._badEndTimestamp(endTimeText, lineCount));\n      }\n      if (startTime != null && endTime !== null && endTime > startTime) {\n        this._handleError(this._errorBuilder?._badRangeTimestamp(startTime, endTime, lineCount));\n      }\n    }\n  }\n  /**\n   * @see {@link https://www.w3.org/TR/webvtt1/#region-settings-parsing}\n   */\n  _parseRegionSettings(settings, line) {\n    let badValue;\n    for (let i = 0; i < settings.length; i++) {\n      if (SETTING_SEP_RE.test(settings[i])) {\n        badValue = false;\n        const [name, value] = settings[i].split(SETTING_SEP_RE);\n        switch (name) {\n          case \"id\":\n            this._region.id = value;\n            break;\n          case \"width\":\n            const width = toPercentage(value);\n            if (width !== null)\n              this._region.width = width;\n            else\n              badValue = true;\n            break;\n          case \"lines\":\n            const lines = toNumber(value);\n            if (lines !== null)\n              this._region.lines = lines;\n            else\n              badValue = true;\n            break;\n          case \"regionanchor\":\n            const region = toCoords(value);\n            if (region !== null) {\n              this._region.regionAnchorX = region[0];\n              this._region.regionAnchorY = region[1];\n            } else\n              badValue = true;\n            break;\n          case \"viewportanchor\":\n            const viewport = toCoords(value);\n            if (viewport !== null) {\n              this._region.viewportAnchorX = viewport[0];\n              this._region.viewportAnchorY = viewport[1];\n            } else\n              badValue = true;\n            break;\n          case \"scroll\":\n            if (value === \"up\")\n              this._region.scroll = \"up\";\n            else\n              badValue = true;\n            break;\n          default:\n            this._handleError(this._errorBuilder?._unknownRegionSetting(name, value, line));\n        }\n        if (badValue) {\n          this._handleError(this._errorBuilder?._badRegionSetting(name, value, line));\n        }\n      }\n    }\n  }\n  /**\n   * @see {@link https://www.w3.org/TR/webvtt1/#cue-timings-and-settings-parsing}\n   */\n  _parseCueSettings(settings, line) {\n    let badValue;\n    for (let i = 0; i < settings.length; i++) {\n      badValue = false;\n      if (SETTING_SEP_RE.test(settings[i])) {\n        const [name, value] = settings[i].split(SETTING_SEP_RE);\n        switch (name) {\n          case \"region\":\n            const region = this._regions[value];\n            if (region)\n              this._cue.region = region;\n            break;\n          case \"vertical\":\n            if (value === \"lr\" || value === \"rl\") {\n              this._cue.vertical = value;\n              this._cue.region = null;\n            } else\n              badValue = true;\n            break;\n          case \"line\":\n            const [linePos, lineAlign] = value.split(COMMA);\n            if (linePos.includes(PERCENT_SIGN)) {\n              const percentage = toPercentage(linePos);\n              if (percentage !== null) {\n                this._cue.line = percentage;\n                this._cue.snapToLines = false;\n              } else\n                badValue = true;\n            } else {\n              const number = toFloat(linePos);\n              if (number !== null)\n                this._cue.line = number;\n              else\n                badValue = true;\n            }\n            if (LINE_ALIGN_RE.test(lineAlign)) {\n              this._cue.lineAlign = lineAlign;\n            } else if (lineAlign) {\n              badValue = true;\n            }\n            if (this._cue.line !== \"auto\")\n              this._cue.region = null;\n            break;\n          case \"position\":\n            const [colPos, colAlign] = value.split(COMMA), position = toPercentage(colPos);\n            if (position !== null)\n              this._cue.position = position;\n            else\n              badValue = true;\n            if (colAlign && POS_ALIGN_RE.test(colAlign)) {\n              this._cue.positionAlign = colAlign;\n            } else if (colAlign) {\n              badValue = true;\n            }\n            break;\n          case \"size\":\n            const size = toPercentage(value);\n            if (size !== null) {\n              this._cue.size = size;\n              if (size < 100)\n                this._cue.region = null;\n            } else {\n              badValue = true;\n            }\n            break;\n          case \"align\":\n            if (ALIGN_RE.test(value)) {\n              this._cue.align = value;\n            } else {\n              badValue = true;\n            }\n            break;\n          default:\n            this._handleError(this._errorBuilder?._unknownCueSetting(name, value, line));\n        }\n        if (badValue) {\n          this._handleError(this._errorBuilder?._badCueSetting(name, value, line));\n        }\n      }\n    }\n  }\n  _handleError(error) {\n    if (!error)\n      return;\n    this._errors.push(error);\n    if (this._init.strict) {\n      this._init.cancel();\n      throw error;\n    } else {\n      this._init.onError?.(error);\n    }\n  }\n}\nfunction parseVTTTimestamp(timestamp) {\n  const match = timestamp.match(TIMESTAMP_RE);\n  if (!match)\n    return null;\n  const hours = match[1] ? parseInt(match[1], 10) : 0, minutes = parseInt(match[2], 10), seconds = parseInt(match[3], 10), milliseconds = match[4] ? parseInt(match[4].padEnd(3, \"0\"), 10) : 0, total = hours * 3600 + minutes * 60 + seconds + milliseconds / 1e3;\n  if (hours < 0 || minutes < 0 || seconds < 0 || milliseconds < 0 || minutes > 59 || seconds > 59) {\n    return null;\n  }\n  return total;\n}\nfunction createVTTParser() {\n  return new VTTParser();\n}\n\nvar vttParser = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  VTTBlock: VTTBlock,\n  VTTParser: VTTParser,\n  default: createVTTParser,\n  parseVTTTimestamp: parseVTTTimestamp\n});\n\nconst DIGIT_RE = /[0-9]/, MULTI_SPACE_RE = /[\\s\\t]+/, TAG_NAME = {\n  c: \"span\",\n  i: \"i\",\n  b: \"b\",\n  u: \"u\",\n  ruby: \"ruby\",\n  rt: \"rt\",\n  v: \"span\",\n  lang: \"span\",\n  timestamp: \"span\"\n}, HTML_ENTITIES = {\n  \"&amp;\": \"&\",\n  \"&lt;\": \"<\",\n  \"&gt;\": \">\",\n  \"&quot;\": '\"',\n  \"&#39;\": \"'\",\n  \"&nbsp;\": \"\\xA0\",\n  \"&lrm;\": \"\\u200E\",\n  \"&rlm;\": \"\\u200F\"\n}, HTML_ENTITY_RE = /&(?:amp|lt|gt|quot|#(0+)?39|nbsp|lrm|rlm);/g, COLORS = /* @__PURE__ */ new Set([\n  \"white\",\n  \"lime\",\n  \"cyan\",\n  \"red\",\n  \"yellow\",\n  \"magenta\",\n  \"blue\",\n  \"black\"\n]), BLOCK_TYPES = /* @__PURE__ */ new Set(Object.keys(TAG_NAME));\nfunction tokenizeVTTCue(cue) {\n  let buffer = \"\", mode = 1 /* Data */, result = [], stack = [], node;\n  for (let i = 0; i < cue.text.length; i++) {\n    const char = cue.text[i];\n    switch (mode) {\n      case 1 /* Data */:\n        if (char === \"<\") {\n          addText();\n          mode = 2 /* Tag */;\n        } else {\n          buffer += char;\n        }\n        break;\n      case 2 /* Tag */:\n        switch (char) {\n          case \"\\n\":\n          case \"\t\":\n          case \" \":\n            addNode();\n            mode = 4 /* Annotation */;\n            break;\n          case \".\":\n            addNode();\n            mode = 3 /* Class */;\n            break;\n          case \"/\":\n            mode = 5 /* EndTag */;\n            break;\n          case \">\":\n            addNode();\n            mode = 1 /* Data */;\n            break;\n          default:\n            if (!buffer && DIGIT_RE.test(char))\n              mode = 6 /* Timestamp */;\n            buffer += char;\n            break;\n        }\n        break;\n      case 3 /* Class */:\n        switch (char) {\n          case \"\t\":\n          case \" \":\n          case \"\\n\":\n            addClass();\n            if (node)\n              node.class?.trim();\n            mode = 4 /* Annotation */;\n            break;\n          case \".\":\n            addClass();\n            break;\n          case \">\":\n            addClass();\n            if (node)\n              node.class?.trim();\n            mode = 1 /* Data */;\n            break;\n          default:\n            buffer += char;\n        }\n        break;\n      case 4 /* Annotation */:\n        if (char === \">\") {\n          buffer = buffer.replace(MULTI_SPACE_RE, \" \");\n          if (node?.type === \"v\")\n            node.voice = replaceHTMLEntities(buffer);\n          else if (node?.type === \"lang\")\n            node.lang = replaceHTMLEntities(buffer);\n          buffer = \"\";\n          mode = 1 /* Data */;\n        } else {\n          buffer += char;\n        }\n        break;\n      case 5 /* EndTag */:\n        if (char === \">\") {\n          buffer = \"\";\n          node = stack.pop();\n          mode = 1 /* Data */;\n        }\n        break;\n      case 6 /* Timestamp */:\n        if (char === \">\") {\n          const time = parseVTTTimestamp(buffer);\n          if (time !== null && time >= cue.startTime && time <= cue.endTime) {\n            buffer = \"timestamp\";\n            addNode();\n            node.time = time;\n          }\n          buffer = \"\";\n          mode = 1 /* Data */;\n        } else {\n          buffer += char;\n        }\n        break;\n    }\n  }\n  function addNode() {\n    if (BLOCK_TYPES.has(buffer)) {\n      const parent = node;\n      node = createBlockNode(buffer);\n      if (parent) {\n        if (stack[stack.length - 1] !== parent)\n          stack.push(parent);\n        parent.children.push(node);\n      } else\n        result.push(node);\n    }\n    buffer = \"\";\n    mode = 1 /* Data */;\n  }\n  function addClass() {\n    if (node && buffer) {\n      const color = buffer.replace(\"bg_\", \"\");\n      if (COLORS.has(color)) {\n        node[buffer.startsWith(\"bg_\") ? \"bgColor\" : \"color\"] = color;\n      } else {\n        node.class = !node.class ? buffer : node.class + \" \" + buffer;\n      }\n    }\n    buffer = \"\";\n  }\n  function addText() {\n    if (!buffer)\n      return;\n    const text = { type: \"text\", data: replaceHTMLEntities(buffer) };\n    node ? node.children.push(text) : result.push(text);\n    buffer = \"\";\n  }\n  if (mode === 1 /* Data */)\n    addText();\n  return result;\n}\nfunction createBlockNode(type) {\n  return {\n    tagName: TAG_NAME[type],\n    type,\n    children: []\n  };\n}\nfunction replaceHTMLEntities(text) {\n  return text.replace(HTML_ENTITY_RE, (entity) => HTML_ENTITIES[entity] || \"'\");\n}\n\nfunction setCSSVar(el, name, value) {\n  el.style.setProperty(`--${name}`, value + \"\");\n}\nfunction setDataAttr(el, name, value = true) {\n  el.setAttribute(`data-${name}`, value === true ? \"\" : value + \"\");\n}\nfunction setPartAttr(el, name) {\n  el.setAttribute(\"data-part\", name);\n}\nfunction getLineHeight(el) {\n  return parseFloat(getComputedStyle(el).lineHeight) || 0;\n}\n\nfunction createVTTCueTemplate(cue) {\n  if (IS_SERVER) {\n    throw Error(\n      \"[media-captions] called `createVTTCueTemplate` on the server - use `renderVTTCueString`\"\n    );\n  }\n  const template = document.createElement(\"template\");\n  template.innerHTML = renderVTTCueString(cue);\n  return { cue, content: template.content };\n}\nfunction renderVTTCueString(cue, currentTime = 0) {\n  return renderVTTTokensString(tokenizeVTTCue(cue), currentTime);\n}\nfunction renderVTTTokensString(tokens, currentTime = 0) {\n  let attrs, result = \"\";\n  for (const token of tokens) {\n    if (token.type === \"text\") {\n      result += token.data;\n    } else {\n      const isTimestamp = token.type === \"timestamp\";\n      attrs = {};\n      attrs.class = token.class;\n      attrs.title = token.type === \"v\" && token.voice;\n      attrs.lang = token.type === \"lang\" && token.lang;\n      attrs[\"data-part\"] = token.type === \"v\" && \"voice\";\n      if (isTimestamp) {\n        attrs[\"data-part\"] = \"timed\";\n        attrs[\"data-time\"] = token.time;\n        attrs[\"data-future\"] = token.time > currentTime;\n        attrs[\"data-past\"] = token.time < currentTime;\n      }\n      attrs.style = `${token.color ? `color: ${token.color};` : \"\"}${token.bgColor ? `background-color: ${token.bgColor};` : \"\"}`;\n      const attributes = Object.entries(attrs).filter((v) => v[1]).map((v) => `${v[0]}=\"${v[1] === true ? \"\" : v[1]}\"`).join(\" \");\n      result += `<${token.tagName}${attributes ? \" \" + attributes : \"\"}>${renderVTTTokensString(\n        token.children\n      )}</${token.tagName}>`;\n    }\n  }\n  return result;\n}\nfunction updateTimedVTTCueNodes(root, currentTime) {\n  if (IS_SERVER)\n    return;\n  for (const el of root.querySelectorAll('[data-part=\"timed\"]')) {\n    const time = Number(el.getAttribute(\"data-time\"));\n    if (Number.isNaN(time))\n      continue;\n    if (time > currentTime)\n      setDataAttr(el, \"future\");\n    else\n      el.removeAttribute(\"data-future\");\n    if (time < currentTime)\n      setDataAttr(el, \"past\");\n    else\n      el.removeAttribute(\"data-past\");\n  }\n}\n\nfunction debounce(fn, delay) {\n  let timeout = null, args;\n  function run() {\n    clear();\n    fn(...args);\n    args = void 0;\n  }\n  function clear() {\n    clearTimeout(timeout);\n    timeout = null;\n  }\n  function debounce2() {\n    args = [].slice.call(arguments);\n    clear();\n    timeout = setTimeout(run, delay);\n  }\n  return debounce2;\n}\n\nconst STARTING_BOX = Symbol(\"STARTING_BOX\" );\nfunction createBox(box) {\n  if (box instanceof HTMLElement) {\n    return {\n      top: box.offsetTop,\n      width: box.clientWidth,\n      height: box.clientHeight,\n      left: box.offsetLeft,\n      right: box.offsetLeft + box.clientWidth,\n      bottom: box.offsetTop + box.clientHeight\n    };\n  }\n  return { ...box };\n}\nfunction moveBox(box, axis, delta) {\n  switch (axis) {\n    case \"+x\":\n      box.left += delta;\n      box.right += delta;\n      break;\n    case \"-x\":\n      box.left -= delta;\n      box.right -= delta;\n      break;\n    case \"+y\":\n      box.top += delta;\n      box.bottom += delta;\n      break;\n    case \"-y\":\n      box.top -= delta;\n      box.bottom -= delta;\n      break;\n  }\n}\nfunction isBoxCollision(a, b) {\n  return a.left <= b.right && a.right >= b.left && a.top <= b.bottom && a.bottom >= b.top;\n}\nfunction isAnyBoxCollision(box, boxes) {\n  for (let i = 0; i < boxes.length; i++)\n    if (isBoxCollision(box, boxes[i]))\n      return boxes[i];\n  return null;\n}\nfunction isWithinBox(container, box) {\n  return box.top >= 0 && box.bottom <= container.height && box.left >= 0 && box.right <= container.width;\n}\nfunction isBoxOutOfBounds(container, box, axis) {\n  switch (axis) {\n    case \"+x\":\n      return box.left < 0;\n    case \"-x\":\n      return box.right > container.width;\n    case \"+y\":\n      return box.top < 0;\n    case \"-y\":\n      return box.bottom > container.height;\n  }\n}\nfunction calcBoxIntersectPercentage(container, box) {\n  const x = Math.max(0, Math.min(container.width, box.right) - Math.max(0, box.left)), y = Math.max(0, Math.min(container.height, box.bottom) - Math.max(0, box.top)), intersectArea = x * y;\n  return intersectArea / (container.height * container.width);\n}\nfunction createCSSBox(container, box) {\n  return {\n    top: box.top / container.height,\n    left: box.left / container.width,\n    right: (container.width - box.right) / container.width,\n    bottom: (container.height - box.bottom) / container.height\n  };\n}\nfunction resolveRelativeBox(container, box) {\n  box.top = box.top * container.height;\n  box.left = box.left * container.width;\n  box.right = container.width - box.right * container.width;\n  box.bottom = container.height - box.bottom * container.height;\n  return box;\n}\nconst BOX_SIDES = [\"top\", \"left\", \"right\", \"bottom\"];\nfunction setBoxCSSVars(el, container, box, prefix) {\n  const cssBox = createCSSBox(container, box);\n  for (const side of BOX_SIDES) {\n    setCSSVar(el, `${prefix}-${side}`, cssBox[side] * 100 + \"%\");\n  }\n}\nfunction avoidBoxCollisions(container, box, boxes, axis) {\n  let percentage = 1, positionedBox, startBox = { ...box };\n  for (let i = 0; i < axis.length; i++) {\n    while (isBoxOutOfBounds(container, box, axis[i]) || isWithinBox(container, box) && isAnyBoxCollision(box, boxes)) {\n      moveBox(box, axis[i], 1);\n    }\n    if (isWithinBox(container, box))\n      return box;\n    const intersection = calcBoxIntersectPercentage(container, box);\n    if (percentage > intersection) {\n      positionedBox = { ...box };\n      percentage = intersection;\n    }\n    box = { ...startBox };\n  }\n  return positionedBox || startBox;\n}\n\nconst POSITION_OVERRIDE = Symbol(\"POSITION_OVERRIDE\" );\nfunction positionCue(container, cue, displayEl, boxes) {\n  let cueEl = displayEl.firstElementChild, line = computeCueLine(cue), displayBox, axis = [];\n  if (!displayEl[STARTING_BOX]) {\n    displayEl[STARTING_BOX] = createStartingBox(container, displayEl);\n  }\n  displayBox = resolveRelativeBox(container, { ...displayEl[STARTING_BOX] });\n  if (displayEl[POSITION_OVERRIDE]) {\n    axis = [displayEl[POSITION_OVERRIDE] === \"top\" ? \"+y\" : \"-y\", \"+x\", \"-x\"];\n  } else if (cue.snapToLines) {\n    let size;\n    switch (cue.vertical) {\n      case \"\":\n        axis = [\"+y\", \"-y\"];\n        size = \"height\";\n        break;\n      case \"rl\":\n        axis = [\"+x\", \"-x\"];\n        size = \"width\";\n        break;\n      case \"lr\":\n        axis = [\"-x\", \"+x\"];\n        size = \"width\";\n        break;\n    }\n    let step = getLineHeight(cueEl), position = step * Math.round(line), maxPosition = container[size] + step, initialAxis = axis[0];\n    if (Math.abs(position) > maxPosition) {\n      position = position < 0 ? -1 : 1;\n      position *= Math.ceil(maxPosition / step) * step;\n    }\n    if (line < 0) {\n      position += cue.vertical === \"\" ? container.height : container.width;\n      axis = axis.reverse();\n    }\n    moveBox(displayBox, initialAxis, position);\n  } else {\n    const isHorizontal = cue.vertical === \"\", posAxis = isHorizontal ? \"+y\" : \"+x\", size = isHorizontal ? displayBox.height : displayBox.width;\n    moveBox(\n      displayBox,\n      posAxis,\n      (isHorizontal ? container.height : container.width) * line / 100\n    );\n    moveBox(\n      displayBox,\n      posAxis,\n      cue.lineAlign === \"center\" ? size / 2 : cue.lineAlign === \"end\" ? size : 0\n    );\n    axis = isHorizontal ? [\"-y\", \"+y\", \"-x\", \"+x\"] : [\"-x\", \"+x\", \"-y\", \"+y\"];\n  }\n  displayBox = avoidBoxCollisions(container, displayBox, boxes, axis);\n  setBoxCSSVars(displayEl, container, displayBox, \"cue\");\n  return displayBox;\n}\nfunction createStartingBox(container, cueEl) {\n  const box = createBox(cueEl), pos = getStyledPositions(cueEl);\n  cueEl[POSITION_OVERRIDE] = false;\n  if (pos.top) {\n    box.top = pos.top;\n    box.bottom = pos.top + box.height;\n    cueEl[POSITION_OVERRIDE] = \"top\";\n  }\n  if (pos.bottom) {\n    const bottom = container.height - pos.bottom;\n    box.top = bottom - box.height;\n    box.bottom = bottom;\n    cueEl[POSITION_OVERRIDE] = \"bottom\";\n  }\n  if (pos.left)\n    box.left = pos.left;\n  if (pos.right)\n    box.right = container.width - pos.right;\n  return createCSSBox(container, box);\n}\nfunction getStyledPositions(el) {\n  const positions = {};\n  for (const side of BOX_SIDES) {\n    positions[side] = parseFloat(el.style.getPropertyValue(`--cue-${side}`));\n  }\n  return positions;\n}\nfunction computeCueLine(cue) {\n  if (cue.line === \"auto\") {\n    if (!cue.snapToLines) {\n      return 100;\n    } else {\n      return -1;\n    }\n  }\n  return cue.line;\n}\nfunction computeCuePosition(cue) {\n  if (cue.position === \"auto\") {\n    switch (cue.align) {\n      case \"start\":\n      case \"left\":\n        return 0;\n      case \"right\":\n      case \"end\":\n        return 100;\n      default:\n        return 50;\n    }\n  }\n  return cue.position;\n}\nfunction computeCuePositionAlignment(cue, dir) {\n  if (cue.positionAlign === \"auto\") {\n    switch (cue.align) {\n      case \"start\":\n        return dir === \"ltr\" ? \"line-left\" : \"line-right\";\n      case \"end\":\n        return dir === \"ltr\" ? \"line-right\" : \"line-left\";\n      case \"center\":\n        return \"center\";\n      default:\n        return `line-${cue.align}`;\n    }\n  }\n  return cue.positionAlign;\n}\n\nconst REGION_AXIS = [\"-y\", \"+y\", \"-x\", \"+x\"];\nfunction positionRegion(container, region, regionEl, boxes) {\n  let cues = Array.from(regionEl.querySelectorAll('[data-part=\"cue-display\"]')), height = 0, limit = Math.max(0, cues.length - region.lines);\n  for (let i = cues.length - 1; i >= limit; i--) {\n    height += cues[i].offsetHeight;\n  }\n  setCSSVar(regionEl, \"region-height\", height + \"px\");\n  if (!regionEl[STARTING_BOX]) {\n    regionEl[STARTING_BOX] = createCSSBox(container, createBox(regionEl));\n  }\n  let box = { ...regionEl[STARTING_BOX] };\n  box = resolveRelativeBox(container, box);\n  box.width = regionEl.clientWidth;\n  box.height = height;\n  box.right = box.left + box.width;\n  box.bottom = box.top + height;\n  box = avoidBoxCollisions(container, box, boxes, REGION_AXIS);\n  setBoxCSSVars(regionEl, container, box, \"region\");\n  return box;\n}\n\nclass CaptionsRenderer {\n  overlay;\n  _overlayBox;\n  _currentTime = 0;\n  _dir = \"ltr\";\n  _activeCues = [];\n  _isResizing = false;\n  _resizeObserver;\n  _regions = /* @__PURE__ */ new Map();\n  _cues = /* @__PURE__ */ new Map();\n  /* Text direction. */\n  get dir() {\n    return this._dir;\n  }\n  set dir(dir) {\n    this._dir = dir;\n    setDataAttr(this.overlay, \"dir\", dir);\n  }\n  get currentTime() {\n    return this._currentTime;\n  }\n  set currentTime(time) {\n    this._currentTime = time;\n    this.update();\n  }\n  constructor(overlay, init) {\n    this.overlay = overlay;\n    this.dir = init?.dir ?? \"ltr\";\n    overlay.setAttribute(\"translate\", \"yes\");\n    overlay.setAttribute(\"aria-live\", \"off\");\n    overlay.setAttribute(\"aria-atomic\", \"true\");\n    setPartAttr(overlay, \"captions\");\n    this._updateOverlay();\n    this._resizeObserver = new ResizeObserver(this._resizing.bind(this));\n    this._resizeObserver.observe(overlay);\n  }\n  changeTrack({ regions, cues }) {\n    this.reset();\n    this._buildRegions(regions);\n    for (const cue of cues)\n      this._cues.set(cue, null);\n    this.update();\n  }\n  addCue(cue) {\n    this._cues.set(cue, null);\n    this.update();\n  }\n  removeCue(cue) {\n    this._cues.delete(cue);\n    this.update();\n  }\n  update(forceUpdate = false) {\n    this._render(forceUpdate);\n  }\n  reset() {\n    this._cues.clear();\n    this._regions.clear();\n    this._activeCues = [];\n    this.overlay.textContent = \"\";\n  }\n  destroy() {\n    this.reset();\n    this._resizeObserver.disconnect();\n  }\n  _resizing() {\n    this._isResizing = true;\n    this._resize();\n  }\n  _resize = debounce(() => {\n    this._isResizing = false;\n    this._updateOverlay();\n    for (const el of this._regions.values()) {\n      el[STARTING_BOX] = null;\n    }\n    for (const el of this._cues.values()) {\n      if (el)\n        el[STARTING_BOX] = null;\n    }\n    this._render(true);\n  }, 50);\n  _updateOverlay() {\n    this._overlayBox = createBox(this.overlay);\n    setCSSVar(this.overlay, \"overlay-width\", this._overlayBox.width + \"px\");\n    setCSSVar(this.overlay, \"overlay-height\", this._overlayBox.height + \"px\");\n  }\n  _render(forceUpdate = false) {\n    if (!this._cues.size || this._isResizing)\n      return;\n    let cue, activeCues = [...this._cues.keys()].filter((cue2) => this._currentTime >= cue2.startTime && this._currentTime <= cue2.endTime).sort(\n      (cueA, cueB) => cueA.startTime !== cueB.startTime ? cueA.startTime - cueB.startTime : cueA.endTime - cueB.endTime\n    ), activeRegions = activeCues.map((cue2) => cue2.region);\n    for (let i = 0; i < this._activeCues.length; i++) {\n      cue = this._activeCues[i];\n      if (activeCues[i] === cue)\n        continue;\n      if (cue.region && !activeRegions.includes(cue.region)) {\n        const regionEl = this._regions.get(cue.region.id);\n        if (regionEl) {\n          regionEl.removeAttribute(\"data-active\");\n          forceUpdate = true;\n        }\n      }\n      const cueEl = this._cues.get(cue);\n      if (cueEl) {\n        cueEl.remove();\n        forceUpdate = true;\n      }\n    }\n    for (let i = 0; i < activeCues.length; i++) {\n      cue = activeCues[i];\n      let cueEl = this._cues.get(cue);\n      if (!cueEl)\n        this._cues.set(cue, cueEl = this._createCueElement(cue));\n      const regionEl = this._hasRegion(cue) && this._regions.get(cue.region.id);\n      if (regionEl && !regionEl.hasAttribute(\"data-active\")) {\n        requestAnimationFrame(() => setDataAttr(regionEl, \"active\"));\n        forceUpdate = true;\n      }\n      if (!cueEl.isConnected) {\n        (regionEl || this.overlay).append(cueEl);\n        forceUpdate = true;\n      }\n    }\n    if (forceUpdate) {\n      const boxes = [], seen = /* @__PURE__ */ new Set();\n      for (let i = activeCues.length - 1; i >= 0; i--) {\n        cue = activeCues[i];\n        if (seen.has(cue.region || cue))\n          continue;\n        const isRegion = this._hasRegion(cue), el = isRegion ? this._regions.get(cue.region.id) : this._cues.get(cue);\n        if (isRegion) {\n          boxes.push(positionRegion(this._overlayBox, cue.region, el, boxes));\n        } else {\n          boxes.push(positionCue(this._overlayBox, cue, el, boxes));\n        }\n        seen.add(isRegion ? cue.region : cue);\n      }\n    }\n    updateTimedVTTCueNodes(this.overlay, this._currentTime);\n    this._activeCues = activeCues;\n  }\n  _buildRegions(regions) {\n    if (!regions)\n      return;\n    for (const region of regions) {\n      const el = this._createRegionElement(region);\n      this._regions.set(region.id, el);\n      this.overlay.append(el);\n    }\n  }\n  _createRegionElement(region) {\n    const el = document.createElement(\"div\");\n    setPartAttr(el, \"region\");\n    setDataAttr(el, \"id\", region.id);\n    setDataAttr(el, \"scroll\", region.scroll);\n    setCSSVar(el, \"region-width\", region.width + \"%\");\n    setCSSVar(el, \"region-anchor-x\", region.regionAnchorX);\n    setCSSVar(el, \"region-anchor-y\", region.regionAnchorY);\n    setCSSVar(el, \"region-viewport-anchor-x\", region.viewportAnchorX);\n    setCSSVar(el, \"region-viewport-anchor-y\", region.viewportAnchorY);\n    setCSSVar(el, \"region-lines\", region.lines);\n    return el;\n  }\n  _createCueElement(cue) {\n    const display = document.createElement(\"div\"), position = computeCuePosition(cue), positionAlignment = computeCuePositionAlignment(cue, this._dir);\n    setPartAttr(display, \"cue-display\");\n    if (cue.vertical !== \"\")\n      setDataAttr(display, \"vertical\");\n    setCSSVar(display, \"cue-text-align\", cue.align);\n    if (cue.style) {\n      for (const prop of Object.keys(cue.style)) {\n        display.style.setProperty(prop, cue.style[prop]);\n      }\n    }\n    if (!this._hasRegion(cue)) {\n      setCSSVar(\n        display,\n        \"cue-writing-mode\",\n        cue.vertical === \"\" ? \"horizontal-tb\" : cue.vertical === \"lr\" ? \"vertical-lr\" : \"vertical-rl\"\n      );\n      if (!cue.style?.[\"--cue-width\"]) {\n        let maxSize = position;\n        if (positionAlignment === \"line-left\") {\n          maxSize = 100 - position;\n        } else if (positionAlignment === \"center\" && position <= 50) {\n          maxSize = position * 2;\n        } else if (positionAlignment === \"center\" && position > 50) {\n          maxSize = (100 - position) * 2;\n        }\n        const size = cue.size < maxSize ? cue.size : maxSize;\n        if (cue.vertical === \"\")\n          setCSSVar(display, \"cue-width\", size + \"%\");\n        else\n          setCSSVar(display, \"cue-height\", size + \"%\");\n      }\n    } else {\n      setCSSVar(\n        display,\n        \"cue-offset\",\n        `${position - (positionAlignment === \"line-right\" ? 100 : positionAlignment === \"center\" ? 50 : 0)}%`\n      );\n    }\n    const el = document.createElement(\"div\");\n    setPartAttr(el, \"cue\");\n    if (cue.id)\n      setDataAttr(el, \"id\", cue.id);\n    el.innerHTML = renderVTTCueString(cue);\n    display.append(el);\n    return display;\n  }\n  _hasRegion(cue) {\n    return cue.region && cue.size === 100 && cue.vertical === \"\" && cue.line === \"auto\";\n  }\n}\n\nexport { CaptionsRenderer as C, ParseError as P, TextCue as T, VTTParser as V, VTTBlock as a, VTTCue as b, ParseErrorCode as c, parseResponse as d, parseByteStream as e, parseText as f, parseTextStream as g, VTTRegion as h, createVTTCueTemplate as i, renderVTTTokensString as j, parseVTTTimestamp as p, renderVTTCueString as r, tokenizeVTTCue as t, updateTimedVTTCueNodes as u };\n"], "mappings": ";;;;;AAAA,IAAM,iBAAiB;AAAA,EACrB,UAAU;AAAA,EACV,cAAc;AAAA,EACd,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,gBAAgB;AAClB;AACA,IAAM,aAAN,cAAyB,MAAM;AAAA,EAG7B,YAAY,MAAM;AAChB,UAAM,KAAK,MAAM;AAHnB;AACA;AAGE,SAAK,OAAO,KAAK;AACjB,SAAK,OAAO,KAAK;AAAA,EACnB;AACF;AAEA,IAAM,qBAAqB;AAC3B,IAAM,0BAAN,MAA8B;AAAA,EAG5B,YAAY,UAAU;AAFtB;AACA;AAEE,UAAM,cAAc,IAAI,uBAAuB,QAAQ;AACvD,SAAK,WAAW,IAAI,eAAe;AAAA,MACjC,MAAM,OAAO;AACX,oBAAY,UAAU,KAAK;AAAA,MAC7B;AAAA,MACA,QAAQ;AACN,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF,CAAC;AACD,SAAK,WAAW,IAAI,eAAe;AAAA,MACjC,MAAM,YAAY;AAChB,oBAAY,SAAS,CAAC,SAAS,WAAW,QAAQ,IAAI;AACtD,oBAAY,UAAU,MAAM,WAAW,MAAM;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAM,yBAAN,MAA6B;AAAA,EAK3B,YAAY,UAAU;AAJtB,mCAAU;AACV;AACA;AACA;AAEE,SAAK,WAAW,IAAI,YAAY,QAAQ;AAAA,EAC1C;AAAA,EACA,UAAU,OAAO;AACf,SAAK,WAAW,KAAK,SAAS,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC;AAC5D,UAAM,QAAQ,KAAK,QAAQ,MAAM,kBAAkB;AACnD,SAAK,UAAU,MAAM,IAAI,KAAK;AAC9B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAChC,WAAK,OAAO,MAAM,CAAC,EAAE,KAAK,CAAC;AAAA,EAC/B;AAAA,EACA,QAAQ;AACN,QAAI,KAAK;AACP,WAAK,OAAO,KAAK,QAAQ,KAAK,CAAC;AACjC,SAAK,UAAU;AACf,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,eAAe,UAAU,MAAM,SAAS;AACtC,QAAM,SAAS,IAAI,eAAe;AAAA,IAChC,MAAM,YAAY;AAChB,YAAM,QAAQ,KAAK,MAAM,kBAAkB;AAC3C,iBAAW,QAAQ;AACjB,mBAAW,QAAQ,IAAI;AACzB,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO,gBAAgB,QAAQ,OAAO;AACxC;AACA,eAAe,gBAAgB,QAAQ,SAAS;AAC9C,QAAM,QAAO,mCAAS,SAAQ;AAC9B,MAAI;AACJ,MAAI,OAAO,SAAS,UAAU;AAC5B,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,mBAAW,MAAM,OAAO,0BAAiB,GAAG;AAC5C;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,mBAAW,MAAM,OAAO,0BAAiB,GAAG;AAC5C;AAAA,MACF;AACE,mBAAW,MAAM,QAAQ,QAAQ,EAAE,KAAK,WAAY;AAAE,iBAAO;AAAA,QAAW,CAAC,GAAG;AAAA,IAChF;AAAA,EACF,OAAO;AACL,cAAU;AAAA,EACZ;AACA,MAAI;AACJ,QAAM,SAAS,OAAO,UAAU,GAAG,SAAS,QAAQ,GAAG,UAAS,mCAAS,YAAW,SAAS,CAAC,EAAC,mCAAS,WAAU,CAAC,EAAC,mCAAS;AAC7H,QAAM,OAAO,KAAK;AAAA,IAChB,QAAQ;AAAA,IACR,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,SAAS;AACP,aAAO,OAAO;AACd,eAAS,OAAO,KAAK,IAAI;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,MAAI,IAAI;AACR,SAAO,MAAM;AACX,UAAM,EAAE,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK;AAC1C,QAAI,MAAM;AACR,aAAO,MAAM,IAAI,CAAC;AAClB,eAAS,OAAO,KAAK,KAAK;AAC1B;AAAA,IACF;AACA,WAAO,MAAM,OAAO,CAAC;AACrB;AAAA,EACF;AACA,SAAO;AACT;AAEA,eAAe,cAAc,UAAU,SAAS;AAtHhD;AAuHE,QAAM,MAAM,MAAM;AAClB,MAAI,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM;AACxB,QAAI;AACJ;AACE,cAAQ,IAAI,WAAW;AAAA,QACrB,MAAM,eAAe;AAAA,QACrB,QAAQ,CAAC,IAAI,KAAK,+BAA+B,IAAI,MAAM,MAAM,qCAAqC,IAAI,MAAM;AAAA,QAChH,MAAM;AAAA,MACR,CAAC;AACD,+CAAS,YAAT,iCAAmB;AAAA,IACrB;AACA,WAAO;AAAA,MACL,UAAU,CAAC;AAAA,MACX,MAAM,CAAC;AAAA,MACP,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC,KAAK;AAAA,IAChB;AAAA,EACF;AACA,QAAM,cAAc,IAAI,QAAQ,IAAI,cAAc,KAAK,IAAI,QAAO,iBAAY,MAAM,oBAAoB,MAAtC,mBAA0C,IAAI,YAAW,iBAAY,MAAM,sBAAsB,MAAxC,mBAA4C;AACvK,SAAO,gBAAgB,IAAI,MAAM,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC;AACjE;AACA,eAAe,gBAAgB,QAAQ,EAAE,WAAW,SAAS,GAAG,QAAQ,IAAI,CAAC,GAAG;AAC9E,QAAM,aAAa,OAAO,YAAY,IAAI,wBAAwB,QAAQ,CAAC;AAC3E,SAAO,gBAAgB,YAAY,OAAO;AAC5C;AAEA,IAAM,UAAN,cAAsB,YAAY;AAAA,EA+BhC,YAAY,WAAW,SAAS,MAAM;AACpC,UAAM;AA1BR;AAAA;AAAA;AAAA;AAAA;AAAA,8BAAK;AAML;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAAc;AAGZ,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACd;AAAA,EACA,iBAAiB,MAAM,UAAU,SAAS;AACxC,UAAM,iBAAiB,MAAM,UAAU,OAAO;AAAA,EAChD;AAAA,EACA,oBAAoB,MAAM,UAAU,SAAS;AAC3C,UAAM,oBAAoB,MAAM,UAAU,OAAO;AAAA,EACnD;AACF;AAEA,IAAM,YAAY,OAAO,aAAa;AAEtC,IAAM,UAAU,YAAY,UAAU,OAAO;AAC7C,IAAM,SAAN,cAAqB,QAAQ;AAAA,EAA7B;AAAA;AAOE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCAAS;AAMT;AAAA;AAAA;AAAA;AAAA;AAAA,oCAAW;AAOX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAAc;AAOd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAAO;AAMP;AAAA;AAAA;AAAA;AAAA;AAAA,qCAAY;AAQZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAAW;AAOX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yCAAgB;AAMhB;AAAA;AAAA;AAAA;AAAA;AAAA,gCAAO;AAMP;AAAA;AAAA;AAAA;AAAA;AAAA,iCAAQ;AAIR;AAAA;AAAA;AAAA;AAAA;AACF;AAEA,IAAM,YAAN,MAAgB;AAAA,EAAhB;AAIE;AAAA;AAAA;AAAA,8BAAK;AAIL;AAAA;AAAA;AAAA,iCAAQ;AAIR;AAAA;AAAA;AAAA,iCAAQ;AAIR;AAAA;AAAA;AAAA,yCAAgB;AAIhB;AAAA;AAAA;AAAA,yCAAgB;AAIhB;AAAA;AAAA;AAAA,2CAAkB;AAIlB;AAAA;AAAA;AAAA,2CAAkB;AAIlB;AAAA;AAAA;AAAA,kCAAS;AAAA;AACX;AAEA,IAAM,UAAU;AAAhB,IAAqB,iBAAiB;AACtC,SAAS,SAAS,MAAM;AACtB,QAAM,MAAM,SAAS,MAAM,EAAE;AAC7B,SAAO,CAAC,OAAO,MAAM,GAAG,IAAI,MAAM;AACpC;AACA,SAAS,aAAa,MAAM;AAC1B,QAAM,MAAM,SAAS,KAAK,QAAQ,gBAAgB,EAAE,GAAG,EAAE;AACzD,SAAO,CAAC,OAAO,MAAM,GAAG,KAAK,OAAO,KAAK,OAAO,MAAM,MAAM;AAC9D;AACA,SAAS,SAAS,MAAM;AACtB,MAAI,CAAC,KAAK,SAAS,OAAO;AACxB,WAAO;AACT,QAAM,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,OAAO,EAAE,IAAI,YAAY;AACnD,SAAO,MAAM,QAAQ,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI;AAC7C;AACA,SAAS,QAAQ,MAAM;AACrB,QAAM,MAAM,WAAW,IAAI;AAC3B,SAAO,CAAC,OAAO,MAAM,GAAG,IAAI,MAAM;AACpC;AAEA,IAAM,eAAe;AAArB,IAA+B,QAAQ;AAAvC,IAA4C,eAAe;AAA3D,IAAgE,iBAAiB;AAAjF,IAAyF,kBAAkB;AAA3G,IAAqK,mBAAmB;AAAxL,IAAgM,qBAAqB;AAArN,IAA+N,wBAAwB;AAAvP,IAA2Q,WAAW;AAAtR,IAAiS,gBAAgB;AAAjT,IAAwT,mBAAmB;AAA3U,IAAgW,WAAW;AAA3W,IAA0Y,gBAAgB;AAA1Z,IAA8a,eAAe;AAA7b,IAAge,eAAe;AAC/e,IAAI,YAA4B,CAAC,cAAc;AAC7C,YAAU,UAAU,MAAM,IAAI,CAAC,IAAI;AACnC,YAAU,UAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,YAAU,UAAU,KAAK,IAAI,CAAC,IAAI;AAClC,YAAU,UAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,YAAU,UAAU,MAAM,IAAI,CAAC,IAAI;AACnC,SAAO;AACT,GAAG,YAAY,CAAC,CAAC;AACjB,IAAM,YAAN,MAAgB;AAAA,EAAhB;AACE;AACA,kCAAS;AACT,qCAAY,CAAC;AACb,oCAAW,CAAC;AACZ,iCAAQ,CAAC;AACT,gCAAO;AACP,mCAAU;AACV,mCAAU,CAAC;AACX;AACA,qCAAY;AAAA;AAAA,EACZ,MAAM,KAAK,MAAM;AACf,SAAK,QAAQ;AACb,QAAI,KAAK;AACP,WAAK,SAAS;AAChB,QAAI,KAAK;AACP,WAAK,iBAAiB,MAAM,OAAO,sBAAa,GAAG;AAAA,EACvD;AAAA,EACA,MAAM,MAAM,WAAW;AAtVzB;AAuVI,QAAI,SAAS,IAAI;AACf,UAAI,KAAK,MAAM;AACb,aAAK,MAAM,KAAK,KAAK,IAAI;AACzB,yBAAK,OAAM,UAAX,4BAAmB,KAAK;AACxB,aAAK,OAAO;AAAA,MACd,WAAW,KAAK,SAAS;AACvB,aAAK,SAAS,KAAK,QAAQ,EAAE,IAAI,KAAK;AACtC,yBAAK,OAAM,aAAX,4BAAsB,KAAK;AAC3B,aAAK,UAAU;AAAA,MACjB,WAAW,KAAK,WAAW,GAAgB;AACzC,aAAK,aAAa,MAAM,SAAS;AACjC,yBAAK,OAAM,qBAAX,4BAA8B,KAAK;AAAA,MACrC;AACA,WAAK,SAAS;AAAA,IAChB,WAAW,KAAK,QAAQ;AACtB,cAAQ,KAAK,QAAQ;AAAA,QACnB,KAAK;AACH,eAAK,aAAa,MAAM,SAAS;AACjC;AAAA,QACF,KAAK;AACH,cAAI,KAAK,MAAM;AACb,kBAAM,UAAU,KAAK,KAAK,KAAK,SAAS;AACxC,gBAAI,CAAC,WAAW,gBAAgB,KAAK,IAAI,GAAG;AAC1C,mBAAK,kBAAkB,KAAK,MAAM,QAAQ,GAAG,SAAS;AAAA,YACxD,OAAO;AACL,mBAAK,KAAK,SAAS,UAAU,OAAO,MAAM;AAAA,YAC5C;AAAA,UACF;AACA;AAAA,QACF,KAAK;AACH,eAAK,qBAAqB,KAAK,MAAM,QAAQ,GAAG,SAAS;AACzD;AAAA,MACJ;AAAA,IACF,WAAW,KAAK,WAAW,gBAAgB,GAAG;AAC5C,WAAK,SAAS;AAAA,IAChB,WAAW,KAAK,WAAW,kBAAkB,GAAG;AAC9C,WAAK,SAAS;AACd,WAAK,UAAU,IAAI,UAAU;AAC7B,WAAK,qBAAqB,KAAK,QAAQ,uBAAuB,EAAE,EAAE,MAAM,QAAQ,GAAG,SAAS;AAAA,IAC9F,WAAW,KAAK,SAAS,aAAa,GAAG;AACvC,YAAM,SAAS,KAAK,gBAAgB,MAAM,SAAS;AACnD,UAAI,QAAQ;AACV,aAAK,OAAO,IAAI,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,EAAE;AAC/C,aAAK,KAAK,KAAK,KAAK;AACpB,aAAK,kBAAkB,OAAO,CAAC,GAAG,SAAS;AAAA,MAC7C;AACA,WAAK,SAAS;AAAA,IAChB,WAAW,cAAc,GAAG;AAC1B,WAAK,aAAa,MAAM,SAAS;AAAA,IACnC;AACA,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO;AACL,WAAO;AAAA,MACL,UAAU,KAAK;AAAA,MACf,MAAM,KAAK;AAAA,MACX,SAAS,OAAO,OAAO,KAAK,QAAQ;AAAA,MACpC,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AAAA,EACA,aAAa,MAAM,WAAW;AAnZhC;AAoZI,QAAI,YAAY,GAAG;AACjB,UAAI,eAAe,KAAK,IAAI,GAAG;AAC7B,cAAM,CAAC,KAAK,KAAK,IAAI,KAAK,MAAM,cAAc;AAC9C,YAAI;AACF,eAAK,UAAU,GAAG,KAAK,SAAS,IAAI,QAAQ,UAAU,EAAE;AAAA,MAC5D;AAAA,IACF,WAAW,KAAK,WAAW,YAAY,GAAG;AACxC,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,WAAK,cAAa,UAAK,kBAAL,mBAAoB,eAAe;AAAA,IACvD;AAAA,EACF;AAAA,EACA,gBAAgB,MAAM,WAAW;AAhanC;AAiaI,UAAM,CAAC,eAAe,eAAe,EAAE,IAAI,KAAK,MAAM,gBAAgB,GAAG,CAAC,aAAa,GAAG,YAAY,IAAI,aAAa,MAAM,QAAQ,GAAG,YAAY,kBAAkB,aAAa,GAAG,UAAU,kBAAkB,WAAW;AAC7N,QAAI,cAAc,QAAQ,YAAY,QAAQ,UAAU,WAAW;AACjE,aAAO,CAAC,WAAW,SAAS,YAAY;AAAA,IAC1C,OAAO;AACL,UAAI,cAAc,MAAM;AACtB,aAAK,cAAa,UAAK,kBAAL,mBAAoB,mBAAmB,eAAe,UAAU;AAAA,MACpF;AACA,UAAI,YAAY,MAAM;AACpB,aAAK,cAAa,UAAK,kBAAL,mBAAoB,iBAAiB,aAAa,UAAU;AAAA,MAChF;AACA,UAAI,aAAa,QAAQ,YAAY,QAAQ,UAAU,WAAW;AAChE,aAAK,cAAa,UAAK,kBAAL,mBAAoB,mBAAmB,WAAW,SAAS,UAAU;AAAA,MACzF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,UAAU,MAAM;AAnbvC;AAobI,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAI,eAAe,KAAK,SAAS,CAAC,CAAC,GAAG;AACpC,mBAAW;AACX,cAAM,CAAC,MAAM,KAAK,IAAI,SAAS,CAAC,EAAE,MAAM,cAAc;AACtD,gBAAQ,MAAM;AAAA,UACZ,KAAK;AACH,iBAAK,QAAQ,KAAK;AAClB;AAAA,UACF,KAAK;AACH,kBAAM,QAAQ,aAAa,KAAK;AAChC,gBAAI,UAAU;AACZ,mBAAK,QAAQ,QAAQ;AAAA;AAErB,yBAAW;AACb;AAAA,UACF,KAAK;AACH,kBAAM,QAAQ,SAAS,KAAK;AAC5B,gBAAI,UAAU;AACZ,mBAAK,QAAQ,QAAQ;AAAA;AAErB,yBAAW;AACb;AAAA,UACF,KAAK;AACH,kBAAM,SAAS,SAAS,KAAK;AAC7B,gBAAI,WAAW,MAAM;AACnB,mBAAK,QAAQ,gBAAgB,OAAO,CAAC;AACrC,mBAAK,QAAQ,gBAAgB,OAAO,CAAC;AAAA,YACvC;AACE,yBAAW;AACb;AAAA,UACF,KAAK;AACH,kBAAM,WAAW,SAAS,KAAK;AAC/B,gBAAI,aAAa,MAAM;AACrB,mBAAK,QAAQ,kBAAkB,SAAS,CAAC;AACzC,mBAAK,QAAQ,kBAAkB,SAAS,CAAC;AAAA,YAC3C;AACE,yBAAW;AACb;AAAA,UACF,KAAK;AACH,gBAAI,UAAU;AACZ,mBAAK,QAAQ,SAAS;AAAA;AAEtB,yBAAW;AACb;AAAA,UACF;AACE,iBAAK,cAAa,UAAK,kBAAL,mBAAoB,sBAAsB,MAAM,OAAO,KAAK;AAAA,QAClF;AACA,YAAI,UAAU;AACZ,eAAK,cAAa,UAAK,kBAAL,mBAAoB,kBAAkB,MAAM,OAAO,KAAK;AAAA,QAC5E;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,UAAU,MAAM;AA7epC;AA8eI,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAW;AACX,UAAI,eAAe,KAAK,SAAS,CAAC,CAAC,GAAG;AACpC,cAAM,CAAC,MAAM,KAAK,IAAI,SAAS,CAAC,EAAE,MAAM,cAAc;AACtD,gBAAQ,MAAM;AAAA,UACZ,KAAK;AACH,kBAAM,SAAS,KAAK,SAAS,KAAK;AAClC,gBAAI;AACF,mBAAK,KAAK,SAAS;AACrB;AAAA,UACF,KAAK;AACH,gBAAI,UAAU,QAAQ,UAAU,MAAM;AACpC,mBAAK,KAAK,WAAW;AACrB,mBAAK,KAAK,SAAS;AAAA,YACrB;AACE,yBAAW;AACb;AAAA,UACF,KAAK;AACH,kBAAM,CAAC,SAAS,SAAS,IAAI,MAAM,MAAM,KAAK;AAC9C,gBAAI,QAAQ,SAAS,YAAY,GAAG;AAClC,oBAAM,aAAa,aAAa,OAAO;AACvC,kBAAI,eAAe,MAAM;AACvB,qBAAK,KAAK,OAAO;AACjB,qBAAK,KAAK,cAAc;AAAA,cAC1B;AACE,2BAAW;AAAA,YACf,OAAO;AACL,oBAAM,SAAS,QAAQ,OAAO;AAC9B,kBAAI,WAAW;AACb,qBAAK,KAAK,OAAO;AAAA;AAEjB,2BAAW;AAAA,YACf;AACA,gBAAI,cAAc,KAAK,SAAS,GAAG;AACjC,mBAAK,KAAK,YAAY;AAAA,YACxB,WAAW,WAAW;AACpB,yBAAW;AAAA,YACb;AACA,gBAAI,KAAK,KAAK,SAAS;AACrB,mBAAK,KAAK,SAAS;AACrB;AAAA,UACF,KAAK;AACH,kBAAM,CAAC,QAAQ,QAAQ,IAAI,MAAM,MAAM,KAAK,GAAG,WAAW,aAAa,MAAM;AAC7E,gBAAI,aAAa;AACf,mBAAK,KAAK,WAAW;AAAA;AAErB,yBAAW;AACb,gBAAI,YAAY,aAAa,KAAK,QAAQ,GAAG;AAC3C,mBAAK,KAAK,gBAAgB;AAAA,YAC5B,WAAW,UAAU;AACnB,yBAAW;AAAA,YACb;AACA;AAAA,UACF,KAAK;AACH,kBAAM,OAAO,aAAa,KAAK;AAC/B,gBAAI,SAAS,MAAM;AACjB,mBAAK,KAAK,OAAO;AACjB,kBAAI,OAAO;AACT,qBAAK,KAAK,SAAS;AAAA,YACvB,OAAO;AACL,yBAAW;AAAA,YACb;AACA;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,KAAK,KAAK,GAAG;AACxB,mBAAK,KAAK,QAAQ;AAAA,YACpB,OAAO;AACL,yBAAW;AAAA,YACb;AACA;AAAA,UACF;AACE,iBAAK,cAAa,UAAK,kBAAL,mBAAoB,mBAAmB,MAAM,OAAO,KAAK;AAAA,QAC/E;AACA,YAAI,UAAU;AACZ,eAAK,cAAa,UAAK,kBAAL,mBAAoB,eAAe,MAAM,OAAO,KAAK;AAAA,QACzE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AA9jBtB;AA+jBI,QAAI,CAAC;AACH;AACF,SAAK,QAAQ,KAAK,KAAK;AACvB,QAAI,KAAK,MAAM,QAAQ;AACrB,WAAK,MAAM,OAAO;AAClB,YAAM;AAAA,IACR,OAAO;AACL,uBAAK,OAAM,YAAX,4BAAqB;AAAA,IACvB;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,WAAW;AACpC,QAAM,QAAQ,UAAU,MAAM,YAAY;AAC1C,MAAI,CAAC;AACH,WAAO;AACT,QAAM,QAAQ,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE,IAAI,GAAG,UAAU,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,UAAU,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,eAAe,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,EAAE,OAAO,GAAG,GAAG,GAAG,EAAE,IAAI,GAAG,QAAQ,QAAQ,OAAO,UAAU,KAAK,UAAU,eAAe;AAC7P,MAAI,QAAQ,KAAK,UAAU,KAAK,UAAU,KAAK,eAAe,KAAK,UAAU,MAAM,UAAU,IAAI;AAC/F,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,kBAAkB;AACzB,SAAO,IAAI,UAAU;AACvB;AAEA,IAAI,YAAyB,OAAO,OAAO;AAAA,EACzC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT;AACF,CAAC;AAED,IAAM,WAAW;AAAjB,IAA0B,iBAAiB;AAA3C,IAAsD,WAAW;AAAA,EAC/D,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,MAAM;AAAA,EACN,WAAW;AACb;AAVA,IAUG,gBAAgB;AAAA,EACjB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AACX;AAnBA,IAmBG,iBAAiB;AAnBpB,IAmBmE,SAAyB,oBAAI,IAAI;AAAA,EAClG;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AA5BD,IA4BI,cAA8B,IAAI,IAAI,OAAO,KAAK,QAAQ,CAAC;AAC/D,SAAS,eAAe,KAAK;AA7nB7B;AA8nBE,MAAI,SAAS,IAAI,OAAO,GAAc,SAAS,CAAC,GAAG,QAAQ,CAAC,GAAG;AAC/D,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK,QAAQ,KAAK;AACxC,UAAM,OAAO,IAAI,KAAK,CAAC;AACvB,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,YAAI,SAAS,KAAK;AAChB,kBAAQ;AACR,iBAAO;AAAA,QACT,OAAO;AACL,oBAAU;AAAA,QACZ;AACA;AAAA,MACF,KAAK;AACH,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,oBAAQ;AACR,mBAAO;AACP;AAAA,UACF,KAAK;AACH,oBAAQ;AACR,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,oBAAQ;AACR,mBAAO;AACP;AAAA,UACF;AACE,gBAAI,CAAC,UAAU,SAAS,KAAK,IAAI;AAC/B,qBAAO;AACT,sBAAU;AACV;AAAA,QACJ;AACA;AAAA,MACF,KAAK;AACH,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,qBAAS;AACT,gBAAI;AACF,yBAAK,UAAL,mBAAY;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,qBAAS;AACT;AAAA,UACF,KAAK;AACH,qBAAS;AACT,gBAAI;AACF,yBAAK,UAAL,mBAAY;AACd,mBAAO;AACP;AAAA,UACF;AACE,sBAAU;AAAA,QACd;AACA;AAAA,MACF,KAAK;AACH,YAAI,SAAS,KAAK;AAChB,mBAAS,OAAO,QAAQ,gBAAgB,GAAG;AAC3C,eAAI,6BAAM,UAAS;AACjB,iBAAK,QAAQ,oBAAoB,MAAM;AAAA,oBAChC,6BAAM,UAAS;AACtB,iBAAK,OAAO,oBAAoB,MAAM;AACxC,mBAAS;AACT,iBAAO;AAAA,QACT,OAAO;AACL,oBAAU;AAAA,QACZ;AACA;AAAA,MACF,KAAK;AACH,YAAI,SAAS,KAAK;AAChB,mBAAS;AACT,iBAAO,MAAM,IAAI;AACjB,iBAAO;AAAA,QACT;AACA;AAAA,MACF,KAAK;AACH,YAAI,SAAS,KAAK;AAChB,gBAAM,OAAO,kBAAkB,MAAM;AACrC,cAAI,SAAS,QAAQ,QAAQ,IAAI,aAAa,QAAQ,IAAI,SAAS;AACjE,qBAAS;AACT,oBAAQ;AACR,iBAAK,OAAO;AAAA,UACd;AACA,mBAAS;AACT,iBAAO;AAAA,QACT,OAAO;AACL,oBAAU;AAAA,QACZ;AACA;AAAA,IACJ;AAAA,EACF;AACA,WAAS,UAAU;AACjB,QAAI,YAAY,IAAI,MAAM,GAAG;AAC3B,YAAM,SAAS;AACf,aAAO,gBAAgB,MAAM;AAC7B,UAAI,QAAQ;AACV,YAAI,MAAM,MAAM,SAAS,CAAC,MAAM;AAC9B,gBAAM,KAAK,MAAM;AACnB,eAAO,SAAS,KAAK,IAAI;AAAA,MAC3B;AACE,eAAO,KAAK,IAAI;AAAA,IACpB;AACA,aAAS;AACT,WAAO;AAAA,EACT;AACA,WAAS,WAAW;AAClB,QAAI,QAAQ,QAAQ;AAClB,YAAM,QAAQ,OAAO,QAAQ,OAAO,EAAE;AACtC,UAAI,OAAO,IAAI,KAAK,GAAG;AACrB,aAAK,OAAO,WAAW,KAAK,IAAI,YAAY,OAAO,IAAI;AAAA,MACzD,OAAO;AACL,aAAK,QAAQ,CAAC,KAAK,QAAQ,SAAS,KAAK,QAAQ,MAAM;AAAA,MACzD;AAAA,IACF;AACA,aAAS;AAAA,EACX;AACA,WAAS,UAAU;AACjB,QAAI,CAAC;AACH;AACF,UAAM,OAAO,EAAE,MAAM,QAAQ,MAAM,oBAAoB,MAAM,EAAE;AAC/D,WAAO,KAAK,SAAS,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI;AAClD,aAAS;AAAA,EACX;AACA,MAAI,SAAS;AACX,YAAQ;AACV,SAAO;AACT;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO;AAAA,IACL,SAAS,SAAS,IAAI;AAAA,IACtB;AAAA,IACA,UAAU,CAAC;AAAA,EACb;AACF;AACA,SAAS,oBAAoB,MAAM;AACjC,SAAO,KAAK,QAAQ,gBAAgB,CAAC,WAAW,cAAc,MAAM,KAAK,GAAG;AAC9E;AAEA,SAAS,UAAU,IAAI,MAAM,OAAO;AAClC,KAAG,MAAM,YAAY,KAAK,IAAI,IAAI,QAAQ,EAAE;AAC9C;AACA,SAAS,YAAY,IAAI,MAAM,QAAQ,MAAM;AAC3C,KAAG,aAAa,QAAQ,IAAI,IAAI,UAAU,OAAO,KAAK,QAAQ,EAAE;AAClE;AACA,SAAS,YAAY,IAAI,MAAM;AAC7B,KAAG,aAAa,aAAa,IAAI;AACnC;AACA,SAAS,cAAc,IAAI;AACzB,SAAO,WAAW,iBAAiB,EAAE,EAAE,UAAU,KAAK;AACxD;AAEA,SAAS,qBAAqB,KAAK;AACjC,MAAI,WAAW;AACb,UAAM;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACA,QAAM,WAAW,SAAS,cAAc,UAAU;AAClD,WAAS,YAAY,mBAAmB,GAAG;AAC3C,SAAO,EAAE,KAAK,SAAS,SAAS,QAAQ;AAC1C;AACA,SAAS,mBAAmB,KAAK,cAAc,GAAG;AAChD,SAAO,sBAAsB,eAAe,GAAG,GAAG,WAAW;AAC/D;AACA,SAAS,sBAAsB,QAAQ,cAAc,GAAG;AACtD,MAAI,OAAO,SAAS;AACpB,aAAW,SAAS,QAAQ;AAC1B,QAAI,MAAM,SAAS,QAAQ;AACzB,gBAAU,MAAM;AAAA,IAClB,OAAO;AACL,YAAM,cAAc,MAAM,SAAS;AACnC,cAAQ,CAAC;AACT,YAAM,QAAQ,MAAM;AACpB,YAAM,QAAQ,MAAM,SAAS,OAAO,MAAM;AAC1C,YAAM,OAAO,MAAM,SAAS,UAAU,MAAM;AAC5C,YAAM,WAAW,IAAI,MAAM,SAAS,OAAO;AAC3C,UAAI,aAAa;AACf,cAAM,WAAW,IAAI;AACrB,cAAM,WAAW,IAAI,MAAM;AAC3B,cAAM,aAAa,IAAI,MAAM,OAAO;AACpC,cAAM,WAAW,IAAI,MAAM,OAAO;AAAA,MACpC;AACA,YAAM,QAAQ,GAAG,MAAM,QAAQ,UAAU,MAAM,KAAK,MAAM,EAAE,GAAG,MAAM,UAAU,qBAAqB,MAAM,OAAO,MAAM,EAAE;AACzH,YAAM,aAAa,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,MAAM,OAAO,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,KAAK,GAAG;AAC1H,gBAAU,IAAI,MAAM,OAAO,GAAG,aAAa,MAAM,aAAa,EAAE,IAAI;AAAA,QAClE,MAAM;AAAA,MACR,CAAC,KAAK,MAAM,OAAO;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,MAAM,aAAa;AACjD,MAAI;AACF;AACF,aAAW,MAAM,KAAK,iBAAiB,qBAAqB,GAAG;AAC7D,UAAM,OAAO,OAAO,GAAG,aAAa,WAAW,CAAC;AAChD,QAAI,OAAO,MAAM,IAAI;AACnB;AACF,QAAI,OAAO;AACT,kBAAY,IAAI,QAAQ;AAAA;AAExB,SAAG,gBAAgB,aAAa;AAClC,QAAI,OAAO;AACT,kBAAY,IAAI,MAAM;AAAA;AAEtB,SAAG,gBAAgB,WAAW;AAAA,EAClC;AACF;AAEA,SAAS,SAAS,IAAI,OAAO;AAC3B,MAAI,UAAU,MAAM;AACpB,WAAS,MAAM;AACb,UAAM;AACN,OAAG,GAAG,IAAI;AACV,WAAO;AAAA,EACT;AACA,WAAS,QAAQ;AACf,iBAAa,OAAO;AACpB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,WAAO,CAAC,EAAE,MAAM,KAAK,SAAS;AAC9B,UAAM;AACN,cAAU,WAAW,KAAK,KAAK;AAAA,EACjC;AACA,SAAO;AACT;AAEA,IAAM,eAAe,OAAO,cAAe;AAC3C,SAAS,UAAU,KAAK;AACtB,MAAI,eAAe,aAAa;AAC9B,WAAO;AAAA,MACL,KAAK,IAAI;AAAA,MACT,OAAO,IAAI;AAAA,MACX,QAAQ,IAAI;AAAA,MACZ,MAAM,IAAI;AAAA,MACV,OAAO,IAAI,aAAa,IAAI;AAAA,MAC5B,QAAQ,IAAI,YAAY,IAAI;AAAA,IAC9B;AAAA,EACF;AACA,SAAO,EAAE,GAAG,IAAI;AAClB;AACA,SAAS,QAAQ,KAAK,MAAM,OAAO;AACjC,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb;AAAA,IACF,KAAK;AACH,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb;AAAA,IACF,KAAK;AACH,UAAI,OAAO;AACX,UAAI,UAAU;AACd;AAAA,IACF,KAAK;AACH,UAAI,OAAO;AACX,UAAI,UAAU;AACd;AAAA,EACJ;AACF;AACA,SAAS,eAAe,GAAG,GAAG;AAC5B,SAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE;AACtF;AACA,SAAS,kBAAkB,KAAK,OAAO;AACrC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAChC,QAAI,eAAe,KAAK,MAAM,CAAC,CAAC;AAC9B,aAAO,MAAM,CAAC;AAClB,SAAO;AACT;AACA,SAAS,YAAY,WAAW,KAAK;AACnC,SAAO,IAAI,OAAO,KAAK,IAAI,UAAU,UAAU,UAAU,IAAI,QAAQ,KAAK,IAAI,SAAS,UAAU;AACnG;AACA,SAAS,iBAAiB,WAAW,KAAK,MAAM;AAC9C,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,IAAI,OAAO;AAAA,IACpB,KAAK;AACH,aAAO,IAAI,QAAQ,UAAU;AAAA,IAC/B,KAAK;AACH,aAAO,IAAI,MAAM;AAAA,IACnB,KAAK;AACH,aAAO,IAAI,SAAS,UAAU;AAAA,EAClC;AACF;AACA,SAAS,2BAA2B,WAAW,KAAK;AAClD,QAAM,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,UAAU,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,UAAU,QAAQ,IAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,gBAAgB,IAAI;AACzL,SAAO,iBAAiB,UAAU,SAAS,UAAU;AACvD;AACA,SAAS,aAAa,WAAW,KAAK;AACpC,SAAO;AAAA,IACL,KAAK,IAAI,MAAM,UAAU;AAAA,IACzB,MAAM,IAAI,OAAO,UAAU;AAAA,IAC3B,QAAQ,UAAU,QAAQ,IAAI,SAAS,UAAU;AAAA,IACjD,SAAS,UAAU,SAAS,IAAI,UAAU,UAAU;AAAA,EACtD;AACF;AACA,SAAS,mBAAmB,WAAW,KAAK;AAC1C,MAAI,MAAM,IAAI,MAAM,UAAU;AAC9B,MAAI,OAAO,IAAI,OAAO,UAAU;AAChC,MAAI,QAAQ,UAAU,QAAQ,IAAI,QAAQ,UAAU;AACpD,MAAI,SAAS,UAAU,SAAS,IAAI,SAAS,UAAU;AACvD,SAAO;AACT;AACA,IAAM,YAAY,CAAC,OAAO,QAAQ,SAAS,QAAQ;AACnD,SAAS,cAAc,IAAI,WAAW,KAAK,QAAQ;AACjD,QAAM,SAAS,aAAa,WAAW,GAAG;AAC1C,aAAW,QAAQ,WAAW;AAC5B,cAAU,IAAI,GAAG,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,GAAG;AAAA,EAC7D;AACF;AACA,SAAS,mBAAmB,WAAW,KAAK,OAAO,MAAM;AACvD,MAAI,aAAa,GAAG,eAAe,WAAW,EAAE,GAAG,IAAI;AACvD,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,WAAO,iBAAiB,WAAW,KAAK,KAAK,CAAC,CAAC,KAAK,YAAY,WAAW,GAAG,KAAK,kBAAkB,KAAK,KAAK,GAAG;AAChH,cAAQ,KAAK,KAAK,CAAC,GAAG,CAAC;AAAA,IACzB;AACA,QAAI,YAAY,WAAW,GAAG;AAC5B,aAAO;AACT,UAAM,eAAe,2BAA2B,WAAW,GAAG;AAC9D,QAAI,aAAa,cAAc;AAC7B,sBAAgB,EAAE,GAAG,IAAI;AACzB,mBAAa;AAAA,IACf;AACA,UAAM,EAAE,GAAG,SAAS;AAAA,EACtB;AACA,SAAO,iBAAiB;AAC1B;AAEA,IAAM,oBAAoB,OAAO,mBAAoB;AACrD,SAAS,YAAY,WAAW,KAAK,WAAW,OAAO;AACrD,MAAI,QAAQ,UAAU,mBAAmB,OAAO,eAAe,GAAG,GAAG,YAAY,OAAO,CAAC;AACzF,MAAI,CAAC,UAAU,YAAY,GAAG;AAC5B,cAAU,YAAY,IAAI,kBAAkB,WAAW,SAAS;AAAA,EAClE;AACA,eAAa,mBAAmB,WAAW,EAAE,GAAG,UAAU,YAAY,EAAE,CAAC;AACzE,MAAI,UAAU,iBAAiB,GAAG;AAChC,WAAO,CAAC,UAAU,iBAAiB,MAAM,QAAQ,OAAO,MAAM,MAAM,IAAI;AAAA,EAC1E,WAAW,IAAI,aAAa;AAC1B,QAAI;AACJ,YAAQ,IAAI,UAAU;AAAA,MACpB,KAAK;AACH,eAAO,CAAC,MAAM,IAAI;AAClB,eAAO;AACP;AAAA,MACF,KAAK;AACH,eAAO,CAAC,MAAM,IAAI;AAClB,eAAO;AACP;AAAA,MACF,KAAK;AACH,eAAO,CAAC,MAAM,IAAI;AAClB,eAAO;AACP;AAAA,IACJ;AACA,QAAI,OAAO,cAAc,KAAK,GAAG,WAAW,OAAO,KAAK,MAAM,IAAI,GAAG,cAAc,UAAU,IAAI,IAAI,MAAM,cAAc,KAAK,CAAC;AAC/H,QAAI,KAAK,IAAI,QAAQ,IAAI,aAAa;AACpC,iBAAW,WAAW,IAAI,KAAK;AAC/B,kBAAY,KAAK,KAAK,cAAc,IAAI,IAAI;AAAA,IAC9C;AACA,QAAI,OAAO,GAAG;AACZ,kBAAY,IAAI,aAAa,KAAK,UAAU,SAAS,UAAU;AAC/D,aAAO,KAAK,QAAQ;AAAA,IACtB;AACA,YAAQ,YAAY,aAAa,QAAQ;AAAA,EAC3C,OAAO;AACL,UAAM,eAAe,IAAI,aAAa,IAAI,UAAU,eAAe,OAAO,MAAM,OAAO,eAAe,WAAW,SAAS,WAAW;AACrI;AAAA,MACE;AAAA,MACA;AAAA,OACC,eAAe,UAAU,SAAS,UAAU,SAAS,OAAO;AAAA,IAC/D;AACA;AAAA,MACE;AAAA,MACA;AAAA,MACA,IAAI,cAAc,WAAW,OAAO,IAAI,IAAI,cAAc,QAAQ,OAAO;AAAA,IAC3E;AACA,WAAO,eAAe,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EAC1E;AACA,eAAa,mBAAmB,WAAW,YAAY,OAAO,IAAI;AAClE,gBAAc,WAAW,WAAW,YAAY,KAAK;AACrD,SAAO;AACT;AACA,SAAS,kBAAkB,WAAW,OAAO;AAC3C,QAAM,MAAM,UAAU,KAAK,GAAG,MAAM,mBAAmB,KAAK;AAC5D,QAAM,iBAAiB,IAAI;AAC3B,MAAI,IAAI,KAAK;AACX,QAAI,MAAM,IAAI;AACd,QAAI,SAAS,IAAI,MAAM,IAAI;AAC3B,UAAM,iBAAiB,IAAI;AAAA,EAC7B;AACA,MAAI,IAAI,QAAQ;AACd,UAAM,SAAS,UAAU,SAAS,IAAI;AACtC,QAAI,MAAM,SAAS,IAAI;AACvB,QAAI,SAAS;AACb,UAAM,iBAAiB,IAAI;AAAA,EAC7B;AACA,MAAI,IAAI;AACN,QAAI,OAAO,IAAI;AACjB,MAAI,IAAI;AACN,QAAI,QAAQ,UAAU,QAAQ,IAAI;AACpC,SAAO,aAAa,WAAW,GAAG;AACpC;AACA,SAAS,mBAAmB,IAAI;AAC9B,QAAM,YAAY,CAAC;AACnB,aAAW,QAAQ,WAAW;AAC5B,cAAU,IAAI,IAAI,WAAW,GAAG,MAAM,iBAAiB,SAAS,IAAI,EAAE,CAAC;AAAA,EACzE;AACA,SAAO;AACT;AACA,SAAS,eAAe,KAAK;AAC3B,MAAI,IAAI,SAAS,QAAQ;AACvB,QAAI,CAAC,IAAI,aAAa;AACpB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,IAAI;AACb;AACA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,IAAI,aAAa,QAAQ;AAC3B,YAAQ,IAAI,OAAO;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AACA,SAAO,IAAI;AACb;AACA,SAAS,4BAA4B,KAAK,KAAK;AAC7C,MAAI,IAAI,kBAAkB,QAAQ;AAChC,YAAQ,IAAI,OAAO;AAAA,MACjB,KAAK;AACH,eAAO,QAAQ,QAAQ,cAAc;AAAA,MACvC,KAAK;AACH,eAAO,QAAQ,QAAQ,eAAe;AAAA,MACxC,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO,QAAQ,IAAI,KAAK;AAAA,IAC5B;AAAA,EACF;AACA,SAAO,IAAI;AACb;AAEA,IAAM,cAAc,CAAC,MAAM,MAAM,MAAM,IAAI;AAC3C,SAAS,eAAe,WAAW,QAAQ,UAAU,OAAO;AAC1D,MAAI,OAAO,MAAM,KAAK,SAAS,iBAAiB,2BAA2B,CAAC,GAAG,SAAS,GAAG,QAAQ,KAAK,IAAI,GAAG,KAAK,SAAS,OAAO,KAAK;AACzI,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,OAAO,KAAK;AAC7C,cAAU,KAAK,CAAC,EAAE;AAAA,EACpB;AACA,YAAU,UAAU,iBAAiB,SAAS,IAAI;AAClD,MAAI,CAAC,SAAS,YAAY,GAAG;AAC3B,aAAS,YAAY,IAAI,aAAa,WAAW,UAAU,QAAQ,CAAC;AAAA,EACtE;AACA,MAAI,MAAM,EAAE,GAAG,SAAS,YAAY,EAAE;AACtC,QAAM,mBAAmB,WAAW,GAAG;AACvC,MAAI,QAAQ,SAAS;AACrB,MAAI,SAAS;AACb,MAAI,QAAQ,IAAI,OAAO,IAAI;AAC3B,MAAI,SAAS,IAAI,MAAM;AACvB,QAAM,mBAAmB,WAAW,KAAK,OAAO,WAAW;AAC3D,gBAAc,UAAU,WAAW,KAAK,QAAQ;AAChD,SAAO;AACT;AAEA,IAAM,mBAAN,MAAuB;AAAA,EAyBrB,YAAY,SAAS,MAAM;AAxB3B;AACA;AACA,wCAAe;AACf,gCAAO;AACP,uCAAc,CAAC;AACf,uCAAc;AACd;AACA,oCAA2B,oBAAI,IAAI;AACnC,iCAAwB,oBAAI,IAAI;AA2DhC,mCAAU,SAAS,MAAM;AACvB,WAAK,cAAc;AACnB,WAAK,eAAe;AACpB,iBAAW,MAAM,KAAK,SAAS,OAAO,GAAG;AACvC,WAAG,YAAY,IAAI;AAAA,MACrB;AACA,iBAAW,MAAM,KAAK,MAAM,OAAO,GAAG;AACpC,YAAI;AACF,aAAG,YAAY,IAAI;AAAA,MACvB;AACA,WAAK,QAAQ,IAAI;AAAA,IACnB,GAAG,EAAE;AArDH,SAAK,UAAU;AACf,SAAK,OAAM,6BAAM,QAAO;AACxB,YAAQ,aAAa,aAAa,KAAK;AACvC,YAAQ,aAAa,aAAa,KAAK;AACvC,YAAQ,aAAa,eAAe,MAAM;AAC1C,gBAAY,SAAS,UAAU;AAC/B,SAAK,eAAe;AACpB,SAAK,kBAAkB,IAAI,eAAe,KAAK,UAAU,KAAK,IAAI,CAAC;AACnE,SAAK,gBAAgB,QAAQ,OAAO;AAAA,EACtC;AAAA;AAAA,EAxBA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,KAAK;AACX,SAAK,OAAO;AACZ,gBAAY,KAAK,SAAS,OAAO,GAAG;AAAA,EACtC;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,MAAM;AACpB,SAAK,eAAe;AACpB,SAAK,OAAO;AAAA,EACd;AAAA,EAYA,YAAY,EAAE,SAAS,KAAK,GAAG;AAC7B,SAAK,MAAM;AACX,SAAK,cAAc,OAAO;AAC1B,eAAW,OAAO;AAChB,WAAK,MAAM,IAAI,KAAK,IAAI;AAC1B,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO,KAAK;AACV,SAAK,MAAM,IAAI,KAAK,IAAI;AACxB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,UAAU,KAAK;AACb,SAAK,MAAM,OAAO,GAAG;AACrB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO,cAAc,OAAO;AAC1B,SAAK,QAAQ,WAAW;AAAA,EAC1B;AAAA,EACA,QAAQ;AACN,SAAK,MAAM,MAAM;AACjB,SAAK,SAAS,MAAM;AACpB,SAAK,cAAc,CAAC;AACpB,SAAK,QAAQ,cAAc;AAAA,EAC7B;AAAA,EACA,UAAU;AACR,SAAK,MAAM;AACX,SAAK,gBAAgB,WAAW;AAAA,EAClC;AAAA,EACA,YAAY;AACV,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EAaA,iBAAiB;AACf,SAAK,cAAc,UAAU,KAAK,OAAO;AACzC,cAAU,KAAK,SAAS,iBAAiB,KAAK,YAAY,QAAQ,IAAI;AACtE,cAAU,KAAK,SAAS,kBAAkB,KAAK,YAAY,SAAS,IAAI;AAAA,EAC1E;AAAA,EACA,QAAQ,cAAc,OAAO;AAC3B,QAAI,CAAC,KAAK,MAAM,QAAQ,KAAK;AAC3B;AACF,QAAI,KAAK,aAAa,CAAC,GAAG,KAAK,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,SAAS,KAAK,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,KAAK,OAAO,EAAE;AAAA,MACtI,CAAC,MAAM,SAAS,KAAK,cAAc,KAAK,YAAY,KAAK,YAAY,KAAK,YAAY,KAAK,UAAU,KAAK;AAAA,IAC5G,GAAG,gBAAgB,WAAW,IAAI,CAAC,SAAS,KAAK,MAAM;AACvD,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,YAAM,KAAK,YAAY,CAAC;AACxB,UAAI,WAAW,CAAC,MAAM;AACpB;AACF,UAAI,IAAI,UAAU,CAAC,cAAc,SAAS,IAAI,MAAM,GAAG;AACrD,cAAM,WAAW,KAAK,SAAS,IAAI,IAAI,OAAO,EAAE;AAChD,YAAI,UAAU;AACZ,mBAAS,gBAAgB,aAAa;AACtC,wBAAc;AAAA,QAChB;AAAA,MACF;AACA,YAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAChC,UAAI,OAAO;AACT,cAAM,OAAO;AACb,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,WAAW,CAAC;AAClB,UAAI,QAAQ,KAAK,MAAM,IAAI,GAAG;AAC9B,UAAI,CAAC;AACH,aAAK,MAAM,IAAI,KAAK,QAAQ,KAAK,kBAAkB,GAAG,CAAC;AACzD,YAAM,WAAW,KAAK,WAAW,GAAG,KAAK,KAAK,SAAS,IAAI,IAAI,OAAO,EAAE;AACxE,UAAI,YAAY,CAAC,SAAS,aAAa,aAAa,GAAG;AACrD,8BAAsB,MAAM,YAAY,UAAU,QAAQ,CAAC;AAC3D,sBAAc;AAAA,MAChB;AACA,UAAI,CAAC,MAAM,aAAa;AACtB,SAAC,YAAY,KAAK,SAAS,OAAO,KAAK;AACvC,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,QAAI,aAAa;AACf,YAAM,QAAQ,CAAC,GAAG,OAAuB,oBAAI,IAAI;AACjD,eAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,cAAM,WAAW,CAAC;AAClB,YAAI,KAAK,IAAI,IAAI,UAAU,GAAG;AAC5B;AACF,cAAM,WAAW,KAAK,WAAW,GAAG,GAAG,KAAK,WAAW,KAAK,SAAS,IAAI,IAAI,OAAO,EAAE,IAAI,KAAK,MAAM,IAAI,GAAG;AAC5G,YAAI,UAAU;AACZ,gBAAM,KAAK,eAAe,KAAK,aAAa,IAAI,QAAQ,IAAI,KAAK,CAAC;AAAA,QACpE,OAAO;AACL,gBAAM,KAAK,YAAY,KAAK,aAAa,KAAK,IAAI,KAAK,CAAC;AAAA,QAC1D;AACA,aAAK,IAAI,WAAW,IAAI,SAAS,GAAG;AAAA,MACtC;AAAA,IACF;AACA,2BAAuB,KAAK,SAAS,KAAK,YAAY;AACtD,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,CAAC;AACH;AACF,eAAW,UAAU,SAAS;AAC5B,YAAM,KAAK,KAAK,qBAAqB,MAAM;AAC3C,WAAK,SAAS,IAAI,OAAO,IAAI,EAAE;AAC/B,WAAK,QAAQ,OAAO,EAAE;AAAA,IACxB;AAAA,EACF;AAAA,EACA,qBAAqB,QAAQ;AAC3B,UAAM,KAAK,SAAS,cAAc,KAAK;AACvC,gBAAY,IAAI,QAAQ;AACxB,gBAAY,IAAI,MAAM,OAAO,EAAE;AAC/B,gBAAY,IAAI,UAAU,OAAO,MAAM;AACvC,cAAU,IAAI,gBAAgB,OAAO,QAAQ,GAAG;AAChD,cAAU,IAAI,mBAAmB,OAAO,aAAa;AACrD,cAAU,IAAI,mBAAmB,OAAO,aAAa;AACrD,cAAU,IAAI,4BAA4B,OAAO,eAAe;AAChE,cAAU,IAAI,4BAA4B,OAAO,eAAe;AAChE,cAAU,IAAI,gBAAgB,OAAO,KAAK;AAC1C,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,KAAK;AA/vCzB;AAgwCI,UAAM,UAAU,SAAS,cAAc,KAAK,GAAG,WAAW,mBAAmB,GAAG,GAAG,oBAAoB,4BAA4B,KAAK,KAAK,IAAI;AACjJ,gBAAY,SAAS,aAAa;AAClC,QAAI,IAAI,aAAa;AACnB,kBAAY,SAAS,UAAU;AACjC,cAAU,SAAS,kBAAkB,IAAI,KAAK;AAC9C,QAAI,IAAI,OAAO;AACb,iBAAW,QAAQ,OAAO,KAAK,IAAI,KAAK,GAAG;AACzC,gBAAQ,MAAM,YAAY,MAAM,IAAI,MAAM,IAAI,CAAC;AAAA,MACjD;AAAA,IACF;AACA,QAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACzB;AAAA,QACE;AAAA,QACA;AAAA,QACA,IAAI,aAAa,KAAK,kBAAkB,IAAI,aAAa,OAAO,gBAAgB;AAAA,MAClF;AACA,UAAI,GAAC,SAAI,UAAJ,mBAAY,iBAAgB;AAC/B,YAAI,UAAU;AACd,YAAI,sBAAsB,aAAa;AACrC,oBAAU,MAAM;AAAA,QAClB,WAAW,sBAAsB,YAAY,YAAY,IAAI;AAC3D,oBAAU,WAAW;AAAA,QACvB,WAAW,sBAAsB,YAAY,WAAW,IAAI;AAC1D,qBAAW,MAAM,YAAY;AAAA,QAC/B;AACA,cAAM,OAAO,IAAI,OAAO,UAAU,IAAI,OAAO;AAC7C,YAAI,IAAI,aAAa;AACnB,oBAAU,SAAS,aAAa,OAAO,GAAG;AAAA;AAE1C,oBAAU,SAAS,cAAc,OAAO,GAAG;AAAA,MAC/C;AAAA,IACF,OAAO;AACL;AAAA,QACE;AAAA,QACA;AAAA,QACA,GAAG,YAAY,sBAAsB,eAAe,MAAM,sBAAsB,WAAW,KAAK,EAAE;AAAA,MACpG;AAAA,IACF;AACA,UAAM,KAAK,SAAS,cAAc,KAAK;AACvC,gBAAY,IAAI,KAAK;AACrB,QAAI,IAAI;AACN,kBAAY,IAAI,MAAM,IAAI,EAAE;AAC9B,OAAG,YAAY,mBAAmB,GAAG;AACrC,YAAQ,OAAO,EAAE;AACjB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,KAAK;AACd,WAAO,IAAI,UAAU,IAAI,SAAS,OAAO,IAAI,aAAa,MAAM,IAAI,SAAS;AAAA,EAC/E;AACF;", "names": []}