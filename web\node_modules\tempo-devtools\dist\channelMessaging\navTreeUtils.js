"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runNavTreeBuiltCallbacks = exports.addNavTreeBuiltCallback = exports.buildNavForNodeNonBlocking = exports.getNavNodeForVirtualComponent = exports.ExtractedPropType = exports.SKIP_ROOT_CODEBASE_ID = exports.EMPTY_TREE_CODEBASE_ID = exports.TOP_LEVEL_PARENT_COMPONENT_TO_SKIP = exports.UNKNOWN_PARENT_COMPONENT = void 0;
const identifierUtils_1 = require("./identifierUtils");
const outlineUtils_1 = require("./outlineUtils");
const jquery_1 = __importDefault(require("jquery"));
const sessionStorageUtils_1 = require("./sessionStorageUtils");
const tempoElement_1 = require("./tempoElement");
const resqUtils_1 = require("./resqUtils");
exports.UNKNOWN_PARENT_COMPONENT = 'UnknownComponent';
exports.TOP_LEVEL_PARENT_COMPONENT_TO_SKIP = 'TOP_LEVEL_PARENT_COMPONENT_TO_SKIP';
exports.EMPTY_TREE_CODEBASE_ID = 'EMPTY-TREE';
// Special codebase ID -> if set on the root node it's expected it doesn't get rendered
// This is used when there are multiple nodes under the root node that we want to return while we don't
// want to render the root node itself
exports.SKIP_ROOT_CODEBASE_ID = 'SKIP-ROOT';
// Matches the interface on the frontend
var ExtractedPropType;
(function (ExtractedPropType) {
    ExtractedPropType["LITERAL"] = "LITERAL";
    ExtractedPropType["FUNCTION"] = "FUNCTION";
    ExtractedPropType["JSON_OBJECT"] = "JSON_OBJECT";
})(ExtractedPropType || (exports.ExtractedPropType = ExtractedPropType = {}));
const extractPropsFromReactFiberNode = (reactFiberNode) => {
    if (!(reactFiberNode === null || reactFiberNode === void 0 ? void 0 : reactFiberNode.memoizedProps)) {
        return {};
    }
    const props = {};
    Object.keys(reactFiberNode.memoizedProps).forEach((key) => {
        if (key === 'children') {
            return;
        }
        // Filter out known props
        if (identifierUtils_1.KNOWN_ATTRIBUTES.has(key.toLowerCase())) {
            return;
        }
        let propValue = reactFiberNode.memoizedProps[key];
        // Filter out unknown classes
        if (key === 'className' && typeof propValue === 'string') {
            propValue = (0, identifierUtils_1.getAllUnknownClasesFromList)(propValue.split(' ')).join(' ');
        }
        if (typeof propValue === 'function') {
            props[key] = {
                value: key,
                type: ExtractedPropType.FUNCTION,
            };
        }
        else if (typeof propValue === 'object') {
            try {
                props[key] = {
                    value: JSON.stringify(propValue),
                    type: ExtractedPropType.JSON_OBJECT,
                };
            }
            catch (e) {
                // skip this prop
            }
        }
        else {
            props[key] = {
                value: propValue,
                type: ExtractedPropType.LITERAL,
            };
        }
    });
    return props;
};
const extractLiteralChildrenFromReactFiberNode = (reactFiberNode) => {
    var _a;
    if (!((_a = reactFiberNode === null || reactFiberNode === void 0 ? void 0 : reactFiberNode.memoizedProps) === null || _a === void 0 ? void 0 : _a.children)) {
        return [];
    }
    const literalChildren = [];
    Array.from(reactFiberNode.memoizedProps.children || []).forEach((childProp, index) => {
        if (typeof childProp !== 'object') {
            literalChildren.push({
                index,
                value: childProp,
            });
        }
    });
    return literalChildren;
};
function selectorSafe(uniquePath) {
    // Dictionary of replacements. You can expand this list as needed.
    const replacements = {
        '!': '_exclamation_',
        '@': '_at_',
        '#': '_hash_',
        $: '_dollar_',
        '%': '_percent_',
        '^': '_caret_',
        '&': '_and_',
        '*': '_asterisk_',
        '(': '_openParen_',
        ')': '_closeParen_',
        '+': '_plus_',
        '=': '_equals_',
        '[': '_openBracket_',
        ']': '_closeBracket_',
        '{': '_openBrace_',
        '}': '_closeBrace_',
        '|': '_pipe_',
        ';': '_semicolon_',
        ':': '_colon_',
        ',': '_comma_',
        '.': '_period_',
        '<': '_lessThan_',
        '>': '_greaterThan_',
        '/': '_slash_',
        '?': '_question_',
        '\\': '_backslash_',
        ' ': '_space_',
    };
    // Replace each character with its mapped value
    Object.keys(replacements).forEach((character) => {
        const regex = new RegExp('\\' + character, 'g');
        uniquePath = uniquePath.replace(regex, replacements[character]);
    });
    // Handle invalid starting characters
    uniquePath = uniquePath.replace(/^[0-9-]/, '_startNumOrHyphen_');
    // Lastly, replace any remaining non-alphanumeric characters just in case
    return uniquePath.replace(/[^a-zA-Z0-9_-]/g, '_');
}
/**
 * Nav node for a component that has no DOM element associated with it
 */
const getNavNodeForVirtualComponent = (parent, componentName, componentInstanceId, uniquePath, scopeLookup, storyboardId, reactFiberNode) => {
    const navTreeNode = {
        parent: parent,
        children: [],
        classList: [],
        directlySetClassList: [],
        name: '',
        tempoElement: tempoElement_1.TempoElement.empty(),
    };
    navTreeNode.name = componentName;
    navTreeNode.isComponent = true;
    navTreeNode.tempoElement = new tempoElement_1.TempoElement(componentInstanceId, storyboardId, uniquePath);
    navTreeNode.props = extractPropsFromReactFiberNode(reactFiberNode);
    navTreeNode.literalChildren =
        extractLiteralChildrenFromReactFiberNode(reactFiberNode);
    Object.keys(scopeLookup).forEach((codebaseId) => {
        var _a;
        if (navTreeNode.scope) {
            return;
        }
        if (((_a = scopeLookup[codebaseId].codebaseIds) === null || _a === void 0 ? void 0 : _a.indexOf(componentInstanceId)) > -1) {
            navTreeNode.scope = scopeLookup[codebaseId];
        }
    });
    return navTreeNode;
};
exports.getNavNodeForVirtualComponent = getNavNodeForVirtualComponent;
const buildNavForNodeNonBlocking = (params, onComplete) => {
    (0, identifierUtils_1.clearNodeForElementKey)();
    (0, identifierUtils_1.clearElementKeyForUniquePath)();
    const queue = [
        {
            params: params,
        },
    ];
    let finalResult = null;
    function processChunk() {
        var _a;
        const startTime = performance.now();
        const MAX_TIME = 10; // ms per frame
        while (queue.length > 0 && performance.now() - startTime < MAX_TIME) {
            const item = queue.shift();
            if (!item) {
                continue;
            }
            const { params } = item;
            const res = buildNavForNode(params);
            // All the child calls to the queue
            if ((_a = res === null || res === void 0 ? void 0 : res.callsToAddToQueue) === null || _a === void 0 ? void 0 : _a.length) {
                queue.push(...res.callsToAddToQueue);
            }
            // First node is top of the tree
            if (!finalResult && (res === null || res === void 0 ? void 0 : res.result)) {
                finalResult = res === null || res === void 0 ? void 0 : res.result;
            }
        }
        // Check if we need to continue processing
        if (queue.length > 0) {
            requestAnimationFrame(processChunk);
        }
        else if (!finalResult) {
            onComplete(null);
        }
        else {
            // Return or use the result here
            onComplete(filterOutNodesWithoutCodebaseId(finalResult, params.elementKeyToNavNode, params.treeElements, params.storyboardId));
        }
    }
    // Start processing
    requestAnimationFrame(processChunk);
};
exports.buildNavForNodeNonBlocking = buildNavForNodeNonBlocking;
const buildNavForNode = ({ storyboardId, parent, node, uniquePathBase, uniquePathAddon, scopeLookup, treeElements, knownComponentNames, knownComponentInstanceNames, elementKeyToLookupList, elementKeyToNavNode, domUniquePath, }) => {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j;
    if ((0, outlineUtils_1.isNodeOutline)(node)) {
        return null;
    }
    if ((0, identifierUtils_1.isSkipNavTreeNode)(node)) {
        return null;
    }
    if (['noscript', 'script'].includes((_a = node === null || node === void 0 ? void 0 : node.tagName) === null || _a === void 0 ? void 0 : _a.toLowerCase())) {
        return null;
    }
    const foundId = (0, identifierUtils_1.getCodebaseIdFromNode)(node);
    // May 15, 2023 -> found bug where a random iframe was being added with the hot reloaded code
    // I think this is related to this bug:
    // https://github.com/facebook/create-react-app/issues/11880
    if (((_b = node === null || node === void 0 ? void 0 : node.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === 'iframe') {
        if (!foundId) {
            node.remove();
            return null;
        }
    }
    const reactFiberNode = (0, resqUtils_1.findReactInstance)(node);
    const boundingRect = node.getBoundingClientRect();
    const { top, left } = (0, jquery_1.default)(node).offset() || { top: 0, left: 0 };
    let parentToUse = parent;
    let uniquePathBaseToUse = uniquePathBase;
    //////////////////////////////////////////////////////////////////
    // Handle virtual components from the react fiber tree
    //////////////////////////////////////////////////////////////////
    // For outlines, components that are added need an outline around all the elements inside
    // Create lookups in local storage to keep track of this
    // Element keys of virtual components
    const virtualComponentElementKeys = [];
    // When there are react forward refs we want to collapse the node into the top level forward ref
    let componentNameToCollapseInto;
    let componentInstanceIdToCollapseInto;
    if (reactFiberNode && (parent === null || parent === void 0 ? void 0 : parent.reactFiberNode)) {
        // Traverse up the stack adding components to the tree until you hit this node's parent
        // Note, we have to account for other children that already performed this operation and added nodes to the tree
        let searchNode = reactFiberNode.return;
        let possibleNodesToAdd = [];
        // This loop picks all the relevant nodes in between (ignoring if they are already added or not)
        while (searchNode) {
            if (searchNode === parent.reactFiberNode) {
                break;
            }
            // Sometimes components are named differently in the react fiber tree from the codebase, but we still want to include them
            // in the DOM tree if they are components defined in source files
            // E.g. in next JS if you create a <Link /> element it will be called "LinkComponent"
            const debugSourceFileInCodebase = ((_c = searchNode === null || searchNode === void 0 ? void 0 : searchNode._debugSource) === null || _c === void 0 ? void 0 : _c.fileName) &&
                !((_e = (_d = searchNode === null || searchNode === void 0 ? void 0 : searchNode._debugSource) === null || _d === void 0 ? void 0 : _d.fileName) === null || _e === void 0 ? void 0 : _e.includes('node_modules'));
            const nodeName = (0, resqUtils_1.getElementNameString)(searchNode);
            if ((((_f = searchNode.memoizedProps) === null || _f === void 0 ? void 0 : _f.tempoelementid) ||
                ((_g = searchNode.memoizedProps) === null || _g === void 0 ? void 0 : _g['data-testid'])) &&
                ((knownComponentNames === null || knownComponentNames === void 0 ? void 0 : knownComponentNames.has(nodeName)) ||
                    (knownComponentInstanceNames === null || knownComponentInstanceNames === void 0 ? void 0 : knownComponentInstanceNames.has(nodeName)) ||
                    debugSourceFileInCodebase)) {
                possibleNodesToAdd.push(searchNode);
            }
            searchNode = searchNode.return;
        }
        // Found the parent, traverse down the nodes, checking if that node was already added to the tree,
        // and adding it if it wasn't
        if (searchNode && possibleNodesToAdd.length) {
            let currentParent = parent;
            Array.from(possibleNodesToAdd)
                .reverse()
                .forEach((nodeToAdd) => {
                var _a, _b, _c, _d, _e, _f, _g, _h;
                const nodeName = (0, resqUtils_1.getElementNameString)(nodeToAdd);
                // If this is a forward ref just move forward in the tree without adding this element, but
                // but still label the next non-forward ref with this node's name & instance ID
                // However, only do this the first time (want the highest forward ref)
                if (((_b = (_a = nodeToAdd === null || nodeToAdd === void 0 ? void 0 : nodeToAdd.elementType) === null || _a === void 0 ? void 0 : _a['$$typeof']) === null || _b === void 0 ? void 0 : _b.toString()) ===
                    'Symbol(react.forward_ref)') {
                    if (!componentNameToCollapseInto &&
                        !componentInstanceIdToCollapseInto) {
                        componentInstanceIdToCollapseInto =
                            ((_c = nodeToAdd.memoizedProps) === null || _c === void 0 ? void 0 : _c.tempoelementid) ||
                                ((_d = nodeToAdd.memoizedProps) === null || _d === void 0 ? void 0 : _d['data-testid']);
                        const referenceTreeElement = treeElements[componentInstanceIdToCollapseInto || ''];
                        if ((referenceTreeElement === null || referenceTreeElement === void 0 ? void 0 : referenceTreeElement.type) === 'component-instance') {
                            componentNameToCollapseInto =
                                referenceTreeElement.componentName;
                        }
                        else {
                            componentNameToCollapseInto = nodeName;
                        }
                    }
                    return;
                }
                const matchingNavTreeNode = currentParent
                    ? (_e = currentParent.children) === null || _e === void 0 ? void 0 : _e.find((child) => child.reactFiberNode === nodeToAdd)
                    : null;
                // Node already matches, increase level and continue
                if (matchingNavTreeNode) {
                    currentParent = matchingNavTreeNode;
                    if (currentParent.tempoElement) {
                        virtualComponentElementKeys.push(currentParent.tempoElement.getKey());
                    }
                    // Increase the size of the bounding box for this element
                    if (!matchingNavTreeNode.pageBoundingBox) {
                        matchingNavTreeNode.pageBoundingBox = {
                            pageX: left,
                            pageY: top,
                            width: boundingRect.width,
                            height: boundingRect.height,
                        };
                    }
                    else {
                        const newRight = Math.max(matchingNavTreeNode.pageBoundingBox.pageX +
                            matchingNavTreeNode.pageBoundingBox.width, left + boundingRect.width);
                        const newLeft = Math.min(matchingNavTreeNode.pageBoundingBox.pageX, boundingRect.left);
                        const newTop = Math.min(matchingNavTreeNode.pageBoundingBox.pageY, boundingRect.top);
                        const newBottom = Math.max(matchingNavTreeNode.pageBoundingBox.pageY +
                            matchingNavTreeNode.pageBoundingBox.height, top + boundingRect.height);
                        matchingNavTreeNode.pageBoundingBox.pageX = newLeft;
                        matchingNavTreeNode.pageBoundingBox.pageY = newTop;
                        matchingNavTreeNode.pageBoundingBox.width = newRight - newLeft;
                        matchingNavTreeNode.pageBoundingBox.height = newBottom - newTop;
                    }
                    return;
                }
                else {
                    // Otherwise, create a new virtual node, add to parent and continue
                    let componentName;
                    let componentInstanceId;
                    if (componentNameToCollapseInto) {
                        componentName = componentInstanceIdToCollapseInto;
                        componentInstanceId = componentInstanceIdToCollapseInto;
                        componentNameToCollapseInto = undefined;
                        componentInstanceIdToCollapseInto = undefined;
                    }
                    else {
                        componentName = nodeName;
                        componentInstanceId =
                            ((_f = nodeToAdd.memoizedProps) === null || _f === void 0 ? void 0 : _f.tempoelementid) ||
                                ((_g = nodeToAdd.memoizedProps) === null || _g === void 0 ? void 0 : _g['data-testid']);
                    }
                    // Update the unique path and use it
                    uniquePathBaseToUse = selectorSafe(`${uniquePathBaseToUse}-${((_h = currentParent === null || currentParent === void 0 ? void 0 : currentParent.children) === null || _h === void 0 ? void 0 : _h.length) || 0}`);
                    const newVirtualComponent = (0, exports.getNavNodeForVirtualComponent)(currentParent, nodeName, componentInstanceId, uniquePathBaseToUse, scopeLookup, storyboardId, nodeToAdd);
                    currentParent.children.push(newVirtualComponent);
                    currentParent = newVirtualComponent;
                    virtualComponentElementKeys.push(newVirtualComponent.tempoElement.getKey());
                    elementKeyToNavNode[newVirtualComponent.tempoElement.getKey()] =
                        newVirtualComponent;
                    // Set the bounding box for the new virtual component
                    newVirtualComponent.pageBoundingBox = {
                        pageX: left,
                        pageY: top,
                        width: boundingRect.width,
                        height: boundingRect.height,
                    };
                }
            });
            parentToUse = currentParent;
        }
    }
    // This node corresponds to the DOM element, not any components, unless we are collapsing into the component
    const navTreeNode = {
        parent: parentToUse,
        children: [],
        classList: (0, identifierUtils_1.getAllUnknownClasses)(node),
        directlySetClassList: [],
        name: '',
        tempoElement: tempoElement_1.TempoElement.empty(),
    };
    (_h = parentToUse === null || parentToUse === void 0 ? void 0 : parentToUse.children) === null || _h === void 0 ? void 0 : _h.push(navTreeNode);
    navTreeNode.name = componentNameToCollapseInto || node.tagName;
    navTreeNode.elementTagName = node.tagName;
    // These are only forward ref components, all other components are added as virtual components
    navTreeNode.isComponent = Boolean(componentInstanceIdToCollapseInto);
    const uniquePathForNode = selectorSafe(`${uniquePathBaseToUse}${uniquePathAddon}`);
    const codebaseId = componentInstanceIdToCollapseInto || foundId || undefined;
    navTreeNode.tempoElement = new tempoElement_1.TempoElement(codebaseId, storyboardId, uniquePathForNode);
    const nodeElementKey = navTreeNode.tempoElement.getKey();
    // Using the virtualComponentElementKeys, set the elementKey in a list for this element
    virtualComponentElementKeys.forEach((elementKey) => {
        if (elementKeyToLookupList[elementKey]) {
            elementKeyToLookupList[elementKey].push(nodeElementKey);
        }
        else {
            elementKeyToLookupList[elementKey] = [nodeElementKey];
        }
    });
    // Set the lookup list for the specific node itself as well
    elementKeyToLookupList[nodeElementKey] = [nodeElementKey];
    (0, identifierUtils_1.setNodeForElementKey)(nodeElementKey, node);
    (0, identifierUtils_1.setElementKeyForUniquePath)(domUniquePath, nodeElementKey);
    const treeElementForNode = treeElements[navTreeNode.tempoElement.codebaseId];
    if (treeElementForNode) {
        const removableClasses = new Set((treeElementForNode === null || treeElementForNode === void 0 ? void 0 : treeElementForNode.removableClasses) || []);
        navTreeNode.directlySetClassList = (_j = navTreeNode.classList) === null || _j === void 0 ? void 0 : _j.filter((cls) => {
            return removableClasses.has(cls);
        });
    }
    navTreeNode.reactFiberNode = reactFiberNode;
    navTreeNode.props = extractPropsFromReactFiberNode(reactFiberNode);
    navTreeNode.literalChildren =
        extractLiteralChildrenFromReactFiberNode(reactFiberNode);
    navTreeNode.pageBoundingBox = {
        pageX: left,
        pageY: top,
        width: boundingRect.width,
        height: boundingRect.height,
    };
    if (navTreeNode.tempoElement.codebaseId) {
        Object.keys(scopeLookup).forEach((codebaseId) => {
            var _a;
            if (navTreeNode.scope) {
                return;
            }
            if (((_a = scopeLookup[codebaseId].codebaseIds) === null || _a === void 0 ? void 0 : _a.indexOf(navTreeNode.tempoElement.codebaseId)) > -1) {
                navTreeNode.scope = scopeLookup[codebaseId];
            }
        });
    }
    const callsToAddToQueue = [];
    // Only parse children for non-svg elements
    if (node.children && node.tagName !== 'svg') {
        Array.from(node.children).forEach((child, index) => {
            callsToAddToQueue.push({
                params: {
                    storyboardId,
                    parent: navTreeNode,
                    node: child,
                    uniquePathBase: uniquePathForNode,
                    uniquePathAddon: `-${index}`,
                    scopeLookup,
                    treeElements,
                    knownComponentNames,
                    knownComponentInstanceNames,
                    elementKeyToLookupList,
                    elementKeyToNavNode,
                    domUniquePath: `${domUniquePath}-${index}`,
                },
            });
        });
    }
    elementKeyToNavNode[nodeElementKey] = navTreeNode;
    return {
        result: navTreeNode,
        callsToAddToQueue,
    };
};
const filterOutNodesWithoutCodebaseId = (finishedNavTree, elementKeyToNavNode, treeElements, storyboardId) => {
    let treeToReturn = finishedNavTree;
    const storyboardType = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.STORYBOARD_TYPE) || 'APPLICATION';
    const storyboardSavedComponentFile = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SAVED_STORYBOARD_COMPONENT_FILENAME);
    const originalStoryboardUrl = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ORIGINAL_STORYBOARD_URL);
    const userNavigatedToNewRoute = originalStoryboardUrl &&
        !window.location.href.includes(originalStoryboardUrl);
    /**
     * Returns whether the given tree element is in the top-level file of the storyboard
     * Note - for saved components the top-level file is the saved component file
     */
    const isElementDirectlyInStoryboard = (node) => {
        var _a, _b, _c, _d, _e, _f;
        const filename = (_a = treeElements[node.tempoElement.codebaseId]) === null || _a === void 0 ? void 0 : _a.filename;
        // For stories, just filter for anything not in _app or _document
        if (storyboardType === 'STORY' &&
            filename &&
            !filename.includes('_app') &&
            !filename.includes('_document')) {
            return true;
        }
        // Special case -> if the parent is the body element this might be in a portal
        // go all the way up the react fiber tree and see if there are any elements
        // that are in the storyboard
        if (((_b = node.parent) === null || _b === void 0 ? void 0 : _b.name) === 'BODY') {
            let parentFiberNode = (_c = node.reactFiberNode) === null || _c === void 0 ? void 0 : _c.return;
            while (parentFiberNode) {
                const codebaseId = ((_d = parentFiberNode === null || parentFiberNode === void 0 ? void 0 : parentFiberNode.props) === null || _d === void 0 ? void 0 : _d.tempoelementid) ||
                    ((_e = parentFiberNode === null || parentFiberNode === void 0 ? void 0 : parentFiberNode.props) === null || _e === void 0 ? void 0 : _e['data-testid']) ||
                    '';
                if (codebaseId) {
                    const treeElementFilename = (_f = treeElements[codebaseId]) === null || _f === void 0 ? void 0 : _f.filename;
                    const valid = Boolean(treeElementFilename === null || treeElementFilename === void 0 ? void 0 : treeElementFilename.includes('tempobook/storyboards')) ||
                        Boolean(treeElementFilename &&
                            treeElementFilename === storyboardSavedComponentFile);
                    if (valid) {
                        return true;
                    }
                }
                parentFiberNode = parentFiberNode === null || parentFiberNode === void 0 ? void 0 : parentFiberNode.return;
            }
        }
        // For everything else, filter anything that is not in the storyboard itself
        return (Boolean(filename === null || filename === void 0 ? void 0 : filename.includes('tempobook/storyboards')) ||
            Boolean(filename && filename === storyboardSavedComponentFile));
    };
    const processNode = (node, elementInStoryboardFound) => {
        var _a, _b;
        // Process the children first
        for (let i = node.children.length - 1; i >= 0; i--) {
            processNode(node.children[i], elementInStoryboardFound || isElementDirectlyInStoryboard(node));
        }
        // Product decision: Filter out nodes that don't exist in storyboard file for the corresponding component URL
        //
        // Historical context:
        // Dec 14 - a bug was found where in cases that components were dynamically loaded (e.g. in Next JS _app.tsx), when you click
        // on the top level component it would point to this location in the codebase:
        //
        // function MyApp({ Component, pageProps: { session, ...pageProps } }: AppProps) {
        //   return (
        //     <SessionProvider session={session}>
        //       <Component {...pageProps} />
        //       <Analytics />
        //     </SessionProvider>
        //   );
        // }
        //
        // This was especially an issue for component storyboards. Thus the decision was made to hide any top-level components or divs
        // that are not in the storyboard file
        const inComponentStoryboardAndSkip = storyboardType !== 'APPLICATION' &&
            !userNavigatedToNewRoute &&
            !elementInStoryboardFound &&
            !isElementDirectlyInStoryboard(node);
        // If this node doesn't have a codebaseId, move its children to its parent
        if (!((_a = node.tempoElement.codebaseId) === null || _a === void 0 ? void 0 : _a.startsWith('tempo-')) ||
            inComponentStoryboardAndSkip) {
            if (node.parent) {
                // Move the children in the spot where the node was
                const childrenToMove = node.children;
                const indexOfNodeInParent = (_b = node.parent.children) === null || _b === void 0 ? void 0 : _b.indexOf(node);
                node.parent.children.splice(indexOfNodeInParent, 1, ...childrenToMove);
                // Change the parent of all the children to the new parent
                childrenToMove.forEach((child) => {
                    child.parent = node.parent;
                });
                // Remove the node from the known nodes
                delete elementKeyToNavNode[node.tempoElement.getKey()];
            }
            else if (node.children.length === 1) {
                // This is the top-level node, move it down
                treeToReturn = node.children[0];
                delete elementKeyToNavNode[node.tempoElement.getKey()];
                treeToReturn.parent = undefined;
            }
            else if (node.children.length === 0) {
                // 0 children, no nav tree to return
                treeToReturn = {
                    children: [],
                    tempoElement: new tempoElement_1.TempoElement(exports.EMPTY_TREE_CODEBASE_ID, storyboardId, '1'),
                    name: '',
                };
                delete elementKeyToNavNode[node.tempoElement.getKey()];
            }
            else {
                // 2+ children, return this node, but make the codebase ID one to skip
                node.tempoElement = new tempoElement_1.TempoElement(exports.SKIP_ROOT_CODEBASE_ID, node.tempoElement.storyboardId, node.tempoElement.uniquePath);
                delete elementKeyToNavNode[node.tempoElement.getKey()];
            }
        }
    };
    processNode(finishedNavTree, false);
    const postProcess = (node, level) => {
        // Remove the react fiber node after processing
        delete node['reactFiberNode'];
        node.level = level;
        node.children.forEach((child) => {
            postProcess(child, node.tempoElement.codebaseId === exports.SKIP_ROOT_CODEBASE_ID
                ? level
                : level + 1);
        });
    };
    postProcess(treeToReturn, 0);
    return treeToReturn;
};
const addNavTreeBuiltCallback = (callbackToAdd) => {
    const { callbackFn, state } = callbackToAdd;
    const callbacks = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.NAV_TREE_CALLBACKS) || [];
    // Sort the multiSelectedElementKeys for consistency before adding
    state.multiSelectedElementKeys = (state.multiSelectedElementKeys || []).sort();
    const existingCallback = callbacks.find((callback) => callback.callbackFn.toString() === callbackFn.toString() &&
        callback.state.selectedElementKey === state.selectedElementKey &&
        callback.state.multiSelectedElementKeys.join(',') ===
            state.multiSelectedElementKeys.join(','));
    if (existingCallback) {
        return;
    }
    callbacks.push(callbackToAdd);
    (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.NAV_TREE_CALLBACKS, callbacks);
};
exports.addNavTreeBuiltCallback = addNavTreeBuiltCallback;
const runNavTreeBuiltCallbacks = () => {
    const callbacks = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.NAV_TREE_CALLBACKS) || [];
    if (!callbacks.length) {
        return;
    }
    const currentSelectedKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
    const multiSelectedElementKeys = ((0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS) || []).sort();
    callbacks.forEach((callback) => {
        const { callbackFn, state } = callback;
        if (state.selectedElementKey === currentSelectedKey &&
            state.multiSelectedElementKeys.join(',') ===
                multiSelectedElementKeys.join(',')) {
            callbackFn();
        }
    });
    (0, sessionStorageUtils_1.removeMemoryStorageItem)(sessionStorageUtils_1.NAV_TREE_CALLBACKS);
};
exports.runNavTreeBuiltCallbacks = runNavTreeBuiltCallbacks;
//# sourceMappingURL=data:application/json;base64,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