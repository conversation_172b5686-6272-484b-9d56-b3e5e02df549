/**
 * Taken from https://github.com/baruchvlz/resq/blob/master/src/utils.js
 * but improved to work with all versions of react
 */
export declare const findReactInstance: (element: any) => any;
export declare const getRootReactElement: () => any;
export declare const getElementNameString: (element: any) => any;
export declare const buildNodeTree: (element: any, parentTreeNode: any) => any;
export declare const getDomElementForReactNode: (node: any) => any;
export declare const getElementName: (type: any) => any;
/**
 * @param tree
 * @param searchFn
 * @param firstOnly if set, returns only the first element in a breadth-firth search
 * @returns
 */
export declare const findElementInTree: (tree: any, searchFn: (node: any) => boolean, firstOnly?: boolean) => any[];
