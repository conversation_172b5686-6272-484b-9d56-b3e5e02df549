apiVersion: v1
kind: ConfigMap
metadata:
  name: streamscale-config
data:
  redis.addr: "redis-service:6379"
  aws.region: "us-east-1"
  s3.endpoint: "https://s3.amazonaws.com"
  s3.input_bucket: "streamscale-input"
  s3.output_bucket: "streamscale-output"
  s3.cdn_endpoint: "https://cdn.streamscale.com"
  postgres.host: "postgres-service"
  postgres.port: "5432"
  postgres.db: "streamscale"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: streamscale-worker-config
data:
  config.yml: |
    server:
      appVersion: "1.0.0"
      port: ":8080"
      mode: "development"
      jwtSecretKey: "${JWT_SECRET_KEY}"

    postgres:
      host: "${POSTGRES_HOST}"
      port: ${POSTGRES_PORT}
      user: "${POSTGRES_USER}"
      password: "${POSTGRES_PASSWORD}"
      name: "${POSTGRES_DB}"
      pgDriver: "pgx"

    redis:
      redisAddr: "${REDIS_ADDR}"
      redisPassword: "${REDIS_PASSWORD}"
      redisDB: "0"
      db: 0
      minIdleConns: 5
      poolSize: 10
      poolTimeout: 15
      jobQueueKey: "video_jobs"

    s3:
      endpoint: "${S3_ENDPOINT}"
      region: "${AWS_REGION}"
      accessKey: "${AWS_ACCESS_KEY}"
      secretKey: "${AWS_SECRET_KEY}"
      inputBucket: "${S3_INPUT_BUCKET}"
      outputBucket: "${S3_OUTPUT_BUCKET}"
      cdnEndpoint: "${S3_CDN_ENDPOINT}"

    logger:
      development: true
      disableCaller: false
      disableStacktrace: false
      encoding: "json"
      level: "info"

    worker:
      workerCount: 8
      maxCPUUsage: 85.0

    container:
      image: "streamscale/worker:latest"
      cpuLimit: 2.0
      memoryMB: 2048
