"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeSessionStorageItem = exports.setSessionStorageItem = exports.getSessionStorageItem = exports.removeMemoryStorageItem = exports.setMemoryStorageItem = exports.getMemoryStorageItem = exports.CURRENT_NAV_TREE = exports.ELEMENT_KEY_TO_NAV_NODE = exports.ELEMENT_KEY_TO_LOOKUP_LIST = exports.NAV_TREE_CALLBACKS = exports.IS_FLUSHING = exports.HOT_RELOADING = exports.TEXT_EDIT = exports.HOVERED_ELEMENT_KEY = exports.MULTI_SELECTED_ELEMENT_KEYS = exports.SELECTED_ELEMENT_KEY = exports.SAVED_STORYBOARD_COMPONENT_FILENAME = exports.ORIGINAL_STORYBOARD_URL = exports.STORYBOARD_TYPE = exports.STORYBOARD_COMPONENT = exports.SCOPE_LOOKUP = exports.TREE_ELEMENT_LOOKUP = void 0;
// Memory Storage Objects
exports.TREE_ELEMENT_LOOKUP = 'TREE_ELEMENT_LOOKUP';
exports.SCOPE_LOOKUP = 'SCOPE_LOOKUP';
exports.STORYBOARD_COMPONENT = 'STORYBOARD_COMPONENT';
exports.STORYBOARD_TYPE = 'STORYBOARD_TYPE';
exports.ORIGINAL_STORYBOARD_URL = 'ORIGINAL_STORYBOARD_URL';
exports.SAVED_STORYBOARD_COMPONENT_FILENAME = 'SAVED_STORYBOARD_COMPONENT_FILENAME';
exports.SELECTED_ELEMENT_KEY = 'SELECTED_ELEMENT_KEY';
exports.MULTI_SELECTED_ELEMENT_KEYS = 'MULTI_SELECTED_ELEMENT_KEYS';
exports.HOVERED_ELEMENT_KEY = 'HOVERED_ELEMENT_KEY';
exports.TEXT_EDIT = 'TEXT_EDIT';
exports.HOT_RELOADING = 'HOT_RELOADING';
exports.IS_FLUSHING = 'IS_FLUSHING';
exports.NAV_TREE_CALLBACKS = 'NAV_TREE_CALLBACKS';
// Generated when creating the nav tree, used for outlines
exports.ELEMENT_KEY_TO_LOOKUP_LIST = 'ELEMENT_KEY_TO_LOOKUP_LIST';
exports.ELEMENT_KEY_TO_NAV_NODE = 'ELEMENT_KEY_TO_NAV_NODE';
exports.CURRENT_NAV_TREE = 'CURRENT_NAV_TREE';
const inMemoryStorage = {};
const getMemoryStorageItem = (key) => {
    return inMemoryStorage[key];
};
exports.getMemoryStorageItem = getMemoryStorageItem;
const setMemoryStorageItem = (key, value) => {
    inMemoryStorage[key] = value;
    if (!value) {
        delete inMemoryStorage[key];
    }
};
exports.setMemoryStorageItem = setMemoryStorageItem;
const removeMemoryStorageItem = (key) => {
    delete inMemoryStorage[key];
};
exports.removeMemoryStorageItem = removeMemoryStorageItem;
const getSessionStorageItem = (key, storyboardId) => {
    return sessionStorage.getItem(`${storyboardId}_${key}`);
};
exports.getSessionStorageItem = getSessionStorageItem;
const setSessionStorageItem = (key, value, storyboardId) => {
    if (!value) {
        (0, exports.removeSessionStorageItem)(key, storyboardId);
        return;
    }
    sessionStorage.setItem(`${storyboardId}_${key}`, value);
};
exports.setSessionStorageItem = setSessionStorageItem;
const removeSessionStorageItem = (key, storyboardId) => {
    sessionStorage.removeItem(`${storyboardId}_${key}`);
};
exports.removeSessionStorageItem = removeSessionStorageItem;
//# sourceMappingURL=data:application/json;base64,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