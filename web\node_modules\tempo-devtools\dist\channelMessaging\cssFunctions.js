"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ruleMatchesElement = exports.getElementClassList = exports.getCssEvals = exports.cssEval = exports.processRulesForSelectedElement = exports.setModifiersForSelectedElement = exports.parse = void 0;
// @ts-ignore
const jquery_1 = __importDefault(require("jquery"));
const identifierUtils_1 = require("./identifierUtils");
const cssRuleUtils_1 = require("./cssRuleUtils");
const constantsAndTypes_1 = require("./constantsAndTypes");
const uuid_1 = require("uuid");
const specificity_1 = require("specificity");
const tempoElement_1 = require("./tempoElement");
const css_selector_parser_1 = require("css-selector-parser");
const sessionStorageUtils_1 = require("./sessionStorageUtils");
const navTreeUtils_1 = require("./navTreeUtils");
exports.parse = (0, css_selector_parser_1.createParser)({
    syntax: {
        baseSyntax: 'latest',
        pseudoClasses: {
            unknown: 'accept',
            definitions: {
                Selector: ['has'],
            },
        },
        pseudoElements: {
            unknown: 'accept',
        },
        combinators: ['>', '+', '~'],
        attributes: {
            operators: ['^=', '$=', '*=', '~='],
        },
        classNames: true,
        namespace: {
            wildcard: true,
        },
        tag: {
            wildcard: true,
        },
    },
    substitutes: true,
});
const addCSSRule = (styleSheet, selector, rules, index) => {
    try {
        if (styleSheet.insertRule) {
            styleSheet.insertRule(`${selector} { ${rules} }`, index);
        }
        else {
            styleSheet.addRule(selector, rules, index);
        }
    }
    catch (e) {
        console.log('Error adding rule: ', e);
    }
};
/**
 * This method filters and process media query rules for responsive modifiers to extract Tailwind responsive classes.
 * A Tailwind responsive modifiers takes the form:
 *
 *   {sm,md,lg...}:className
 *
 * which is represented as:
 *
 * @media (min-width: 640px) {
 *    .sm\:className {
 *     ...
 *   }
 * }
 *
 * This is why we need to filter for media query rules with min-width and then extract the class name.
 * @param rule
 * @returns
 */
const processMediaQueryRulesForResponsiveModifiers = (rule) => {
    let rules = [];
    if (rule instanceof CSSMediaRule) {
        // Loop through each CSSRule within the CSSMediaRule
        for (let innerRule of rule.cssRules) {
            // Check for min-width in media queries and that it is a style rule
            if (rule.media.mediaText.includes('min-width') &&
                innerRule instanceof CSSStyleRule) {
                const parsedIsSelector = (0, exports.parse)(innerRule.selectorText);
                if (parsedIsSelector.type !== 'Selector') {
                    continue;
                }
                const lastRule = parsedIsSelector.rules[0];
                const classNames = lastRule.items.filter((item) => item.type === 'ClassName').map((item) => item.name);
                if (classNames.length !== 1) {
                    continue;
                }
                // Extract Tailwind responsive modifiers
                rules.push({
                    class: classNames[0],
                    pseudos: extractTailwindPrefixes(classNames[0]),
                    cssText: innerRule.style.cssText,
                    style: innerRule.style,
                });
            }
        }
    }
    return rules;
};
/**
 * Since Tailwind CSS responsive modifiers are not CSS pseudo classes, we need to extract them from the class name.
 * We use a regex to match the responsive prefixes and return them as a set.
 * @param selectorText
 * @returns Set[prefixes]
 */
const extractTailwindPrefixes = (selectorText) => {
    // This regex matches classes with responsive prefixes that might be preceded by a period or another colon
    const prefixRegex = /(?:\b|(?<=[:.]))(sm|md|lg|xl|2xl)\\?:[\w-]+/g;
    const matches = selectorText.match(prefixRegex) || [];
    const prefixes = matches.map((match) => {
        // Find the index of the colon or escaped colon
        const index = match.indexOf(match.includes('\\:') ? '\\:' : ':');
        return match.substring(0, index);
    });
    return [...new Set(prefixes)]; // Remove duplicates
};
/**
 * Tailwind CSS dark mode classes (< 3.4.1) are specified using the `:is` pseudo selector and take the form
 *   :is(.dark .dark:bg-red-200)
 * This is to support the behaviour that dark mode classes are applied to the element when the dark class is present in the parent.
 *
 * TODO: We should support the new Tailwind CSS dark mode classes in 3.4.1 and above which are specified using the `@media (prefers-color-scheme: dark)` media query.
 * @param isSelectorString
 * @returns
 */
const processIsSelectorForDarkMode = (isSelector) => {
    if (isSelector.type !== 'Selector') {
        return;
    }
    const firstRule = isSelector.rules[0];
    const classNames = firstRule.items.filter((item) => item.type === 'ClassName').map((item) => item.name);
    if (classNames.length === 0 || classNames[0] !== 'dark') {
        return;
    }
    const nestedRule = firstRule.nestedRule;
    if (!nestedRule) {
        return;
    }
    let darkModeClasses = [];
    const nestedClassNames = nestedRule.items.filter((item) => item.type === 'ClassName').map((item) => item.name);
    if (nestedClassNames.length > 1) {
        console.log('Skipping is selector with multiple classes', firstRule);
        return;
    }
    darkModeClasses.push({
        class: nestedClassNames[0],
        pseudos: [
            'dark',
            ...nestedRule.items.filter((item) => item.type === 'PseudoClass').map((p) => p.name),
        ],
    });
    return darkModeClasses;
};
const setModifiersForSelectedElement = (parentPort, modifiers, selectedElementKey) => {
    // Remove all existing force classes from entire document
    const allElements = document.querySelectorAll('[class*="tempo-force-"]');
    allElements.forEach((element) => {
        const classes = Array.from(element.classList);
        classes.forEach((cls) => {
            if (cls.startsWith('tempo-force-')) {
                element.classList.remove(cls);
            }
        });
    });
    const selectedElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
    if (selectedElement.isEmpty()) {
        return;
    }
    const selectedDomElement = (0, identifierUtils_1.getNodeForElementKey)(selectedElement.getKey());
    if (!selectedDomElement) {
        return;
    }
    modifiers.forEach((modifier) => {
        selectedDomElement.classList.add('tempo-force-' + modifier);
    });
};
exports.setModifiersForSelectedElement = setModifiersForSelectedElement;
const processRulesForSelectedElement = (parentPort, cssElementLookup, selectedElementKey) => {
    var _a, _b, _c, _d, _e;
    // TODO: this whole function is slow, fix
    if (!cssElementLookup) {
        return;
    }
    const selectedElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
    if (selectedElement.isEmpty()) {
        return;
    }
    const selectedDomElement = (0, identifierUtils_1.getNodeForElementKey)(selectedElement.getKey());
    const multiSelectedElementKeys = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS) || [];
    /**
     * If there's no selected DOM element yet, it implies the nav tree isn't built yet.
     * We register a callback to defer the processing of the rules until the nav tree is built.
     */
    if (!selectedDomElement) {
        (0, navTreeUtils_1.addNavTreeBuiltCallback)({
            callbackFn: () => {
                (0, exports.processRulesForSelectedElement)(parentPort, cssElementLookup, selectedElementKey);
            },
            state: {
                selectedElementKey: selectedElementKey,
                multiSelectedElementKeys: multiSelectedElementKeys,
            },
        });
        return;
    }
    const newProcessedCssRules = [];
    const extractedKnownClasses = new Set();
    const knownSelectors = new Set();
    // First get the inline style of the element
    const inlineStyleRule = {
        filename: '',
        selector: 'element.style',
        source: {},
        styles: {},
        applied: true,
        codebaseId: 'element.style',
        removable: false,
        allowChanges: true,
    };
    for (let i = 0; i < ((_a = selectedDomElement === null || selectedDomElement === void 0 ? void 0 : selectedDomElement.style) === null || _a === void 0 ? void 0 : _a.length) || 0; i++) {
        const cssName = selectedDomElement.style[i];
        // @ts-ignore
        inlineStyleRule.styles[cssName] = selectedDomElement.style[cssName];
    }
    newProcessedCssRules.push(inlineStyleRule);
    // Only check the inline-styles of the parent once
    let checkedInlineStylesOfParent = false;
    const directMatchCssRules = [];
    const otherCssRules = [];
    Object.keys(cssElementLookup).forEach((codebaseId) => {
        var _a;
        const cssRule = cssElementLookup[codebaseId];
        knownSelectors.add(cssRule.selector);
        if (!(0, cssRuleUtils_1.isCssSelectorValid)(cssRule.selector)) {
            return;
        }
        (0, cssRuleUtils_1.getAllClassesFromSelector)(cssRule.selector).forEach((cls) => {
            extractedKnownClasses.add(cls);
        });
        // First check if a rule directly matches
        if ((0, cssRuleUtils_1.isCssSelectorValid)(cssRule.selector) &&
            (selectedDomElement === null || selectedDomElement === void 0 ? void 0 : selectedDomElement.matches(cssRule.selector))) {
            directMatchCssRules.push(Object.assign(Object.assign({}, cssRule), { applied: true, allowChanges: true, removable: (0, cssRuleUtils_1.canRemoveCssClassFromElement)(cssRule.selector, selectedDomElement) }));
            return;
        }
        // In order to make the parentElement.style selector unique
        let parentElementIndex = 0;
        // Then check the parents if it's a rule with properties that are inherited
        let parentDomElement = selectedDomElement === null || selectedDomElement === void 0 ? void 0 : selectedDomElement.parentElement;
        const inheritedStyles = {};
        while (parentDomElement) {
            // Inline styles are prioritized over rule based styles
            if (!checkedInlineStylesOfParent) {
                const inlineStyleOfParent = {};
                for (let i = 0; i < ((_a = parentDomElement === null || parentDomElement === void 0 ? void 0 : parentDomElement.style) === null || _a === void 0 ? void 0 : _a.length) || 0; i++) {
                    const cssName = parentDomElement.style[i];
                    if (constantsAndTypes_1.INHERITABLE_CSS_PROPS[cssName]) {
                        inlineStyleOfParent[cssName] = parentDomElement.style[cssName];
                    }
                }
                if (Object.keys(inlineStyleOfParent).length !== 0) {
                    otherCssRules.push({
                        filename: '',
                        // TODO: make this unique
                        selector: `parentElement${parentElementIndex}.style`,
                        inherited: true,
                        source: {},
                        styles: inlineStyleOfParent,
                        applied: true,
                        codebaseId: `parentElement${parentElementIndex}.style`,
                        removable: false,
                        allowChanges: false,
                    });
                }
            }
            // Css defined styles
            if ((0, cssRuleUtils_1.isCssSelectorValid)(cssRule.selector) &&
                !(parentDomElement === null || parentDomElement === void 0 ? void 0 : parentDomElement.matches(cssRule.selector))) {
                parentDomElement = parentDomElement.parentElement;
                continue;
            }
            Object.keys((cssRule === null || cssRule === void 0 ? void 0 : cssRule.styles) || {}).forEach((cssName) => {
                // Prioritize inherited styles that are further down the tree
                if (constantsAndTypes_1.INHERITABLE_CSS_PROPS[cssName] &&
                    inheritedStyles[cssName] !== null) {
                    inheritedStyles[cssName] = cssRule.styles[cssName];
                }
            });
            parentDomElement = parentDomElement.parentElement;
            parentElementIndex += 1;
        }
        // Check once across all css rules
        checkedInlineStylesOfParent = true;
        // Just because a css rule is inherited doesn't mean it can't be eligible to apply,
        // so do not return after appending this rule
        if (Object.keys(inheritedStyles).length !== 0) {
            otherCssRules.push(Object.assign(Object.assign({}, cssRule), { inherited: true, styles: inheritedStyles, applied: true, removable: false, allowChanges: false }));
        }
        // Finally check if it's a rule that can be applied if clases are changed
        otherCssRules.push(Object.assign(Object.assign({}, cssRule), { applied: false, allowChanges: false, eligibleToApply: (0, cssRuleUtils_1.canApplyCssRuleToElement)(cssRule.selector, selectedDomElement) }));
    });
    const mainStyleSheet = document.styleSheets[0];
    // Add any rules not previously added that are available in the stylesheets as read-only
    for (let i = 0; i < document.styleSheets.length; i += 1) {
        const sheet = document.styleSheets[i];
        let rules = null;
        try {
            rules = sheet.cssRules;
        }
        catch (e) {
            console.log(e);
            try {
                rules = sheet.rules;
            }
            catch (e) {
                console.log(e);
            }
        }
        if (!rules) {
            continue;
        }
        for (let j = 0; j < rules.length; j += 1) {
            const rule = rules[j];
            /**
             * Handle Tailwind CSS responsive modifiers
             */
            const responsiveModifiers = processMediaQueryRulesForResponsiveModifiers(rule);
            if (responsiveModifiers.length > 0) {
                for (let k = 0; k < responsiveModifiers.length; k++) {
                    const modifier = responsiveModifiers[k];
                    if (!(selectedDomElement === null || selectedDomElement === void 0 ? void 0 : selectedDomElement.matches('.' + CSS.escape(modifier.class)))) {
                        continue;
                    }
                    const styling = {};
                    for (let l = 0; l < ((_b = modifier === null || modifier === void 0 ? void 0 : modifier.style) === null || _b === void 0 ? void 0 : _b.length) || 0; l += 1) {
                        const cssName = modifier === null || modifier === void 0 ? void 0 : modifier.style[l];
                        // @ts-ignore;
                        styling[cssName] = modifier === null || modifier === void 0 ? void 0 : modifier.style[cssName];
                    }
                    const ruleToPush = {
                        filename: undefined,
                        selector: CSS.escape('.' + modifier.class),
                        classParsed: modifier.class,
                        source: {},
                        styles: styling,
                        applied: true,
                        modifiers: Object.assign({}, modifier.pseudos.reduce((acc, pseudo) => {
                            acc[pseudo] = true;
                            return acc;
                        }, {})),
                        // Generate a random codebase ID to use for selection
                        // Note: this ID is shown as a backup in the overridden tooltip
                        codebaseId: `${modifier.class} ${(0, uuid_1.v4)().toString()}`,
                        removable: false,
                        allowChanges: false,
                        cssText: modifier.cssText,
                    };
                    directMatchCssRules.push(ruleToPush);
                }
            }
            if (!rule.selectorText) {
                continue;
            }
            if (knownSelectors.has(rule.selectorText)) {
                continue;
            }
            const parsedCssRule = (0, exports.parse)(rule.selectorText);
            if (parsedCssRule.type !== 'Selector') {
                continue;
            }
            const firstRule = parsedCssRule.rules[0];
            if (!firstRule) {
                continue;
            }
            /**
             * This is a special case for the `:is` pseudo selector, which is how Tailwind specifies dark mode classes.
             */
            const classNames = firstRule.items.filter((item) => item.type === 'ClassName').map((item) => item.name);
            const pseudos = firstRule.items.filter((item) => item.type === 'PseudoClass');
            // TODO: Add support for https://github.com/tailwindlabs/tailwindcss/pull/13379 (~3.4.4)
            if (classNames.length === 0 &&
                pseudos.length === 1 &&
                pseudos[0].name === 'is') {
                const pseudo = pseudos[0];
                if (pseudo && ((_c = pseudo.argument) === null || _c === void 0 ? void 0 : _c.type) === 'Selector') {
                    const darkModeClasses = processIsSelectorForDarkMode(pseudo.argument);
                    if (darkModeClasses) {
                        for (const darkModeClass of darkModeClasses) {
                            if (!(selectedDomElement === null || selectedDomElement === void 0 ? void 0 : selectedDomElement.matches('.' + CSS.escape(darkModeClass.class)))) {
                                continue;
                            }
                            const styling = {};
                            for (let k = 0; k < ((_d = rule === null || rule === void 0 ? void 0 : rule.style) === null || _d === void 0 ? void 0 : _d.length) || 0; k += 1) {
                                const cssName = rule.style[k];
                                styling[cssName] = rule.style[cssName];
                            }
                            const ruleToPush = {
                                filename: undefined,
                                selector: CSS.escape('.' + darkModeClass.class),
                                classParsed: darkModeClass.class,
                                source: {},
                                styles: styling,
                                applied: true,
                                modifiers: Object.assign({}, darkModeClass.pseudos.reduce((acc, pseudo) => {
                                    acc[pseudo] = true;
                                    return acc;
                                }, {})),
                                // Generate a random codebase ID to use for selection
                                // Note: this ID is shown as a backup in the overridden tooltip
                                codebaseId: `${rule.selectorText} ${(0, uuid_1.v4)().toString()}`,
                                removable: false,
                                allowChanges: false,
                                cssText: rule.style.cssText,
                            };
                            directMatchCssRules.push(ruleToPush);
                        }
                    }
                }
            }
            if (classNames.length === 0 || classNames.length > 1) {
                continue;
            }
            const cls = classNames[0];
            const pseudoClasses = firstRule.items.filter((item) => item.type === 'PseudoClass').map((p) => p.name);
            try {
                if (selectedDomElement === null || selectedDomElement === void 0 ? void 0 : selectedDomElement.matches('.' + CSS.escape(cls))) {
                    const styling = {};
                    for (let k = 0; k < ((_e = rule === null || rule === void 0 ? void 0 : rule.style) === null || _e === void 0 ? void 0 : _e.length) || 0; k += 1) {
                        const cssName = rule.style[k];
                        styling[cssName] = rule.style[cssName];
                    }
                    directMatchCssRules.push({
                        filename: undefined,
                        selector: rule.selectorText,
                        classParsed: cls,
                        source: {},
                        styles: styling,
                        applied: true,
                        modifiers: Object.assign({}, pseudoClasses.reduce((acc, pseudo) => {
                            acc[pseudo.name] = true;
                            return acc;
                        }, {})),
                        // Generate a random codebase ID to use for selection
                        // Note: this ID is shown as a backup in the overridden tooltip
                        codebaseId: `${rule.selectorText} ${(0, uuid_1.v4)().toString()}`,
                        removable: false,
                        allowChanges: false,
                        cssText: rule.style.cssText,
                    });
                }
                else {
                    // console.log("NO MATCH", cls)
                }
            }
            catch (e) {
                // console.error(e);
            }
        }
    }
    // For each direct match rule, check if it has modifiers and create a new rule for each modifier.
    for (let i = 0; i < directMatchCssRules.length; i++) {
        const currentRule = directMatchCssRules[i];
        if (!currentRule.modifiers) {
            continue;
        }
        const rulePseudos = Object.keys(currentRule.modifiers);
        if (rulePseudos.length < 1) {
            continue;
        }
        const cls = currentRule.classParsed;
        if (!cls) {
            continue;
        }
        const cssText = currentRule.cssText;
        if (!cssText) {
            continue;
        }
        // Create a new custom css rule for ones that have pseudo selectors.
        // Use the parseClass as the selector and add `tempo-force-[pseudo]` for each pseudo selector
        const pseudoSelector = rulePseudos
            .map((pseudo) => '.tempo-force-' + pseudo)
            .join('');
        const newSelector = '.' + CSS.escape(cls) + pseudoSelector;
        const newRules = cssText;
        // // Inject new rule into the stylesheet
        addCSSRule(mainStyleSheet, newSelector, newRules, mainStyleSheet.cssRules.length);
    }
    const newList = newProcessedCssRules
        .concat(directMatchCssRules.sort((a, b) => {
        try {
            return -(0, specificity_1.compare)(a.selector, b.selector);
        }
        catch (_a) {
            // Put the invalid elements at the end
            let aValid = true;
            try {
                (0, specificity_1.compare)(a.selector, 'body');
            }
            catch (e) {
                aValid = false;
            }
            let bValid = true;
            try {
                (0, specificity_1.compare)(b.selector, 'body');
            }
            catch (e) {
                bValid = false;
            }
            if (aValid && !bValid) {
                return -1;
            }
            if (!aValid && bValid) {
                return 1;
            }
            return 0;
        }
    }))
        .concat(otherCssRules);
    parentPort.postMessage({
        id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.PROCESSED_CSS_RULES_FOR_ELEMENT,
        processedCssRules: newList,
    });
};
exports.processRulesForSelectedElement = processRulesForSelectedElement;
const cssEval = (element, property) => {
    return window.getComputedStyle(element, null).getPropertyValue(property);
};
exports.cssEval = cssEval;
const getCssEvals = (parentPort, selectedElementKey) => {
    var _a;
    let cssEvals = {};
    const selectdElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
    if (selectdElement.isEmpty()) {
        return;
    }
    const selectedDomElement = (0, identifierUtils_1.getNodeForElementKey)(selectdElement.getKey());
    if (!selectedDomElement) {
        return;
    }
    constantsAndTypes_1.CSS_VALUES_TO_COLLECT.forEach((cssName) => {
        cssEvals[cssName] = (0, exports.cssEval)(selectedDomElement, cssName);
    });
    const parentCssEvals = {};
    const parentElement = selectedDomElement.parentElement;
    if (parentElement) {
        constantsAndTypes_1.CSS_VALUES_TO_COLLECT_FOR_PARENT.forEach((cssName) => {
            parentCssEvals[cssName] = (0, exports.cssEval)(selectedDomElement.parentElement, cssName);
        });
        // Use jQuery to check if 'dark' class is in any ancestor of the parent element
        let darkEnabledInParent = ((_a = (0, jquery_1.default)((0, identifierUtils_1.getNodeForElementKey)(selectdElement.getKey()))) === null || _a === void 0 ? void 0 : _a.closest('.dark').length) > 0;
        parentCssEvals['darkEnabledInParent'] = darkEnabledInParent;
    }
    cssEvals['parent'] = parentCssEvals;
    parentPort.postMessage({
        id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.CSS_EVALS_FOR_ELEMENT,
        cssEvals,
    });
};
exports.getCssEvals = getCssEvals;
const getElementClassList = (parentPort, selectedElementKey) => {
    const selectdElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
    if (selectdElement.isEmpty()) {
        return;
    }
    const selectedDomElement = (0, identifierUtils_1.getNodeForElementKey)(selectdElement.getKey());
    if (!selectedDomElement) {
        return;
    }
    parentPort.postMessage({
        id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.ELEMENT_CLASS_LIST,
        classList: Array.from(selectedDomElement.classList),
    });
};
exports.getElementClassList = getElementClassList;
const ruleMatchesElement = (parentPort, messageId, rule, selectedElementKey) => {
    if (!rule) {
        return;
    }
    const selectdElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
    if (selectdElement.isEmpty()) {
        return;
    }
    const selectedDomElement = (0, identifierUtils_1.getNodeForElementKey)(selectdElement.getKey());
    if (!selectedDomElement) {
        return;
    }
    parentPort.postMessage({
        id: messageId,
        matches: selectedDomElement === null || selectedDomElement === void 0 ? void 0 : selectedDomElement.matches(rule),
    });
};
exports.ruleMatchesElement = ruleMatchesElement;
//# sourceMappingURL=data:application/json;base64,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