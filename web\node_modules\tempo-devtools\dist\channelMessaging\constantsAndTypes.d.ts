export declare const CSS_VALUES_TO_COLLECT: Set<string>;
export declare const CSS_VALUES_TO_COLLECT_FOR_PARENT: Set<string>;
export declare const INHERITABLE_CSS_PROPS: any;
export declare enum FIXED_IFRAME_MESSAGE_IDS {
    HOVERED_ELEMENT_KEY = "HOVERED_ELEMENT_KEY",
    SELECTED_ELEMENT_KEY = "SELECTED_ELEMENT_KEY",
    MULTI_SELECTED_ELEMENT_KEYS = "MULTI_SELECTED_ELEMENT_KEYS",
    CONTEXT_REQUESTED = "CONTEXT_REQUESTED",
    WHEEL_EVENT = "WHEEL_EVENT",
    NAV_TREE = "NAV_TREE",
    PROCESSED_CSS_RULES_FOR_ELEMENT = "PROCESSED_CSS_RULES_FOR_ELEMENT",
    CSS_EVALS_FOR_ELEMENT = "CSS_EVALS_FOR_ELEMENT",
    ELEMENT_CLASS_LIST = "ELEMENT_CLASS_LIST",
    KEY_DOWN_EVENT = "KEY_DOWN_EVENT",
    KEY_UP_EVENT = "KEY_UP_EVENT",
    MOUSE_MOVE_EVENT = "MOUSE_MOVE_EVENT",
    DRAG_START_EVENT = "DRAG_START_EVENT",
    DRAG_END_EVENT = "DRAG_END_EVENT",
    DRAG_CANCEL_EVENT = "DRAG_CANCEL_EVENT",
    LATEST_HREF = "LATEST_HREF",
    LATEST_HYDRATION_ERROR_STATUS = "LATEST_HYDRATION_ERROR_STATUS",
    START_EDITING_TEXT = "START_EDITING_TEXT",
    EDITED_TEXT = "EDITED_TEXT",
    INSTANT_UPDATE_DONE = "INSTANT_UPDATE_DONE",
    EDIT_DYNAMIC_TEXT = "EDIT_DYNAMIC_TEXT"
}
export declare const DELETE_STYLE_CONSTANT: any;
export declare const SELECT_OR_HOVER_STORYBOARD: string;
export declare enum STORYBOARD_HYDRATION_STATUS {
    OTHER_ERROR = "other_error",
    ERROR = "error",
    NO_ERROR = "no_error"
}
