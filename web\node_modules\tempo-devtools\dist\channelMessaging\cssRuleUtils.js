"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.camelToSnakeCase = exports.isCssSelectorValid = exports.getAllClassesFromSelector = exports.canRemoveCssClassFromElement = exports.canApplyCssRuleToElement = void 0;
const cssFunctions_1 = require("./cssFunctions");
const canApplyCssRuleToElement = (cssRule, element) => {
    var _a;
    try {
        if (!element) {
            return false;
        }
        if (!(0, exports.isCssSelectorValid)(cssRule)) {
            return false;
        }
        if (element.matches(cssRule)) {
            return false;
        }
        const parsedCssRule = (0, cssFunctions_1.parse)(cssRule);
        let lastRule = parsedCssRule;
        while (lastRule.nestedRule) {
            lastRule = lastRule.nestedRule;
        }
        const addedClasses = [];
        const classes = new Set(element.classList);
        (_a = lastRule.items) === null || _a === void 0 ? void 0 : _a.forEach((item) => {
            if (item.type === 'ClassName') {
                const cls = item.name;
                if (!classes.has(cls)) {
                    element.classList.add(cls);
                    addedClasses.push(cls);
                }
            }
        });
        const canApply = element.matches(cssRule);
        addedClasses.forEach((cls) => {
            element.classList.remove(cls);
        });
        return canApply;
    }
    catch (e) {
        console.error(e);
        return false;
    }
};
exports.canApplyCssRuleToElement = canApplyCssRuleToElement;
const canRemoveCssClassFromElement = (cssRule, element) => {
    var _a;
    try {
        if (!(0, exports.isCssSelectorValid)(cssRule)) {
            return false;
        }
        if (!element.matches(cssRule)) {
            return false;
        }
        const parsedCssRule = (0, cssFunctions_1.parse)(cssRule);
        let lastRule = parsedCssRule;
        while (lastRule.nestedRule) {
            lastRule = lastRule.nestedRule;
        }
        const removedClasses = [];
        const classes = new Set(element.classList);
        (_a = lastRule.items) === null || _a === void 0 ? void 0 : _a.forEach((item) => {
            if (item.type === 'ClassName') {
                const cls = item.name;
                if (!classes.has(cls)) {
                    return;
                }
                element.classList.remove(cls);
                removedClasses.push(cls);
            }
        });
        const canRemove = !element.matches(cssRule);
        removedClasses.forEach((cls) => {
            element.classList.add(cls);
        });
        return canRemove;
    }
    catch (e) {
        console.error(e);
        return false;
    }
};
exports.canRemoveCssClassFromElement = canRemoveCssClassFromElement;
const getAllClassesFromSelector = (cssSelector) => {
    try {
        if (!(0, exports.isCssSelectorValid)(cssSelector)) {
            return new Set();
        }
        const parsedCssRule = (0, cssFunctions_1.parse)(cssSelector);
        let traverseRule = parsedCssRule;
        const allClasses = new Set();
        while (traverseRule) {
            const items = traverseRule.items || [];
            items.forEach((item) => {
                if (item.type === 'ClassName') {
                    allClasses.add(item.name);
                }
            });
            traverseRule = traverseRule.nestedRule;
        }
        return allClasses;
    }
    catch (e) {
        console.log('Failed to parse classes from selector ' + cssSelector + ', ' + e);
        return new Set();
    }
};
exports.getAllClassesFromSelector = getAllClassesFromSelector;
const queryCheck = (s) => document.createDocumentFragment().querySelector(s);
const isCssSelectorValid = (cssSelector) => {
    try {
        queryCheck(cssSelector);
        const parsedCssRule = (0, cssFunctions_1.parse)(cssSelector);
        return true;
    }
    catch (e) {
        return false;
    }
};
exports.isCssSelectorValid = isCssSelectorValid;
const camelToSnakeCase = (str) => {
    if (!str)
        return str;
    return (str.charAt(0).toLowerCase() +
        str.substring(1).replace(/[A-Z]/g, (letter) => `-${letter.toLowerCase()}`));
};
exports.camelToSnakeCase = camelToSnakeCase;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3NzUnVsZVV0aWxzLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc3JjL2NoYW5uZWxNZXNzYWdpbmcvY3NzUnVsZVV0aWxzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7OztBQUFBLGlEQUF1QztBQUVoQyxNQUFNLHdCQUF3QixHQUFHLENBQUMsT0FBZSxFQUFFLE9BQVksRUFBRSxFQUFFOztJQUN4RSxJQUFJO1FBQ0YsSUFBSSxDQUFDLE9BQU8sRUFBRTtZQUNaLE9BQU8sS0FBSyxDQUFDO1NBQ2Q7UUFFRCxJQUFJLENBQUMsSUFBQSwwQkFBa0IsRUFBQyxPQUFPLENBQUMsRUFBRTtZQUNoQyxPQUFPLEtBQUssQ0FBQztTQUNkO1FBRUQsSUFBSSxPQUFPLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxFQUFFO1lBQzVCLE9BQU8sS0FBSyxDQUFDO1NBQ2Q7UUFFRCxNQUFNLGFBQWEsR0FBRyxJQUFBLG9CQUFLLEVBQUMsT0FBTyxDQUFDLENBQUM7UUFDckMsSUFBSSxRQUFRLEdBQVEsYUFBYSxDQUFDO1FBRWxDLE9BQU8sUUFBUSxDQUFDLFVBQVUsRUFBRTtZQUMxQixRQUFRLEdBQUcsUUFBUSxDQUFDLFVBQVUsQ0FBQztTQUNoQztRQUVELE1BQU0sWUFBWSxHQUFRLEVBQUUsQ0FBQztRQUM3QixNQUFNLE9BQU8sR0FBRyxJQUFJLEdBQUcsQ0FBQyxPQUFPLENBQUMsU0FBUyxDQUFDLENBQUM7UUFFM0MsTUFBQSxRQUFRLENBQUMsS0FBSywwQ0FBRSxPQUFPLENBQUMsQ0FBQyxJQUFTLEVBQUUsRUFBRTtZQUNwQyxJQUFJLElBQUksQ0FBQyxJQUFJLEtBQUssV0FBVyxFQUFFO2dCQUM3QixNQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDO2dCQUN0QixJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsRUFBRTtvQkFDckIsT0FBTyxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUM7b0JBQzNCLFlBQVksQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7aUJBQ3hCO2FBQ0Y7UUFDSCxDQUFDLENBQUMsQ0FBQztRQUVILE1BQU0sUUFBUSxHQUFHLE9BQU8sQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDMUMsWUFBWSxDQUFDLE9BQU8sQ0FBQyxDQUFDLEdBQVcsRUFBRSxFQUFFO1lBQ25DLE9BQU8sQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ2hDLENBQUMsQ0FBQyxDQUFDO1FBRUgsT0FBTyxRQUFRLENBQUM7S0FDakI7SUFBQyxPQUFPLENBQUMsRUFBRTtRQUNWLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDakIsT0FBTyxLQUFLLENBQUM7S0FDZDtBQUNILENBQUMsQ0FBQztBQTVDVyxRQUFBLHdCQUF3Qiw0QkE0Q25DO0FBRUssTUFBTSw0QkFBNEIsR0FBRyxDQUFDLE9BQWUsRUFBRSxPQUFZLEVBQUUsRUFBRTs7SUFDNUUsSUFBSTtRQUNGLElBQUksQ0FBQyxJQUFBLDBCQUFrQixFQUFDLE9BQU8sQ0FBQyxFQUFFO1lBQ2hDLE9BQU8sS0FBSyxDQUFDO1NBQ2Q7UUFFRCxJQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsRUFBRTtZQUM3QixPQUFPLEtBQUssQ0FBQztTQUNkO1FBRUQsTUFBTSxhQUFhLEdBQUcsSUFBQSxvQkFBSyxFQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQ3JDLElBQUksUUFBUSxHQUFRLGFBQWEsQ0FBQztRQUVsQyxPQUFPLFFBQVEsQ0FBQyxVQUFVLEVBQUU7WUFDMUIsUUFBUSxHQUFHLFFBQVEsQ0FBQyxVQUFVLENBQUM7U0FDaEM7UUFFRCxNQUFNLGNBQWMsR0FBUSxFQUFFLENBQUM7UUFDL0IsTUFBTSxPQUFPLEdBQUcsSUFBSSxHQUFHLENBQUMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBRTNDLE1BQUEsUUFBUSxDQUFDLEtBQUssMENBQUUsT0FBTyxDQUFDLENBQUMsSUFBUyxFQUFFLEVBQUU7WUFDcEMsSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLFdBQVcsRUFBRTtnQkFDN0IsTUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQztnQkFDdEIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLEVBQUU7b0JBQ3JCLE9BQU87aUJBQ1I7Z0JBRUQsT0FBTyxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUM7Z0JBQzlCLGNBQWMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7YUFDMUI7UUFDSCxDQUFDLENBQUMsQ0FBQztRQUVILE1BQU0sU0FBUyxHQUFHLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUM1QyxjQUFjLENBQUMsT0FBTyxDQUFDLENBQUMsR0FBVyxFQUFFLEVBQUU7WUFDckMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDN0IsQ0FBQyxDQUFDLENBQUM7UUFFSCxPQUFPLFNBQVMsQ0FBQztLQUNsQjtJQUFDLE9BQU8sQ0FBQyxFQUFFO1FBQ1YsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNqQixPQUFPLEtBQUssQ0FBQztLQUNkO0FBQ0gsQ0FBQyxDQUFDO0FBMUNXLFFBQUEsNEJBQTRCLGdDQTBDdkM7QUFDSyxNQUFNLHlCQUF5QixHQUFHLENBQUMsV0FBbUIsRUFBZSxFQUFFO0lBQzVFLElBQUk7UUFDRixJQUFJLENBQUMsSUFBQSwwQkFBa0IsRUFBQyxXQUFXLENBQUMsRUFBRTtZQUNwQyxPQUFPLElBQUksR0FBRyxFQUFFLENBQUM7U0FDbEI7UUFFRCxNQUFNLGFBQWEsR0FBRyxJQUFBLG9CQUFLLEVBQUMsV0FBVyxDQUFDLENBQUM7UUFDekMsSUFBSSxZQUFZLEdBQVEsYUFBYSxDQUFDO1FBQ3RDLE1BQU0sVUFBVSxHQUFHLElBQUksR0FBRyxFQUFVLENBQUM7UUFFckMsT0FBTyxZQUFZLEVBQUU7WUFDbkIsTUFBTSxLQUFLLEdBQUcsWUFBWSxDQUFDLEtBQUssSUFBSSxFQUFFLENBQUM7WUFDdkMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQVMsRUFBRSxFQUFFO2dCQUMxQixJQUFJLElBQUksQ0FBQyxJQUFJLEtBQUssV0FBVyxFQUFFO29CQUM3QixVQUFVLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztpQkFDM0I7WUFDSCxDQUFDLENBQUMsQ0FBQztZQUNILFlBQVksR0FBRyxZQUFZLENBQUMsVUFBVSxDQUFDO1NBQ3hDO1FBRUQsT0FBTyxVQUFVLENBQUM7S0FDbkI7SUFBQyxPQUFPLENBQUMsRUFBRTtRQUNWLE9BQU8sQ0FBQyxHQUFHLENBQ1Qsd0NBQXdDLEdBQUcsV0FBVyxHQUFHLElBQUksR0FBRyxDQUFDLENBQ2xFLENBQUM7UUFDRixPQUFPLElBQUksR0FBRyxFQUFFLENBQUM7S0FDbEI7QUFDSCxDQUFDLENBQUM7QUEzQlcsUUFBQSx5QkFBeUIsNkJBMkJwQztBQUVGLE1BQU0sVUFBVSxHQUFHLENBQUMsQ0FBTSxFQUFFLEVBQUUsQ0FDNUIsUUFBUSxDQUFDLHNCQUFzQixFQUFFLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDO0FBRTlDLE1BQU0sa0JBQWtCLEdBQUcsQ0FBQyxXQUFtQixFQUFFLEVBQUU7SUFDeEQsSUFBSTtRQUNGLFVBQVUsQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUV4QixNQUFNLGFBQWEsR0FBRyxJQUFBLG9CQUFLLEVBQUMsV0FBVyxDQUFDLENBQUM7UUFDekMsT0FBTyxJQUFJLENBQUM7S0FDYjtJQUFDLE9BQU8sQ0FBQyxFQUFFO1FBQ1YsT0FBTyxLQUFLLENBQUM7S0FDZDtBQUNILENBQUMsQ0FBQztBQVRXLFFBQUEsa0JBQWtCLHNCQVM3QjtBQUVLLE1BQU0sZ0JBQWdCLEdBQUcsQ0FBQyxHQUFXLEVBQUUsRUFBRTtJQUM5QyxJQUFJLENBQUMsR0FBRztRQUFFLE9BQU8sR0FBRyxDQUFDO0lBRXJCLE9BQU8sQ0FDTCxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLFdBQVcsRUFBRTtRQUMzQixHQUFHLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxRQUFRLEVBQUUsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLElBQUksTUFBTSxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsQ0FDM0UsQ0FBQztBQUNKLENBQUMsQ0FBQztBQVBXLFFBQUEsZ0JBQWdCLG9CQU8zQiIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHBhcnNlIH0gZnJvbSAnLi9jc3NGdW5jdGlvbnMnO1xuXG5leHBvcnQgY29uc3QgY2FuQXBwbHlDc3NSdWxlVG9FbGVtZW50ID0gKGNzc1J1bGU6IHN0cmluZywgZWxlbWVudDogYW55KSA9PiB7XG4gIHRyeSB7XG4gICAgaWYgKCFlbGVtZW50KSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgaWYgKCFpc0Nzc1NlbGVjdG9yVmFsaWQoY3NzUnVsZSkpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICBpZiAoZWxlbWVudC5tYXRjaGVzKGNzc1J1bGUpKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgY29uc3QgcGFyc2VkQ3NzUnVsZSA9IHBhcnNlKGNzc1J1bGUpO1xuICAgIGxldCBsYXN0UnVsZTogYW55ID0gcGFyc2VkQ3NzUnVsZTtcblxuICAgIHdoaWxlIChsYXN0UnVsZS5uZXN0ZWRSdWxlKSB7XG4gICAgICBsYXN0UnVsZSA9IGxhc3RSdWxlLm5lc3RlZFJ1bGU7XG4gICAgfVxuXG4gICAgY29uc3QgYWRkZWRDbGFzc2VzOiBhbnkgPSBbXTtcbiAgICBjb25zdCBjbGFzc2VzID0gbmV3IFNldChlbGVtZW50LmNsYXNzTGlzdCk7XG5cbiAgICBsYXN0UnVsZS5pdGVtcz8uZm9yRWFjaCgoaXRlbTogYW55KSA9PiB7XG4gICAgICBpZiAoaXRlbS50eXBlID09PSAnQ2xhc3NOYW1lJykge1xuICAgICAgICBjb25zdCBjbHMgPSBpdGVtLm5hbWU7XG4gICAgICAgIGlmICghY2xhc3Nlcy5oYXMoY2xzKSkge1xuICAgICAgICAgIGVsZW1lbnQuY2xhc3NMaXN0LmFkZChjbHMpO1xuICAgICAgICAgIGFkZGVkQ2xhc3Nlcy5wdXNoKGNscyk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGNvbnN0IGNhbkFwcGx5ID0gZWxlbWVudC5tYXRjaGVzKGNzc1J1bGUpO1xuICAgIGFkZGVkQ2xhc3Nlcy5mb3JFYWNoKChjbHM6IHN0cmluZykgPT4ge1xuICAgICAgZWxlbWVudC5jbGFzc0xpc3QucmVtb3ZlKGNscyk7XG4gICAgfSk7XG5cbiAgICByZXR1cm4gY2FuQXBwbHk7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICBjb25zb2xlLmVycm9yKGUpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufTtcblxuZXhwb3J0IGNvbnN0IGNhblJlbW92ZUNzc0NsYXNzRnJvbUVsZW1lbnQgPSAoY3NzUnVsZTogc3RyaW5nLCBlbGVtZW50OiBhbnkpID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAoIWlzQ3NzU2VsZWN0b3JWYWxpZChjc3NSdWxlKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cblxuICAgIGlmICghZWxlbWVudC5tYXRjaGVzKGNzc1J1bGUpKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgY29uc3QgcGFyc2VkQ3NzUnVsZSA9IHBhcnNlKGNzc1J1bGUpO1xuICAgIGxldCBsYXN0UnVsZTogYW55ID0gcGFyc2VkQ3NzUnVsZTtcblxuICAgIHdoaWxlIChsYXN0UnVsZS5uZXN0ZWRSdWxlKSB7XG4gICAgICBsYXN0UnVsZSA9IGxhc3RSdWxlLm5lc3RlZFJ1bGU7XG4gICAgfVxuXG4gICAgY29uc3QgcmVtb3ZlZENsYXNzZXM6IGFueSA9IFtdO1xuICAgIGNvbnN0IGNsYXNzZXMgPSBuZXcgU2V0KGVsZW1lbnQuY2xhc3NMaXN0KTtcblxuICAgIGxhc3RSdWxlLml0ZW1zPy5mb3JFYWNoKChpdGVtOiBhbnkpID0+IHtcbiAgICAgIGlmIChpdGVtLnR5cGUgPT09ICdDbGFzc05hbWUnKSB7XG4gICAgICAgIGNvbnN0IGNscyA9IGl0ZW0ubmFtZTtcbiAgICAgICAgaWYgKCFjbGFzc2VzLmhhcyhjbHMpKSB7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgZWxlbWVudC5jbGFzc0xpc3QucmVtb3ZlKGNscyk7XG4gICAgICAgIHJlbW92ZWRDbGFzc2VzLnB1c2goY2xzKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGNvbnN0IGNhblJlbW92ZSA9ICFlbGVtZW50Lm1hdGNoZXMoY3NzUnVsZSk7XG4gICAgcmVtb3ZlZENsYXNzZXMuZm9yRWFjaCgoY2xzOiBzdHJpbmcpID0+IHtcbiAgICAgIGVsZW1lbnQuY2xhc3NMaXN0LmFkZChjbHMpO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuIGNhblJlbW92ZTtcbiAgfSBjYXRjaCAoZSkge1xuICAgIGNvbnNvbGUuZXJyb3IoZSk7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59O1xuZXhwb3J0IGNvbnN0IGdldEFsbENsYXNzZXNGcm9tU2VsZWN0b3IgPSAoY3NzU2VsZWN0b3I6IHN0cmluZyk6IFNldDxzdHJpbmc+ID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAoIWlzQ3NzU2VsZWN0b3JWYWxpZChjc3NTZWxlY3RvcikpIHtcbiAgICAgIHJldHVybiBuZXcgU2V0KCk7XG4gICAgfVxuXG4gICAgY29uc3QgcGFyc2VkQ3NzUnVsZSA9IHBhcnNlKGNzc1NlbGVjdG9yKTtcbiAgICBsZXQgdHJhdmVyc2VSdWxlOiBhbnkgPSBwYXJzZWRDc3NSdWxlO1xuICAgIGNvbnN0IGFsbENsYXNzZXMgPSBuZXcgU2V0PHN0cmluZz4oKTtcblxuICAgIHdoaWxlICh0cmF2ZXJzZVJ1bGUpIHtcbiAgICAgIGNvbnN0IGl0ZW1zID0gdHJhdmVyc2VSdWxlLml0ZW1zIHx8IFtdO1xuICAgICAgaXRlbXMuZm9yRWFjaCgoaXRlbTogYW55KSA9PiB7XG4gICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICdDbGFzc05hbWUnKSB7XG4gICAgICAgICAgYWxsQ2xhc3Nlcy5hZGQoaXRlbS5uYW1lKTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICB0cmF2ZXJzZVJ1bGUgPSB0cmF2ZXJzZVJ1bGUubmVzdGVkUnVsZTtcbiAgICB9XG5cbiAgICByZXR1cm4gYWxsQ2xhc3NlcztcbiAgfSBjYXRjaCAoZSkge1xuICAgIGNvbnNvbGUubG9nKFxuICAgICAgJ0ZhaWxlZCB0byBwYXJzZSBjbGFzc2VzIGZyb20gc2VsZWN0b3IgJyArIGNzc1NlbGVjdG9yICsgJywgJyArIGUsXG4gICAgKTtcbiAgICByZXR1cm4gbmV3IFNldCgpO1xuICB9XG59O1xuXG5jb25zdCBxdWVyeUNoZWNrID0gKHM6IGFueSkgPT5cbiAgZG9jdW1lbnQuY3JlYXRlRG9jdW1lbnRGcmFnbWVudCgpLnF1ZXJ5U2VsZWN0b3Iocyk7XG5cbmV4cG9ydCBjb25zdCBpc0Nzc1NlbGVjdG9yVmFsaWQgPSAoY3NzU2VsZWN0b3I6IHN0cmluZykgPT4ge1xuICB0cnkge1xuICAgIHF1ZXJ5Q2hlY2soY3NzU2VsZWN0b3IpO1xuXG4gICAgY29uc3QgcGFyc2VkQ3NzUnVsZSA9IHBhcnNlKGNzc1NlbGVjdG9yKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBjYXRjaCAoZSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufTtcblxuZXhwb3J0IGNvbnN0IGNhbWVsVG9TbmFrZUNhc2UgPSAoc3RyOiBzdHJpbmcpID0+IHtcbiAgaWYgKCFzdHIpIHJldHVybiBzdHI7XG5cbiAgcmV0dXJuIChcbiAgICBzdHIuY2hhckF0KDApLnRvTG93ZXJDYXNlKCkgK1xuICAgIHN0ci5zdWJzdHJpbmcoMSkucmVwbGFjZSgvW0EtWl0vZywgKGxldHRlcikgPT4gYC0ke2xldHRlci50b0xvd2VyQ2FzZSgpfWApXG4gICk7XG59O1xuIl19