{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "StreamScale Analytics API", "description": "Collection for testing the StreamScale Analytics API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"auth_token\", jsonData.token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"your_password\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login to get an authentication token"}, "response": []}], "description": "Authentication endpoints"}, {"name": "Videos", "item": [{"name": "Get Video List", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "if (jsonData.videos && jsonData.videos.length > 0) {", "    pm.environment.set(\"video_id\", jsonData.videos[0].video_id);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/video/list", "host": ["{{base_url}}"], "path": ["api", "v1", "video", "list"]}, "description": "Get a list of videos to use for testing"}, "response": []}], "description": "Video endpoints for getting test data"}, {"name": "Analytics", "item": [{"name": "Get Analytics Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/analytics/summary", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "summary"]}, "description": "Get analytics summary for the authenticated user"}, "response": []}, {"name": "Record Video View", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"video_id\": \"{{video_id}}\",\n    \"duration\": 180\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/analytics/views", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "views"]}, "description": "Record a new view for a video"}, "response": []}, {"name": "Get Video Views", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/analytics/videos/{{video_id}}/views?start_date=2023-01-01&end_date=2023-12-31&limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "videos", "{{video_id}}", "views"], "query": [{"key": "start_date", "value": "2023-01-01"}, {"key": "end_date", "value": "2023-12-31"}, {"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}, "description": "Get views for a specific video"}, "response": []}, {"name": "Start Watch Session", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"session_id\", jsonData.session_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"video_id\": \"{{video_id}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/analytics/sessions/start", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "sessions", "start"]}, "description": "Start a new watch session for a video"}, "response": []}, {"name": "End Watch Session", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"session_id\": \"{{session_id}}\",\n    \"watch_duration\": 175,\n    \"completed\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/analytics/sessions/end", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "sessions", "end"]}, "description": "End a watch session and record metrics"}, "response": []}, {"name": "Get Video Performance", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/analytics/videos/{{video_id}}/performance", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "videos", "{{video_id}}", "performance"]}, "description": "Get performance metrics for a specific video"}, "response": []}, {"name": "Get Top Performing Videos", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/analytics/videos/top?limit=5", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "videos", "top"], "query": [{"key": "limit", "value": "5"}]}, "description": "Get top performing videos for the authenticated user"}, "response": []}, {"name": "Get Recent Videos", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/analytics/videos/recent?limit=5", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "videos", "recent"], "query": [{"key": "limit", "value": "5"}]}, "description": "Get recent videos for the authenticated user"}, "response": []}], "description": "Analytics API endpoints"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}]}