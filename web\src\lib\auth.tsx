import { createContext, useContext, useState, useEffect } from "react";
import { authApi } from "./api";
export interface User {
  id: string;
  email: string;
  name: string;
}
interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}
const AuthContext = createContext<AuthContextType | null>(null);
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const validateSession = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setUser(null);
        setIsLoading(false);
        return;
      }
      const userData = await authApi.getCurrentUser();
      if (userData) {
        setUser(userData);
      }
    } catch (err) {
      console.error("Error validating session:", err);
      setUser(null);
      localStorage.removeItem('token');
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    validateSession();
  }, []);
  const login = async (email: string, password: string) => {
    try {
      setError(null);
      setIsLoading(true);
      const { user: userData, token } = await authApi.login(email, password);
      if (!userData || !token) {
        throw new Error("Invalid response from server");
      }
      localStorage.setItem('token', token);
      setUser(userData);
      return userData;
    } catch (err) {
      console.error("Login error:", err);
      setError(err instanceof Error ? err.message : "Login failed");
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  const logout = async () => {
    setIsLoading(true);
    try {
      await authApi.logout();
      localStorage.removeItem('token');
      setUser(null);
    } catch (err) {
      console.error("Logout error:", err);
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading, error }}>
      {children}
    </AuthContext.Provider>
  );
}
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
