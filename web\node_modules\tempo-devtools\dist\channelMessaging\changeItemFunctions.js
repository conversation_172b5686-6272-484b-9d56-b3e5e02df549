"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCodebaseIds = exports.applyChangeItemToDocument = exports.resetIntermediateClassesForSliderInstantUpdate = exports.TEMPORARY_STYLING_CLASS_NAME = exports.ADD_CLASS_INSTANT_UPDATE_QUEUE = exports.ADD_JSX_PREFIX = exports.DUPLICATE_PLACEHOLDER_PREFIX = exports.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID = void 0;
const jquery_1 = __importDefault(require("jquery"));
const identifierUtils_1 = require("./identifierUtils");
const changeLedgerTypes_1 = require("./changeLedgerTypes");
const constantsAndTypes_1 = require("./constantsAndTypes");
const cssRuleUtils_1 = require("./cssRuleUtils");
const sessionStorageUtils_1 = require("./sessionStorageUtils");
const tempoElement_1 = require("./tempoElement");
const uuid_1 = require("uuid");
// These constants match what tempo-api has
exports.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID = 'tempo-wrap-in-div-placeholder';
exports.DUPLICATE_PLACEHOLDER_PREFIX = 'tempo-duplicate-placeholder-';
exports.ADD_JSX_PREFIX = 'tempo-add-jsx-placeholder-';
// Stored in memory storage, used to keep track of some possible add class instant
// updates that need to be re-applied after a hot reload
// (e.g. when the additional) instant updates happened during flushing
exports.ADD_CLASS_INSTANT_UPDATE_QUEUE = 'ADD_CLASS_INSTANT_UPDATE_QUEUE';
exports.TEMPORARY_STYLING_CLASS_NAME = 'arb89-temp-styling';
const getTopLevelCodebaseIdForComponent = (componentId) => {
    let topLevelCodebaseId = null;
    let minNumberParents = Infinity;
    (0, jquery_1.default)(`.component-${componentId}`).each((index, element) => {
        if ((0, jquery_1.default)(element).parents().length < minNumberParents) {
            minNumberParents = (0, jquery_1.default)(element).parents().length;
            topLevelCodebaseId = (0, identifierUtils_1.getCodebaseIdFromNode)(element);
        }
    });
    return topLevelCodebaseId;
};
const makeid = (length) => {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
        counter += 1;
    }
    return result;
};
let intermediateClassesForSliderInstantUpdate = {};
const resetIntermediateClassesForSliderInstantUpdate = () => {
    intermediateClassesForSliderInstantUpdate = {};
};
exports.resetIntermediateClassesForSliderInstantUpdate = resetIntermediateClassesForSliderInstantUpdate;
const sizeVariantToWrap = {
    sm: '@media (width >= 40rem)',
    md: '@media (width >= 48rem)',
    lg: '@media (width >= 64rem)',
    xl: '@media (width >= 80rem)',
    '2xl': '@media (width >= 96rem)',
};
const addClassCssRule = (className, rules, id, variant) => {
    let selector = className
        .replace(/\[/g, '\\[')
        .replace(/\]/g, '\\]')
        .replace(/\(/g, '\\(')
        .replace(/\)/g, '\\)')
        .replace(/\./g, '\\.')
        .replace(/\%/g, '\\%')
        .replace(/\!/g, '\\!')
        .replace(/\//g, '\\/')
        .replace(/#/g, '\\#');
    let ruleToSet = rules;
    if (variant && variant !== 'default') {
        selector = `${variant}\\:${selector}`;
        if (variant === 'hover') {
            selector = `${selector}:hover`;
        }
        else if (sizeVariantToWrap[variant]) {
            ruleToSet = `${sizeVariantToWrap[variant]} { ${rules} }`;
        }
    }
    selector = `.${selector}`;
    try {
        addOrEditCSSRule(selector, ruleToSet, id);
    }
    catch (e) {
        console.log('Error adding class CSS rule', e);
    }
};
const addOrEditCSSRule = (selector, rules, id) => {
    var styleEl = document.createElement('style');
    if (id) {
        const existingElement = document.getElementById(id);
        if (existingElement) {
            existingElement.remove();
        }
        styleEl.id = id;
    }
    // Append <style> element to <head>
    document.head.appendChild(styleEl);
    var styleSheet = styleEl.sheet;
    if (styleSheet.insertRule) {
        // All browsers, except IE before version 9
        styleSheet.insertRule(selector + '{' + rules + '}', styleSheet.cssRules.length);
    }
    else if (styleSheet.addRule) {
        // IE before version 9
        styleSheet.addRule(selector, rules, styleSheet.rules.length);
    }
};
const applyChangeItemToDocument = (parentPort, storyboardId, plainChangeItem) => {
    var _a;
    if (!plainChangeItem || !plainChangeItem.type) {
        return { sendNewNavTree: false, instantUpdateSuccessful: false };
    }
    const changeItem = (0, changeLedgerTypes_1.reconstructChangeLedgerClass)(plainChangeItem);
    let extraInstantUpdateData = {};
    let instantUpdateSuccessful = false;
    // The display: none rule is needed for a lot of instant updates, so create it if it doesn't exist
    if (!document.getElementById(identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS)) {
        addClassCssRule(identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS, 'display: none !important', identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS);
    }
    let sendNewNavTree = false;
    if (changeItem.type === changeLedgerTypes_1.ChangeType.ADD_JSX) {
        const castChangeItem = changeItem;
        const changeFields = castChangeItem.changeFields;
        const newAddedIds = [];
        if (changeFields.htmlForInstantUpdate) {
            const elementToAdd = (0, jquery_1.default)(changeFields.htmlForInstantUpdate);
            elementToAdd.attr(identifierUtils_1.TEMPO_DELETE_AFTER_REFRESH, 'true');
            elementToAdd.attr(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true'); // So that the DOM tree refresh doesn't get triggered
            elementToAdd.attr(identifierUtils_1.TEMPO_OUTLINE_UNTIL_REFESH, 'true');
            const ID_FOR_ELEMENT = `${exports.ADD_JSX_PREFIX}${(0, uuid_1.v4)()}`;
            elementToAdd.attr(identifierUtils_1.TEMPO_ELEMENT_ID, ID_FOR_ELEMENT);
            elementToAdd.addClass(ID_FOR_ELEMENT);
            newAddedIds.push(ID_FOR_ELEMENT);
            (0, jquery_1.default)(`.${changeFields.codebaseIdToAddTo}`).each((index, item) => {
                if (changeFields.afterCodebaseId) {
                    const afterElement = (0, jquery_1.default)(`.${changeFields.afterCodebaseId}`);
                    if (!(afterElement === null || afterElement === void 0 ? void 0 : afterElement.length)) {
                        return;
                    }
                    elementToAdd.insertAfter(afterElement.first());
                }
                else if (changeFields.beforeCodebaseId) {
                    const beforeElement = (0, jquery_1.default)(`.${changeFields.beforeCodebaseId}`);
                    if (!(beforeElement === null || beforeElement === void 0 ? void 0 : beforeElement.length)) {
                        return;
                    }
                    elementToAdd.insertBefore(beforeElement.first());
                }
                else {
                    (0, jquery_1.default)(item).append(elementToAdd);
                }
                sendNewNavTree = true;
                instantUpdateSuccessful = true;
            });
        }
        extraInstantUpdateData['newAddedIds'] = newAddedIds;
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.MOVE_JSX) {
        const castChangeItem = changeItem;
        // Find each element that matches the jsxCodebaseId
        const sourceElements = [];
        // See if direct matches work first
        if ((0, jquery_1.default)(`.${castChangeItem.changeFields.codebaseIdToMove}`).length > 0) {
            (0, jquery_1.default)(`.${castChangeItem.changeFields.codebaseIdToMove}`).each((index, element) => {
                sourceElements.push((0, jquery_1.default)(element));
            });
        }
        else {
            // Try to find it by the component ID
            let topLevelCodebaseId = getTopLevelCodebaseIdForComponent(castChangeItem.changeFields.codebaseIdToMove || '');
            if (topLevelCodebaseId) {
                (0, jquery_1.default)(`.${topLevelCodebaseId}`).each((index, element) => {
                    sourceElements.push((0, jquery_1.default)(element));
                });
            }
        }
        // If the container is a component, drop into the codebaseId of the top-most child div
        let containerCodebaseId = getTopLevelCodebaseIdForComponent(castChangeItem.changeFields.codebaseIdToMoveTo || '') || castChangeItem.changeFields.codebaseIdToMoveTo;
        // For each source element, find the new matching parent element
        const newParentElements = [];
        sourceElements.forEach((element) => {
            let newParentElement = null;
            // For each parent, try to see if it either matches or contains the new parent
            let parentElement = element.parent();
            while (parentElement.length) {
                // If the parent directly matches, this is it
                if (parentElement.hasClass(containerCodebaseId)) {
                    newParentElement = parentElement;
                    break;
                }
                // Check children that match the codebase ID to drop into
                const matchingChildren = parentElement.find(`.${containerCodebaseId}`);
                if (matchingChildren.length) {
                    // TODO: What if this matches more than one?
                    newParentElement = matchingChildren.first();
                    break;
                }
                parentElement = parentElement.parent();
            }
            if (!newParentElement) {
                newParentElements.push(null);
                return;
            }
            newParentElements.push(newParentElement);
        });
        // For each child/parentElement pair, move the child to the new parent
        sourceElements.forEach((element, index) => {
            const newParentElement = newParentElements[index];
            if (!newParentElement.length) {
                console.log('Could not find new parent element for instant update');
                return;
            }
            sendNewNavTree = true;
            instantUpdateSuccessful = true;
            element.attr(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true');
            // If the parent hasn't changed, just move it, otherwise clone it and create a new one in the new spot
            let useClone = !newParentElement.is(element.parent());
            // So that nextjs hot reloading works, simply hide the element and clone it into the new spot
            let cloneElement;
            if (useClone) {
                cloneElement = element.clone();
                cloneElement.attr(identifierUtils_1.TEMPO_DELETE_AFTER_REFRESH, 'true');
                element.addClass(identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS);
                element.attr(identifierUtils_1.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH, 'true');
            }
            if (castChangeItem.changeFields.afterCodebaseId) {
                const afterIdToUse = getTopLevelCodebaseIdForComponent(castChangeItem.changeFields.afterCodebaseId) || castChangeItem.changeFields.afterCodebaseId;
                const afterElement = newParentElement.children(`.${afterIdToUse}`);
                if (afterElement.length) {
                    if (useClone && cloneElement) {
                        cloneElement.insertAfter(afterElement.first());
                    }
                    else {
                        element.insertAfter(afterElement.first());
                    }
                    return;
                }
            }
            if (castChangeItem.changeFields.beforeCodebaseId) {
                const beforeIdToUse = getTopLevelCodebaseIdForComponent(castChangeItem.changeFields.beforeCodebaseId) || castChangeItem.changeFields.beforeCodebaseId;
                const beforeElement = newParentElement.children(`.${beforeIdToUse}`);
                if (beforeElement.length) {
                    if (useClone && cloneElement) {
                        cloneElement.insertBefore(beforeElement.first());
                    }
                    else {
                        element.insertBefore(beforeElement.first());
                    }
                    return;
                }
            }
            if (useClone && cloneElement) {
                cloneElement.appendTo(newParentElement);
            }
            else {
                element.appendTo(newParentElement);
            }
        });
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.REMOVE_JSX) {
        const castChangeItem = changeItem;
        const parentToElementKeysRemoved = {};
        castChangeItem.changeFields.codebaseIdsToRemove.forEach((codebaseId) => {
            // See if direct matches work first
            let codebaseIdToRemove;
            if ((0, jquery_1.default)(`.${codebaseId}`).length > 0) {
                codebaseIdToRemove = codebaseId;
            }
            else {
                // Try to find it by the component ID
                let topLevelCodebaseId = getTopLevelCodebaseIdForComponent(codebaseId || '');
                if (!topLevelCodebaseId) {
                    console.log('Could not find component element for instant update');
                    return false;
                }
                codebaseIdToRemove = topLevelCodebaseId;
            }
            // For each item that is removed, save the inner HTML in case it gets deleted and we want to undo
            (0, jquery_1.default)(`.${codebaseIdToRemove}`).each((index, item) => {
                const elementKeyRemoved = (0, identifierUtils_1.getElementKeyFromNode)(item);
                const parentElementKey = (0, identifierUtils_1.getElementKeyFromNode)(item.parentElement);
                if (elementKeyRemoved && parentElementKey) {
                    if (!parentToElementKeysRemoved[parentElementKey]) {
                        parentToElementKeysRemoved[parentElementKey] = [];
                    }
                    parentToElementKeysRemoved[parentElementKey].push({
                        outerHTML: item.outerHTML,
                        elementKeyRemoved,
                    });
                }
                item.classList.add(identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS);
                item.setAttribute(identifierUtils_1.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH, 'true');
                sendNewNavTree = true;
                instantUpdateSuccessful = true;
            });
        });
        extraInstantUpdateData.parentToElementKeysRemoved =
            parentToElementKeysRemoved;
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.ADD_CLASS ||
        changeItem.type === changeLedgerTypes_1.ChangeType.STYLING) {
        let className, cssEquivalent, codebaseIdToAddClass, temporaryClass, codebaseClassName, modifiers;
        if (changeItem.type === changeLedgerTypes_1.ChangeType.ADD_CLASS) {
            const castChangeItem = changeItem;
            codebaseClassName = castChangeItem.changeFields.className;
            className = castChangeItem.changeFields.className;
            cssEquivalent = castChangeItem.changeFields.cssEquivalent;
            codebaseIdToAddClass = castChangeItem.changeFields.codebaseIdToAddClass;
            temporaryClass = castChangeItem.changeFields.temporaryOnly;
            modifiers = castChangeItem.changeFields.modifiers;
            if (temporaryClass) {
                className = exports.TEMPORARY_STYLING_CLASS_NAME;
            }
        }
        else {
            // As of March 6, 2024 we only support tailwind STYLING changes, so treat them as adding a class
            const castChangeItem = changeItem.changeFields;
            className = '';
            cssEquivalent = Object.keys(castChangeItem.stylingChanges)
                .map((key) => {
                if (castChangeItem.stylingChanges[key] === constantsAndTypes_1.DELETE_STYLE_CONSTANT) {
                    return `${(0, cssRuleUtils_1.camelToSnakeCase)(key)}: unset !important;`;
                }
                return `${(0, cssRuleUtils_1.camelToSnakeCase)(key)}: ${castChangeItem.stylingChanges[key]};`;
            })
                .join('');
            codebaseIdToAddClass = castChangeItem.codebaseId;
            modifiers = castChangeItem.modifiers;
        }
        const SAFE_CLASSNAME_REGEX = /[^A-Za-z0-9_-]/g;
        // Escape any custom classes
        let classToAdd = (className || '')
            .replace(SAFE_CLASSNAME_REGEX, '-') // Replace any non-alphanumeric characters with '-'
            .replace(/^\d/, '-$&'); // If the class starts with a digit, prepend with '-'
        // Instead of adding the class name, generate a new class and set the
        // css equivalent values inside it
        // This class will be deleted after a hot reload
        // Note - for temporary classes we want to explicitly use the same class
        if (cssEquivalent && !temporaryClass) {
            const msSinceJan1 = Date.now() - 1704067200000;
            classToAdd = `${identifierUtils_1.TEMPO_INSTANT_UPDATE_STYLING_PREFIX}${msSinceJan1}-${classToAdd}`;
        }
        if (classToAdd) {
            if (!temporaryClass) {
                // Clear the temporary class on this element if it has it
                (0, jquery_1.default)(`.${codebaseIdToAddClass}`).removeClass(exports.TEMPORARY_STYLING_CLASS_NAME);
            }
            if (cssEquivalent) {
                if (modifiers && modifiers.length > 0) {
                    const CSS_PSEUDO_MODIFIERS = [
                        'hover',
                        'required',
                        'focus',
                        'active',
                        'invalid',
                        'disabled',
                    ];
                    const pseudoModifiers = modifiers.filter((modifier) => CSS_PSEUDO_MODIFIERS.includes(modifier));
                    const pseudoModifiersSuffix = pseudoModifiers.join(':');
                    if (pseudoModifiers.length > 0) {
                        const modifierClass = `${classToAdd}:${pseudoModifiersSuffix}`;
                        addClassCssRule(modifierClass, cssEquivalent, modifierClass);
                    }
                    else {
                        addClassCssRule(classToAdd, cssEquivalent, classToAdd);
                    }
                    const forceClasses = modifiers
                        .map((modifier) => `.tempo-force-${modifier}`)
                        .join('');
                    const instantUpdateForForceClass = `${classToAdd}${forceClasses}`;
                    addClassCssRule(instantUpdateForForceClass, cssEquivalent, instantUpdateForForceClass);
                }
                else {
                    addClassCssRule(classToAdd, cssEquivalent, classToAdd);
                }
            }
            const currentAddClassValues = (0, sessionStorageUtils_1.getMemoryStorageItem)(exports.ADD_CLASS_INSTANT_UPDATE_QUEUE) || [];
            // See if direct matches work first
            if ((0, jquery_1.default)(`.${codebaseIdToAddClass}`).length > 0) {
                (0, jquery_1.default)(`.${codebaseIdToAddClass}`).addClass(classToAdd);
                instantUpdateSuccessful = true;
                currentAddClassValues.push({
                    codebaseId: codebaseIdToAddClass,
                    className: classToAdd,
                });
            }
            else {
                // Try to find it by the component ID
                let topLevelCodebaseId = getTopLevelCodebaseIdForComponent(codebaseIdToAddClass || '');
                if (topLevelCodebaseId && (0, jquery_1.default)(`.${topLevelCodebaseId}`).length > 0) {
                    instantUpdateSuccessful = true;
                    (0, jquery_1.default)(`.${topLevelCodebaseId}`).addClass(classToAdd);
                    currentAddClassValues.push({
                        codebaseId: topLevelCodebaseId,
                        className: classToAdd,
                    });
                }
            }
            (0, sessionStorageUtils_1.setMemoryStorageItem)(exports.ADD_CLASS_INSTANT_UPDATE_QUEUE, currentAddClassValues);
            extraInstantUpdateData.addedClass = classToAdd;
            extraInstantUpdateData.codebaseAddedClass = codebaseClassName;
        }
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.REMOVE_CLASS) {
        const removeClassChangeFields = changeItem.changeFields;
        // See if direct matches work first
        if ((0, jquery_1.default)(`.${removeClassChangeFields.codebaseIdToRemoveClass}`).length > 0) {
            (0, jquery_1.default)(`.${removeClassChangeFields.codebaseIdToRemoveClass}`).removeClass(removeClassChangeFields.className);
            instantUpdateSuccessful = true;
        }
        else {
            // Try to find it by the component ID
            let topLevelCodebaseId = getTopLevelCodebaseIdForComponent(removeClassChangeFields.codebaseIdToRemoveClass || '');
            if (topLevelCodebaseId && (0, jquery_1.default)(`.${topLevelCodebaseId}`).length > 0) {
                instantUpdateSuccessful = true;
                (0, jquery_1.default)(`.${topLevelCodebaseId}`).removeClass(removeClassChangeFields.className);
            }
        }
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.WRAP_DIV) {
        const changeFields = changeItem.changeFields;
        const codebaseIdsToWrap = changeFields.codebaseIdsToWrap;
        const firstCodebaseId = codebaseIdsToWrap[0];
        // We assume the other codebase IDs are siblings of this codebase ID, so we
        // find each instance of the first one and include the other items that match in it
        // If the other items aren't all found, we do not wrap and let hot reload handle it
        (0, jquery_1.default)(`.${firstCodebaseId}`).each((index, item) => {
            const otherCodebaseIds = codebaseIdsToWrap.slice(1);
            // For each codebase ID in otherCodebaseIds, retrieve the element that is a sibling of item
            const siblings = (0, jquery_1.default)(item).siblings();
            const allItemsToAddToNewDiv = [item];
            let earliestItem = item;
            let earliestIndex = (0, jquery_1.default)(item).index();
            otherCodebaseIds.forEach((codebaseId) => {
                const foundSibling = siblings.filter(`.${codebaseId}`).get(0);
                if (foundSibling) {
                    allItemsToAddToNewDiv.push(foundSibling);
                    const index = (0, jquery_1.default)(foundSibling).index();
                    if (index < earliestIndex) {
                        earliestItem = foundSibling;
                        earliestIndex = index;
                    }
                }
            });
            // TODO: What to do if they all can't be found?
            if (allItemsToAddToNewDiv.length !== codebaseIdsToWrap.length) {
                // For now, just add the ones that were found
            }
            // Create a div with a clone of the item, while hiding the item
            // When the hot reload happens the clone gets deleted and the item is shown again
            const newDiv = document.createElement('div');
            newDiv.className = exports.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID;
            newDiv.setAttribute(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true'); // So that the DOM tree refresh doesn't get triggered
            newDiv.setAttribute('tempoelementid', exports.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID);
            newDiv.setAttribute('data-testid', exports.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID);
            newDiv.setAttribute(identifierUtils_1.TEMPO_DELETE_AFTER_REFRESH, 'true');
            allItemsToAddToNewDiv.forEach((elem) => {
                newDiv.appendChild(elem.cloneNode(true));
                elem.classList.add(identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS);
                elem.setAttribute(identifierUtils_1.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH, 'true');
            });
            // Insert the new div right before the first item
            earliestItem.insertAdjacentElement('beforebegin', newDiv);
            sendNewNavTree = true;
            instantUpdateSuccessful = true;
        });
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.DUPLICATE) {
        const changeFileds = changeItem.changeFields;
        const codebaseIdsToDuplicate = changeFileds.codebaseIdsToDuplicate;
        codebaseIdsToDuplicate.forEach((codebaseIdToDuplicate) => {
            (0, jquery_1.default)(`.${codebaseIdToDuplicate}`).each((index, item) => {
                const clonedNode = item.cloneNode(true);
                clonedNode.setAttribute(identifierUtils_1.TEMPO_DELETE_AFTER_REFRESH, 'true');
                clonedNode.setAttribute(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true'); // So that the DOM tree refresh doesn't get triggered
                // Set up all the correct duplicated codebase IDs
                clonedNode.setAttribute('tempoelementid', `${exports.DUPLICATE_PLACEHOLDER_PREFIX}${codebaseIdToDuplicate}`);
                clonedNode.setAttribute('data-testid', `${exports.DUPLICATE_PLACEHOLDER_PREFIX}${codebaseIdToDuplicate}`);
                clonedNode.classList.add(exports.DUPLICATE_PLACEHOLDER_PREFIX + codebaseIdToDuplicate);
                clonedNode.classList.remove(codebaseIdToDuplicate);
                let children = Array.from(clonedNode.children);
                while (children.length) {
                    const child = children.pop();
                    if (!child) {
                        continue;
                    }
                    const codebaseId = child.getAttribute('tempoelementid') ||
                        child.getAttribute('data-testid');
                    if (!codebaseId) {
                        continue;
                    }
                    child.setAttribute('tempoelementid', `${exports.DUPLICATE_PLACEHOLDER_PREFIX}${codebaseId}`);
                    child.setAttribute('data-testid', `${exports.DUPLICATE_PLACEHOLDER_PREFIX}${codebaseId}`);
                    child.classList.remove(codebaseId);
                    child.classList.add(exports.DUPLICATE_PLACEHOLDER_PREFIX + codebaseId);
                    child.setAttribute(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true');
                    children.push(...Array.from(child.children));
                }
                // Add the clones node right after the found node
                item.insertAdjacentElement('afterend', clonedNode);
                sendNewNavTree = true;
                instantUpdateSuccessful = true;
            });
        });
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.CHANGE_TAG) {
        const changeFields = changeItem.changeFields;
        (0, jquery_1.default)(`.${changeFields.codebaseIdToChange}`).each((index, item) => {
            const $newElement = (0, jquery_1.default)('<' + changeFields.newTagName + '></' + changeFields.newTagName + '>');
            $newElement.attr(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true'); // So that the DOM tree refresh doesn't get triggered
            $newElement.attr(identifierUtils_1.TEMPO_DELETE_AFTER_REFRESH, 'true');
            const $item = (0, jquery_1.default)(item);
            // Copy all attributes from the original element to the new element
            jquery_1.default.each($item[0].attributes, function () {
                $newElement.attr(this.name, this.value);
            });
            $item.contents().clone(true, true).appendTo($newElement);
            // Add right before the cloned item so the unique path stays the same
            $item.before($newElement);
            // Hide the original item
            $item.addClass(identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS);
            $item.attr(identifierUtils_1.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH, 'true');
            sendNewNavTree = true;
            instantUpdateSuccessful = true;
        });
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.UNDO) {
        const { sendNewNavTree: _sendNewNavTree, instantUpdateSuccessful: _instantUpdateSuccessful, } = applyUndoChangeItemToDocument(parentPort, changeItem);
        sendNewNavTree = _sendNewNavTree;
        instantUpdateSuccessful = _instantUpdateSuccessful;
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.REDO) {
        const changeFields = changeItem.changeFields;
        const changeToRedo = changeFields.changeToRedo;
        if (changeLedgerTypes_1.CHANGE_TYPES_WITH_INSTANT_UNDO.includes(changeToRedo.type)) {
            const { sendNewNavTree: _sendNewNavTree, instantUpdateSuccessful: _instantUpdateSuccessful, } = (0, exports.applyChangeItemToDocument)(parentPort, storyboardId, changeToRedo);
            sendNewNavTree = _sendNewNavTree;
            instantUpdateSuccessful = _instantUpdateSuccessful;
            if (changeToRedo.prevIdToNewIdMap) {
                const sendNewFromUpdate = (0, exports.updateCodebaseIds)(parentPort, changeToRedo.prevIdToNewIdMap, true);
                sendNewNavTree = sendNewFromUpdate || sendNewNavTree;
            }
        }
    }
    else if (changeItem.type === changeLedgerTypes_1.ChangeType.UPDATE_CLASSES) {
        const { codebaseIdToUpdateClass, newClasses, oldClasses, involvedClasses, isInstantChangeOnly, } = changeItem.changeFields;
        // TODO(nhanthai): Should handle this better instead of just remove and add
        for (const oldClass of oldClasses) {
            (0, jquery_1.default)(`.${codebaseIdToUpdateClass}`).removeClass(oldClass);
        }
        (intermediateClassesForSliderInstantUpdate[codebaseIdToUpdateClass] || []).forEach((className) => {
            (0, jquery_1.default)(`.${codebaseIdToUpdateClass}`).removeClass(className);
        });
        for (const newClass of newClasses) {
            (0, jquery_1.default)(`.${codebaseIdToUpdateClass}`).addClass(newClass);
        }
        for (const involvedClass of involvedClasses) {
            addClassCssRule(involvedClass.tailwind, involvedClass.css, undefined, involvedClass.variant);
        }
        if (isInstantChangeOnly) {
            intermediateClassesForSliderInstantUpdate[codebaseIdToUpdateClass] = [];
            for (const involvedClass of involvedClasses) {
                const classToSet = involvedClass.variant
                    ? `${involvedClass.variant}:${involvedClass.tailwind}`
                    : involvedClass.tailwind;
                intermediateClassesForSliderInstantUpdate[codebaseIdToUpdateClass].push(classToSet);
            }
        }
        else {
            intermediateClassesForSliderInstantUpdate[codebaseIdToUpdateClass] = [];
        }
    }
    // Immediately set the new selected element keys to prevent any delay in the outlines updating
    let elementKeyToSelectAfterInstantUpdate = changeItem.getElementKeyToSelectAfterInstantUpdate();
    let elementKeysToMultiselectAfterInstantUpdate = changeItem.getElementKeysToMultiselectAfterInstantUpdate();
    if (changeItem.type === changeLedgerTypes_1.ChangeType.UNDO) {
        elementKeyToSelectAfterInstantUpdate = changeItem.changeFields.changeToUndo.getElementKeyToSelectAfterUndoInstantUpdate();
        elementKeysToMultiselectAfterInstantUpdate = changeItem.changeFields.changeToUndo.getElementKeysToMultiselectAfterUndoInstantUpdate();
    }
    if (elementKeyToSelectAfterInstantUpdate !== undefined) {
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, elementKeyToSelectAfterInstantUpdate);
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
            elementKey: elementKeyToSelectAfterInstantUpdate,
            outerHTML: (_a = (0, identifierUtils_1.getNodeForElementKey)(elementKeyToSelectAfterInstantUpdate)) === null || _a === void 0 ? void 0 : _a.outerHTML,
        });
    }
    if (elementKeysToMultiselectAfterInstantUpdate !== undefined) {
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS, elementKeysToMultiselectAfterInstantUpdate);
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,
            elementKeys: elementKeysToMultiselectAfterInstantUpdate,
            outerHTMLs: elementKeysToMultiselectAfterInstantUpdate === null || elementKeysToMultiselectAfterInstantUpdate === void 0 ? void 0 : elementKeysToMultiselectAfterInstantUpdate.map((elementKey) => { var _a; return (_a = (0, identifierUtils_1.getNodeForElementKey)(elementKey)) === null || _a === void 0 ? void 0 : _a.outerHTML; }),
        });
    }
    if (instantUpdateSuccessful) {
        // Delete any elements that need to be deleted after instant updates
        (0, jquery_1.default)(`*[${identifierUtils_1.TEMPO_DELETE_AFTER_INSTANT_UPDATE}=true]`).remove();
    }
    parentPort.postMessage({
        id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.INSTANT_UPDATE_DONE,
        changeItem: plainChangeItem,
        instantUpdateData: extraInstantUpdateData,
        instantUpdateSuccessful,
    });
    return { sendNewNavTree, instantUpdateSuccessful };
};
exports.applyChangeItemToDocument = applyChangeItemToDocument;
const applyUndoChangeItemToDocument = (parentPort, changeItem) => {
    const changeFields = changeItem.changeFields;
    const changeToUndo = changeFields.changeToUndo;
    if (!changeLedgerTypes_1.CHANGE_TYPES_WITH_INSTANT_UNDO.includes(changeToUndo.type)) {
        return { sendNewNavTree: false, instantUpdateSuccessful: false };
    }
    let sendNewNavTree = false;
    let instantUpdateSuccessful = false;
    // API has completed and the IDs have been updated, reverse this change
    if (changeToUndo.prevIdToNewIdMap) {
        const undoCodebaseIdChanges = {};
        Object.keys(changeToUndo.prevIdToNewIdMap).forEach((prevId) => {
            const newId = changeToUndo.prevIdToNewIdMap[prevId];
            undoCodebaseIdChanges[newId] = prevId;
        });
        // If undoing do not update the codebase IDs backwards if there are codebase IDs to set after
        // the undo instant update is done
        const selectedElementSpecifiedAfterUndo = changeToUndo.getElementKeyToSelectAfterUndoInstantUpdate() !== undefined;
        sendNewNavTree = (0, exports.updateCodebaseIds)(parentPort, undoCodebaseIdChanges, !selectedElementSpecifiedAfterUndo);
    }
    // Then undo the actual change
    if (changeToUndo.type === changeLedgerTypes_1.ChangeType.REMOVE_JSX) {
        // Re-add the removed JSX
        const innerChangeFields = changeToUndo.changeFields;
        const codebaseIdsToReadd = innerChangeFields.codebaseIdsToRemove;
        // If it has been flushed, re-create the html elements from the saved inner HTML
        if (changeFields.matchingActivityFlushed) {
            const instantUpdateData = changeToUndo.getInstantUpdateData();
            const parentToElementKeysRemoved = instantUpdateData.parentToElementKeysRemoved || {};
            Object.entries(parentToElementKeysRemoved).forEach(([parentElementKey, itemsRemoved]) => {
                // Sort the removed entries in order of unique path
                const sortedItemsRemoved = Object.values(itemsRemoved).sort((a, b) => {
                    const aElementKey = tempoElement_1.TempoElement.fromKey(a.elementKeyRemoved);
                    const bElementKey = tempoElement_1.TempoElement.fromKey(b.elementKeyRemoved);
                    return aElementKey.uniquePath.localeCompare(bElementKey.uniquePath);
                });
                // Find the parent element
                const parentElement = (0, identifierUtils_1.getNodeForElementKey)(parentElementKey);
                if (parentElement) {
                    // Add the removed elements back in order
                    sortedItemsRemoved.forEach((item) => {
                        const { elementKeyRemoved, outerHTML } = item;
                        const element = tempoElement_1.TempoElement.fromKey(elementKeyRemoved);
                        const indexInParent = Number(element.uniquePath.split('-').pop());
                        const newElementFromHtml = (0, jquery_1.default)(outerHTML).get(0);
                        // Add to the parent in the index
                        if (newElementFromHtml) {
                            newElementFromHtml.setAttribute(identifierUtils_1.TEMPO_DELETE_AFTER_REFRESH, 'true');
                            newElementFromHtml.setAttribute(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true');
                            parentElement.insertBefore(newElementFromHtml, parentElement.children[indexInParent] || null);
                            instantUpdateSuccessful = true;
                            sendNewNavTree = true;
                        }
                    });
                }
            });
        }
        else {
            // Not flushed yet so can just re-add
            codebaseIdsToReadd.forEach((codebaseIdToReadd) => {
                (0, jquery_1.default)(`.${codebaseIdToReadd}`).each((index, item) => {
                    item.classList.remove(identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS);
                    item.removeAttribute(identifierUtils_1.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH);
                    sendNewNavTree = true;
                    instantUpdateSuccessful = true;
                });
            });
        }
    }
    else if (changeToUndo.type === changeLedgerTypes_1.ChangeType.ADD_CLASS ||
        changeToUndo.type === changeLedgerTypes_1.ChangeType.STYLING) {
        const instantUpdateData = changeToUndo.getInstantUpdateData();
        const innerChangeFields = changeToUndo.changeFields;
        const addedClass = instantUpdateData === null || instantUpdateData === void 0 ? void 0 : instantUpdateData.addedClass;
        if (addedClass) {
            (0, jquery_1.default)(`.${innerChangeFields.codebaseIdToAddClass}`).each((index, item) => {
                if ((0, jquery_1.default)(item).hasClass(addedClass)) {
                    (0, jquery_1.default)(item).removeClass(addedClass);
                    instantUpdateSuccessful = true;
                }
            });
        }
        const codebaseAddedClass = instantUpdateData === null || instantUpdateData === void 0 ? void 0 : instantUpdateData.codebaseAddedClass;
        if (codebaseAddedClass) {
            (0, jquery_1.default)(`.${innerChangeFields.codebaseIdToAddClass}`).each((index, item) => {
                if ((0, jquery_1.default)(item).hasClass(codebaseAddedClass)) {
                    (0, jquery_1.default)(item).removeClass(codebaseAddedClass);
                    instantUpdateSuccessful = true;
                }
            });
        }
    }
    else if (changeToUndo.type === changeLedgerTypes_1.ChangeType.UPDATE_CLASSES) {
        const { codebaseIdToUpdateClass, newClasses, oldClasses } = changeToUndo.changeFields;
        // TODO(nhanthai): Should handle this better instead of just remove and add
        for (const newClass of newClasses) {
            (0, jquery_1.default)(`.${codebaseIdToUpdateClass}`).removeClass(newClass);
        }
        for (const oldClass of oldClasses) {
            (0, jquery_1.default)(`.${codebaseIdToUpdateClass}`).addClass(oldClass);
        }
        intermediateClassesForSliderInstantUpdate[codebaseIdToUpdateClass] = [];
    }
    else if (changeToUndo.type === changeLedgerTypes_1.ChangeType.ADD_JSX) {
        const instantUpdateData = changeToUndo.getInstantUpdateData();
        const addedIds = instantUpdateData === null || instantUpdateData === void 0 ? void 0 : instantUpdateData.addedIds;
        addedIds === null || addedIds === void 0 ? void 0 : addedIds.forEach((addedId) => {
            (0, jquery_1.default)(`.${addedId}`).remove();
            instantUpdateSuccessful = true;
        });
        sendNewNavTree = true;
    }
    return { sendNewNavTree, instantUpdateSuccessful };
};
/**
 * After a change is processed on the backend, we need to update the codebase ids in the document.
 */
const updateCodebaseIds = (parentPort, prevIdToNewIdMap, updateElementKeys) => {
    // Update codebase ids in the document
    const changes = [];
    Object.entries(prevIdToNewIdMap).forEach(([prevCodebaseId, newCodebaseId]) => {
        (0, jquery_1.default)(`.${prevCodebaseId}`).each((index, item) => {
            changes.push({
                item,
                prevCodebaseId,
                newCodebaseId,
            });
        });
    });
    // Codebase Ids can swap, so we have to apply the changes after looking all elements up
    changes.forEach((change) => {
        const $item = (0, jquery_1.default)(change.item);
        const newClass = ($item.attr('class') || '').replace(new RegExp(`${change.prevCodebaseId}`, 'g'), change.newCodebaseId);
        $item.attr('class', newClass);
        change.item.setAttribute('tempoelementid', change.newCodebaseId);
        change.item.setAttribute('data-testid', change.newCodebaseId);
    });
    if (!updateElementKeys) {
        return Boolean(changes.length);
    }
    const keysToCheck = [
        {
            key: sessionStorageUtils_1.SELECTED_ELEMENT_KEY,
            messageId: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
        },
        {
            key: sessionStorageUtils_1.HOVERED_ELEMENT_KEY,
            messageId: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY,
        },
    ];
    keysToCheck.forEach(({ key, messageId }) => {
        var _a;
        const elementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(key);
        const tempoElement = tempoElement_1.TempoElement.fromKey(elementKey);
        if (prevIdToNewIdMap[tempoElement.codebaseId]) {
            const newElement = new tempoElement_1.TempoElement(prevIdToNewIdMap[tempoElement.codebaseId], tempoElement.storyboardId, tempoElement.uniquePath);
            (0, sessionStorageUtils_1.setMemoryStorageItem)(key, newElement.getKey());
            parentPort.postMessage({
                id: messageId,
                elementKey: newElement.getKey(),
                outerHTML: (_a = (0, identifierUtils_1.getNodeForElementKey)(newElement.getKey())) === null || _a === void 0 ? void 0 : _a.outerHTML,
            });
        }
    });
    // Also update the multiselected element keys
    const multiselectedElementKeys = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS);
    if (multiselectedElementKeys === null || multiselectedElementKeys === void 0 ? void 0 : multiselectedElementKeys.length) {
        const newMultiselectedElementKeys = [];
        multiselectedElementKeys.forEach((elementKey) => {
            const tempoElement = tempoElement_1.TempoElement.fromKey(elementKey);
            if (prevIdToNewIdMap[tempoElement.codebaseId]) {
                const newElement = new tempoElement_1.TempoElement(prevIdToNewIdMap[tempoElement.codebaseId], tempoElement.storyboardId, tempoElement.uniquePath);
                newMultiselectedElementKeys.push(newElement.getKey());
            }
            else {
                newMultiselectedElementKeys.push(elementKey);
            }
        });
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS, newMultiselectedElementKeys);
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,
            elementKeys: newMultiselectedElementKeys,
            outerHTMLs: newMultiselectedElementKeys === null || newMultiselectedElementKeys === void 0 ? void 0 : newMultiselectedElementKeys.map((elementKey) => { var _a; return (_a = (0, identifierUtils_1.getNodeForElementKey)(elementKey)) === null || _a === void 0 ? void 0 : _a.outerHTML; }),
        });
    }
    return Boolean(changes.length);
};
exports.updateCodebaseIds = updateCodebaseIds;
//# sourceMappingURL=data:application/json;base64,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