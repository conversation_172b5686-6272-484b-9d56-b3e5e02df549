{"name": "tempo-devtools", "version": "2.0.106", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint 'src/**/*.js'", "format": "prettier --write 'src/**/*.{js,ts}'", "build": "npm run build:tsc && npm run build:swc && npm run stage-wc", "build:tsc": "tsc", "build:swc": "node build-swc.js", "stage-wc": "node stage-wc.js", "deploy": "npm run build && npm publish --access public"}, "files": ["dist", "bin", "swc/target/wasm32-wasi/release/tempo_swc.wasm", "swc/package.json", "swc/*/target/wasm32-wasi/release/tempo_swc.wasm", "swc/*/package.json", "src/tempo-routes.d.ts"], "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "css-selector-parser": "^3.0.5", "express": "^4.18.2", "jquery": "^3.6.4", "lodash": "^4.17.21", "lz-string": "^1.5.0", "prompt-sync": "^4.2.0", "react": "^18.2.0", "readline": "^1.3.0", "resq": "^1.11.0", "seedrandom": "^3.0.5", "shelljs": "^0.8.5", "specificity": "^0.4.1", "uuid": "^9.0.0", "vite-plugin-pages": "^0.32.5"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/types": "^7.23.0", "@types/jquery": "^3.5.25", "@types/lodash": "^4.14.200", "@types/node": "^20.8.9", "@types/react": "^18.2.33", "@types/seedrandom": "^3.0.7", "@types/uuid": "^9.0.6", "copyfiles": "^2.4.1", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.2", "typescript": "^5.2.2"}, "peerDependencies": {"react-router-dom": "^6.2.1"}, "bin": {"tempo": "./bin/tempo-devtools-cli-fallback.js"}}