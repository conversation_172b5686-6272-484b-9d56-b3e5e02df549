{"version": 3, "sources": ["../../@vidstack/react/node_modules/media-captions/dist/dev/ssa-parser.js"], "sourcesContent": ["import { b as VTTCue, p as parseVTTTimestamp } from './index.js';\n\nconst FORMAT_START_RE = /^Format:[\\s\\t]*/, STYLE_START_RE = /^Style:[\\s\\t]*/, DIALOGUE_START_RE = /^Dialogue:[\\s\\t]*/, FORMAT_SPLIT_RE = /[\\s\\t]*,[\\s\\t]*/, STYLE_FUNCTION_RE = /\\{[^}]+\\}/g, NEW_LINE_RE = /\\\\N/g, STYLES_SECTION_START_RE = /^\\[(.*)[\\s\\t]?Styles\\]$/, EVENTS_SECTION_START_RE = /^\\[(.*)[\\s\\t]?Events\\]$/;\nclass SSAParser {\n  _init;\n  _section = 0 /* None */;\n  _cue = null;\n  _cues = [];\n  _errors = [];\n  _format = null;\n  _errorBuilder;\n  _styles = {};\n  async init(init) {\n    this._init = init;\n    if (init.errors)\n      this._errorBuilder = (await import('./errors.js')).ParseErrorBuilder;\n  }\n  parse(line, lineCount) {\n    if (this._section) {\n      switch (this._section) {\n        case 1 /* Style */:\n          if (line === \"\") {\n            this._section = 0 /* None */;\n          } else if (STYLE_START_RE.test(line)) {\n            if (this._format) {\n              const styles = line.replace(STYLE_START_RE, \"\").split(FORMAT_SPLIT_RE);\n              this._parseStyles(styles);\n            } else {\n              this._handleError(this._errorBuilder?._missingFormat(\"Style\", lineCount));\n            }\n          } else if (FORMAT_START_RE.test(line)) {\n            this._format = line.replace(FORMAT_START_RE, \"\").split(FORMAT_SPLIT_RE);\n          } else if (EVENTS_SECTION_START_RE.test(line)) {\n            this._format = null;\n            this._section = 2 /* Event */;\n          }\n          break;\n        case 2 /* Event */:\n          if (line === \"\") {\n            this._commitCue();\n          } else if (DIALOGUE_START_RE.test(line)) {\n            this._commitCue();\n            if (this._format) {\n              const dialogue = line.replace(DIALOGUE_START_RE, \"\").split(FORMAT_SPLIT_RE), cue = this._parseDialogue(dialogue, lineCount);\n              if (cue)\n                this._cue = cue;\n            } else {\n              this._handleError(this._errorBuilder?._missingFormat(\"Dialogue\", lineCount));\n            }\n          } else if (this._cue) {\n            this._cue.text += \"\\n\" + line.replace(STYLE_FUNCTION_RE, \"\").replace(NEW_LINE_RE, \"\\n\");\n          } else if (FORMAT_START_RE.test(line)) {\n            this._format = line.replace(FORMAT_START_RE, \"\").split(FORMAT_SPLIT_RE);\n          } else if (STYLES_SECTION_START_RE.test(line)) {\n            this._format = null;\n            this._section = 1 /* Style */;\n          } else if (EVENTS_SECTION_START_RE.test(line)) {\n            this._format = null;\n          }\n      }\n    } else if (line === \"\") ; else if (STYLES_SECTION_START_RE.test(line)) {\n      this._format = null;\n      this._section = 1 /* Style */;\n    } else if (EVENTS_SECTION_START_RE.test(line)) {\n      this._format = null;\n      this._section = 2 /* Event */;\n    }\n  }\n  done() {\n    return {\n      metadata: {},\n      cues: this._cues,\n      regions: [],\n      errors: this._errors\n    };\n  }\n  _commitCue() {\n    if (!this._cue)\n      return;\n    this._cues.push(this._cue);\n    this._init.onCue?.(this._cue);\n    this._cue = null;\n  }\n  _parseStyles(values) {\n    let name = \"Default\", styles = {}, outlineX, align = \"center\", vertical = \"bottom\", marginV, outlineY = 1.2, outlineColor, bgColor, borderStyle = 3, transform = [];\n    for (let i = 0; i < this._format.length; i++) {\n      const field = this._format[i], value = values[i];\n      switch (field) {\n        case \"Name\":\n          name = value;\n          break;\n        case \"Fontname\":\n          styles[\"font-family\"] = value;\n          break;\n        case \"Fontsize\":\n          styles[\"font-size\"] = `calc(${value} / var(--overlay-height))`;\n          break;\n        case \"PrimaryColour\":\n          const color = parseColor(value);\n          if (color)\n            styles[\"--cue-color\"] = color;\n          break;\n        case \"BorderStyle\":\n          borderStyle = parseInt(value, 10);\n          break;\n        case \"BackColour\":\n          bgColor = parseColor(value);\n          break;\n        case \"OutlineColour\":\n          const _outlineColor = parseColor(value);\n          if (_outlineColor)\n            outlineColor = _outlineColor;\n          break;\n        case \"Bold\":\n          if (parseInt(value))\n            styles[\"font-weight\"] = \"bold\";\n          break;\n        case \"Italic\":\n          if (parseInt(value))\n            styles[\"font-style\"] = \"italic\";\n          break;\n        case \"Underline\":\n          if (parseInt(value))\n            styles[\"text-decoration\"] = \"underline\";\n          break;\n        case \"StrikeOut\":\n          if (parseInt(value))\n            styles[\"text-decoration\"] = \"line-through\";\n          break;\n        case \"Spacing\":\n          styles[\"letter-spacing\"] = value + \"px\";\n          break;\n        case \"AlphaLevel\":\n          styles[\"opacity\"] = parseFloat(value);\n          break;\n        case \"ScaleX\":\n          transform.push(`scaleX(${parseFloat(value) / 100})`);\n          break;\n        case \"ScaleY\":\n          transform.push(`scaleY(${parseFloat(value) / 100})`);\n          break;\n        case \"Angle\":\n          transform.push(`rotate(${value}deg)`);\n          break;\n        case \"Shadow\":\n          outlineY = parseInt(value, 10) * 1.2;\n          break;\n        case \"MarginL\":\n          styles[\"--cue-width\"] = \"auto\";\n          styles[\"--cue-left\"] = parseFloat(value) + \"px\";\n          break;\n        case \"MarginR\":\n          styles[\"--cue-width\"] = \"auto\";\n          styles[\"--cue-right\"] = parseFloat(value) + \"px\";\n          break;\n        case \"MarginV\":\n          marginV = parseFloat(value);\n          break;\n        case \"Outline\":\n          outlineX = parseInt(value, 10);\n          break;\n        case \"Alignment\":\n          const alignment = parseInt(value, 10);\n          if (alignment >= 4)\n            vertical = alignment >= 7 ? \"top\" : \"center\";\n          switch (alignment % 3) {\n            case 1:\n              align = \"start\";\n              break;\n            case 2:\n              align = \"center\";\n              break;\n            case 3:\n              align = \"end\";\n              break;\n          }\n      }\n    }\n    styles._vertical = vertical;\n    styles[\"--cue-white-space\"] = \"normal\";\n    styles[\"--cue-line-height\"] = \"normal\";\n    styles[\"--cue-text-align\"] = align;\n    if (vertical === \"center\") {\n      styles[`--cue-top`] = \"50%\";\n      transform.push(\"translateY(-50%)\");\n    } else {\n      styles[`--cue-${vertical}`] = (marginV || 0) + \"px\";\n    }\n    if (borderStyle === 1) {\n      styles[\"--cue-padding-y\"] = \"0\";\n    }\n    if (borderStyle === 1 || bgColor) {\n      styles[\"--cue-bg-color\"] = borderStyle === 1 ? \"none\" : bgColor;\n    }\n    if (borderStyle === 3 && outlineColor) {\n      styles[\"--cue-outline\"] = `${outlineX}px solid ${outlineColor}`;\n    }\n    if (borderStyle === 1 && typeof outlineX === \"number\") {\n      const color = bgColor ?? \"#000\";\n      styles[\"--cue-text-shadow\"] = [\n        outlineColor && buildTextShadow(outlineX * 1.2, outlineY * 1.2, outlineColor),\n        outlineColor ? buildTextShadow(outlineX * (outlineX / 2), outlineY * (outlineX / 2), color) : buildTextShadow(outlineX, outlineY, color)\n      ].filter(Boolean).join(\", \");\n    }\n    if (transform.length)\n      styles[\"--cue-transform\"] = transform.join(\" \");\n    this._styles[name] = styles;\n  }\n  _parseDialogue(values, lineCount) {\n    const fields = this._buildFields(values);\n    const timestamp = this._parseTimestamp(fields.Start, fields.End, lineCount);\n    if (!timestamp)\n      return;\n    const cue = new VTTCue(timestamp[0], timestamp[1], \"\"), styles = { ...this._styles[fields.Style] || {} }, voice = fields.Name ? `<v ${fields.Name}>` : \"\";\n    const vertical = styles._vertical, marginLeft = fields.MarginL && parseFloat(fields.MarginL), marginRight = fields.MarginR && parseFloat(fields.MarginR), marginV = fields.MarginV && parseFloat(fields.MarginV);\n    if (marginLeft) {\n      styles[\"--cue-width\"] = \"auto\";\n      styles[\"--cue-left\"] = marginLeft + \"px\";\n    }\n    if (marginRight) {\n      styles[\"--cue-width\"] = \"auto\";\n      styles[\"--cue-right\"] = marginRight + \"px\";\n    }\n    if (marginV && vertical !== \"center\") {\n      styles[`--cue-${vertical}`] = marginV + \"px\";\n    }\n    cue.text = voice + values.slice(this._format.length - 1).join(\", \").replace(STYLE_FUNCTION_RE, \"\").replace(NEW_LINE_RE, \"\\n\");\n    delete styles._vertical;\n    if (Object.keys(styles).length)\n      cue.style = styles;\n    return cue;\n  }\n  _buildFields(values) {\n    const fields = {};\n    for (let i = 0; i < this._format.length; i++) {\n      fields[this._format[i]] = values[i];\n    }\n    return fields;\n  }\n  _parseTimestamp(startTimeText, endTimeText, lineCount) {\n    const startTime = parseVTTTimestamp(startTimeText), endTime = parseVTTTimestamp(endTimeText);\n    if (startTime !== null && endTime !== null && endTime > startTime) {\n      return [startTime, endTime];\n    } else {\n      if (startTime === null) {\n        this._handleError(this._errorBuilder?._badStartTimestamp(startTimeText, lineCount));\n      }\n      if (endTime === null) {\n        this._handleError(this._errorBuilder?._badEndTimestamp(endTimeText, lineCount));\n      }\n      if (startTime != null && endTime !== null && endTime > startTime) {\n        this._handleError(this._errorBuilder?._badRangeTimestamp(startTime, endTime, lineCount));\n      }\n    }\n  }\n  _handleError(error) {\n    if (!error)\n      return;\n    this._errors.push(error);\n    if (this._init.strict) {\n      this._init.cancel();\n      throw error;\n    } else {\n      this._init.onError?.(error);\n    }\n  }\n}\nfunction parseColor(color) {\n  const abgr = parseInt(color.replace(\"&H\", \"\"), 16);\n  if (abgr >= 0) {\n    const a = abgr >> 24 & 255 ^ 255;\n    const alpha = a / 255;\n    const b = abgr >> 16 & 255;\n    const g = abgr >> 8 & 255;\n    const r = abgr & 255;\n    return \"rgba(\" + [r, g, b, alpha].join(\",\") + \")\";\n  }\n  return null;\n}\nfunction buildTextShadow(x, y, color) {\n  const noOfShadows = Math.ceil(2 * Math.PI * x);\n  let textShadow = \"\";\n  for (let i = 0; i < noOfShadows; i++) {\n    const theta = 2 * Math.PI * i / noOfShadows;\n    textShadow += x * Math.cos(theta) + \"px \" + y * Math.sin(theta) + \"px 0 \" + color + (i == noOfShadows - 1 ? \"\" : \",\");\n  }\n  return textShadow;\n}\nfunction createSSAParser() {\n  return new SSAParser();\n}\n\nexport { SSAParser, createSSAParser as default };\n"], "mappings": ";;;;;;;;;AAEA,IAAM,kBAAkB;AAAxB,IAA2C,iBAAiB;AAA5D,IAA8E,oBAAoB;AAAlG,IAAuH,kBAAkB;AAAzI,IAA4J,oBAAoB;AAAhL,IAA8L,cAAc;AAA5M,IAAoN,0BAA0B;AAA9O,IAAyQ,0BAA0B;AACnS,IAAM,YAAN,MAAgB;AAAA,EAAhB;AACE;AACA,oCAAW;AACX,gCAAO;AACP,iCAAQ,CAAC;AACT,mCAAU,CAAC;AACX,mCAAU;AACV;AACA,mCAAU,CAAC;AAAA;AAAA,EACX,MAAM,KAAK,MAAM;AACf,SAAK,QAAQ;AACb,QAAI,KAAK;AACP,WAAK,iBAAiB,MAAM,OAAO,sBAAa,GAAG;AAAA,EACvD;AAAA,EACA,MAAM,MAAM,WAAW;AAjBzB;AAkBI,QAAI,KAAK,UAAU;AACjB,cAAQ,KAAK,UAAU;AAAA,QACrB,KAAK;AACH,cAAI,SAAS,IAAI;AACf,iBAAK,WAAW;AAAA,UAClB,WAAW,eAAe,KAAK,IAAI,GAAG;AACpC,gBAAI,KAAK,SAAS;AAChB,oBAAM,SAAS,KAAK,QAAQ,gBAAgB,EAAE,EAAE,MAAM,eAAe;AACrE,mBAAK,aAAa,MAAM;AAAA,YAC1B,OAAO;AACL,mBAAK,cAAa,UAAK,kBAAL,mBAAoB,eAAe,SAAS,UAAU;AAAA,YAC1E;AAAA,UACF,WAAW,gBAAgB,KAAK,IAAI,GAAG;AACrC,iBAAK,UAAU,KAAK,QAAQ,iBAAiB,EAAE,EAAE,MAAM,eAAe;AAAA,UACxE,WAAW,wBAAwB,KAAK,IAAI,GAAG;AAC7C,iBAAK,UAAU;AACf,iBAAK,WAAW;AAAA,UAClB;AACA;AAAA,QACF,KAAK;AACH,cAAI,SAAS,IAAI;AACf,iBAAK,WAAW;AAAA,UAClB,WAAW,kBAAkB,KAAK,IAAI,GAAG;AACvC,iBAAK,WAAW;AAChB,gBAAI,KAAK,SAAS;AAChB,oBAAM,WAAW,KAAK,QAAQ,mBAAmB,EAAE,EAAE,MAAM,eAAe,GAAG,MAAM,KAAK,eAAe,UAAU,SAAS;AAC1H,kBAAI;AACF,qBAAK,OAAO;AAAA,YAChB,OAAO;AACL,mBAAK,cAAa,UAAK,kBAAL,mBAAoB,eAAe,YAAY,UAAU;AAAA,YAC7E;AAAA,UACF,WAAW,KAAK,MAAM;AACpB,iBAAK,KAAK,QAAQ,OAAO,KAAK,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,aAAa,IAAI;AAAA,UACxF,WAAW,gBAAgB,KAAK,IAAI,GAAG;AACrC,iBAAK,UAAU,KAAK,QAAQ,iBAAiB,EAAE,EAAE,MAAM,eAAe;AAAA,UACxE,WAAW,wBAAwB,KAAK,IAAI,GAAG;AAC7C,iBAAK,UAAU;AACf,iBAAK,WAAW;AAAA,UAClB,WAAW,wBAAwB,KAAK,IAAI,GAAG;AAC7C,iBAAK,UAAU;AAAA,UACjB;AAAA,MACJ;AAAA,IACF,WAAW,SAAS,GAAI;AAAA,aAAW,wBAAwB,KAAK,IAAI,GAAG;AACrE,WAAK,UAAU;AACf,WAAK,WAAW;AAAA,IAClB,WAAW,wBAAwB,KAAK,IAAI,GAAG;AAC7C,WAAK,UAAU;AACf,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO;AACL,WAAO;AAAA,MACL,UAAU,CAAC;AAAA,MACX,MAAM,KAAK;AAAA,MACX,SAAS,CAAC;AAAA,MACV,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AAAA,EACA,aAAa;AA5Ef;AA6EI,QAAI,CAAC,KAAK;AACR;AACF,SAAK,MAAM,KAAK,KAAK,IAAI;AACzB,qBAAK,OAAM,UAAX,4BAAmB,KAAK;AACxB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,aAAa,QAAQ;AACnB,QAAI,OAAO,WAAW,SAAS,CAAC,GAAG,UAAU,QAAQ,UAAU,WAAW,UAAU,SAAS,WAAW,KAAK,cAAc,SAAS,cAAc,GAAG,YAAY,CAAC;AAClK,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,YAAM,QAAQ,KAAK,QAAQ,CAAC,GAAG,QAAQ,OAAO,CAAC;AAC/C,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO;AACP;AAAA,QACF,KAAK;AACH,iBAAO,aAAa,IAAI;AACxB;AAAA,QACF,KAAK;AACH,iBAAO,WAAW,IAAI,QAAQ,KAAK;AACnC;AAAA,QACF,KAAK;AACH,gBAAM,QAAQ,WAAW,KAAK;AAC9B,cAAI;AACF,mBAAO,aAAa,IAAI;AAC1B;AAAA,QACF,KAAK;AACH,wBAAc,SAAS,OAAO,EAAE;AAChC;AAAA,QACF,KAAK;AACH,oBAAU,WAAW,KAAK;AAC1B;AAAA,QACF,KAAK;AACH,gBAAM,gBAAgB,WAAW,KAAK;AACtC,cAAI;AACF,2BAAe;AACjB;AAAA,QACF,KAAK;AACH,cAAI,SAAS,KAAK;AAChB,mBAAO,aAAa,IAAI;AAC1B;AAAA,QACF,KAAK;AACH,cAAI,SAAS,KAAK;AAChB,mBAAO,YAAY,IAAI;AACzB;AAAA,QACF,KAAK;AACH,cAAI,SAAS,KAAK;AAChB,mBAAO,iBAAiB,IAAI;AAC9B;AAAA,QACF,KAAK;AACH,cAAI,SAAS,KAAK;AAChB,mBAAO,iBAAiB,IAAI;AAC9B;AAAA,QACF,KAAK;AACH,iBAAO,gBAAgB,IAAI,QAAQ;AACnC;AAAA,QACF,KAAK;AACH,iBAAO,SAAS,IAAI,WAAW,KAAK;AACpC;AAAA,QACF,KAAK;AACH,oBAAU,KAAK,UAAU,WAAW,KAAK,IAAI,GAAG,GAAG;AACnD;AAAA,QACF,KAAK;AACH,oBAAU,KAAK,UAAU,WAAW,KAAK,IAAI,GAAG,GAAG;AACnD;AAAA,QACF,KAAK;AACH,oBAAU,KAAK,UAAU,KAAK,MAAM;AACpC;AAAA,QACF,KAAK;AACH,qBAAW,SAAS,OAAO,EAAE,IAAI;AACjC;AAAA,QACF,KAAK;AACH,iBAAO,aAAa,IAAI;AACxB,iBAAO,YAAY,IAAI,WAAW,KAAK,IAAI;AAC3C;AAAA,QACF,KAAK;AACH,iBAAO,aAAa,IAAI;AACxB,iBAAO,aAAa,IAAI,WAAW,KAAK,IAAI;AAC5C;AAAA,QACF,KAAK;AACH,oBAAU,WAAW,KAAK;AAC1B;AAAA,QACF,KAAK;AACH,qBAAW,SAAS,OAAO,EAAE;AAC7B;AAAA,QACF,KAAK;AACH,gBAAM,YAAY,SAAS,OAAO,EAAE;AACpC,cAAI,aAAa;AACf,uBAAW,aAAa,IAAI,QAAQ;AACtC,kBAAQ,YAAY,GAAG;AAAA,YACrB,KAAK;AACH,sBAAQ;AACR;AAAA,YACF,KAAK;AACH,sBAAQ;AACR;AAAA,YACF,KAAK;AACH,sBAAQ;AACR;AAAA,UACJ;AAAA,MACJ;AAAA,IACF;AACA,WAAO,YAAY;AACnB,WAAO,mBAAmB,IAAI;AAC9B,WAAO,mBAAmB,IAAI;AAC9B,WAAO,kBAAkB,IAAI;AAC7B,QAAI,aAAa,UAAU;AACzB,aAAO,WAAW,IAAI;AACtB,gBAAU,KAAK,kBAAkB;AAAA,IACnC,OAAO;AACL,aAAO,SAAS,QAAQ,EAAE,KAAK,WAAW,KAAK;AAAA,IACjD;AACA,QAAI,gBAAgB,GAAG;AACrB,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AACA,QAAI,gBAAgB,KAAK,SAAS;AAChC,aAAO,gBAAgB,IAAI,gBAAgB,IAAI,SAAS;AAAA,IAC1D;AACA,QAAI,gBAAgB,KAAK,cAAc;AACrC,aAAO,eAAe,IAAI,GAAG,QAAQ,YAAY,YAAY;AAAA,IAC/D;AACA,QAAI,gBAAgB,KAAK,OAAO,aAAa,UAAU;AACrD,YAAM,QAAQ,WAAW;AACzB,aAAO,mBAAmB,IAAI;AAAA,QAC5B,gBAAgB,gBAAgB,WAAW,KAAK,WAAW,KAAK,YAAY;AAAA,QAC5E,eAAe,gBAAgB,YAAY,WAAW,IAAI,YAAY,WAAW,IAAI,KAAK,IAAI,gBAAgB,UAAU,UAAU,KAAK;AAAA,MACzI,EAAE,OAAO,OAAO,EAAE,KAAK,IAAI;AAAA,IAC7B;AACA,QAAI,UAAU;AACZ,aAAO,iBAAiB,IAAI,UAAU,KAAK,GAAG;AAChD,SAAK,QAAQ,IAAI,IAAI;AAAA,EACvB;AAAA,EACA,eAAe,QAAQ,WAAW;AAChC,UAAM,SAAS,KAAK,aAAa,MAAM;AACvC,UAAM,YAAY,KAAK,gBAAgB,OAAO,OAAO,OAAO,KAAK,SAAS;AAC1E,QAAI,CAAC;AACH;AACF,UAAM,MAAM,IAAI,OAAO,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE,GAAG,KAAK,QAAQ,OAAO,KAAK,KAAK,CAAC,EAAE,GAAG,QAAQ,OAAO,OAAO,MAAM,OAAO,IAAI,MAAM;AACvJ,UAAM,WAAW,OAAO,WAAW,aAAa,OAAO,WAAW,WAAW,OAAO,OAAO,GAAG,cAAc,OAAO,WAAW,WAAW,OAAO,OAAO,GAAG,UAAU,OAAO,WAAW,WAAW,OAAO,OAAO;AAC/M,QAAI,YAAY;AACd,aAAO,aAAa,IAAI;AACxB,aAAO,YAAY,IAAI,aAAa;AAAA,IACtC;AACA,QAAI,aAAa;AACf,aAAO,aAAa,IAAI;AACxB,aAAO,aAAa,IAAI,cAAc;AAAA,IACxC;AACA,QAAI,WAAW,aAAa,UAAU;AACpC,aAAO,SAAS,QAAQ,EAAE,IAAI,UAAU;AAAA,IAC1C;AACA,QAAI,OAAO,QAAQ,OAAO,MAAM,KAAK,QAAQ,SAAS,CAAC,EAAE,KAAK,IAAI,EAAE,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,aAAa,IAAI;AAC5H,WAAO,OAAO;AACd,QAAI,OAAO,KAAK,MAAM,EAAE;AACtB,UAAI,QAAQ;AACd,WAAO;AAAA,EACT;AAAA,EACA,aAAa,QAAQ;AACnB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,aAAO,KAAK,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,eAAe,aAAa,WAAW;AA/OzD;AAgPI,UAAM,YAAY,kBAAkB,aAAa,GAAG,UAAU,kBAAkB,WAAW;AAC3F,QAAI,cAAc,QAAQ,YAAY,QAAQ,UAAU,WAAW;AACjE,aAAO,CAAC,WAAW,OAAO;AAAA,IAC5B,OAAO;AACL,UAAI,cAAc,MAAM;AACtB,aAAK,cAAa,UAAK,kBAAL,mBAAoB,mBAAmB,eAAe,UAAU;AAAA,MACpF;AACA,UAAI,YAAY,MAAM;AACpB,aAAK,cAAa,UAAK,kBAAL,mBAAoB,iBAAiB,aAAa,UAAU;AAAA,MAChF;AACA,UAAI,aAAa,QAAQ,YAAY,QAAQ,UAAU,WAAW;AAChE,aAAK,cAAa,UAAK,kBAAL,mBAAoB,mBAAmB,WAAW,SAAS,UAAU;AAAA,MACzF;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AA/PtB;AAgQI,QAAI,CAAC;AACH;AACF,SAAK,QAAQ,KAAK,KAAK;AACvB,QAAI,KAAK,MAAM,QAAQ;AACrB,WAAK,MAAM,OAAO;AAClB,YAAM;AAAA,IACR,OAAO;AACL,uBAAK,OAAM,YAAX,4BAAqB;AAAA,IACvB;AAAA,EACF;AACF;AACA,SAAS,WAAW,OAAO;AACzB,QAAM,OAAO,SAAS,MAAM,QAAQ,MAAM,EAAE,GAAG,EAAE;AACjD,MAAI,QAAQ,GAAG;AACb,UAAM,IAAI,QAAQ,KAAK,MAAM;AAC7B,UAAM,QAAQ,IAAI;AAClB,UAAM,IAAI,QAAQ,KAAK;AACvB,UAAM,IAAI,QAAQ,IAAI;AACtB,UAAM,IAAI,OAAO;AACjB,WAAO,UAAU,CAAC,GAAG,GAAG,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAChD;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,GAAG,GAAG,OAAO;AACpC,QAAM,cAAc,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC7C,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,UAAM,QAAQ,IAAI,KAAK,KAAK,IAAI;AAChC,kBAAc,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,UAAU,SAAS,KAAK,cAAc,IAAI,KAAK;AAAA,EACnH;AACA,SAAO;AACT;AACA,SAAS,kBAAkB;AACzB,SAAO,IAAI,UAAU;AACvB;", "names": []}