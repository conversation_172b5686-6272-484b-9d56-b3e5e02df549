FROM golang:1.21-alpine AS builder

WORKDIR /app

# Cache Go modules
COPY go.mod go.sum ./
RUN go mod download

# Copy source and build
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o autoscaler ./cmd/autoscaler/main.go

# Final image
FROM alpine:latest

# Install Docker CLI for scaling
RUN apk add --no-cache docker-cli

WORKDIR /app

# Copy built binary
COPY --from=builder /app/autoscaler .

# Run the autoscaler
CMD ["./autoscaler"]
