version: '3.8'

services:
  # Redis service for job queue
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - streamscale-network

  # PostgreSQL service for database
  postgres:
    image: postgres:13-alpine
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-streamscale}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - streamscale-network

  # API server
  api:
    build:
      context: .
      dockerfile: Dockerfile.server
    ports:
      - "8080:8080"
    environment:
      - REDIS_ADDR=redis:6379
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-streamscale}
      - AWS_ACCESS_KEY=${AWS_ACCESS_KEY}
      - AWS_SECRET_KEY=${AWS_SECRET_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - S3_ENDPOINT=${S3_ENDPOINT:-https://s3.amazonaws.com}
      - S3_INPUT_BUCKET=${S3_INPUT_BUCKET:-streamscale-input}
      - S3_OUTPUT_BUCKET=${S3_OUTPUT_BUCKET:-streamscale-output}
      - S3_CDN_ENDPOINT=${S3_CDN_ENDPOINT:-https://cdn.streamscale.com}
    volumes:
      - ./config.yml:/app/config.yml
    depends_on:
      - redis
      - postgres
    networks:
      - streamscale-network

  # Worker service (auto-scaled)
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    deploy:
      replicas: 0  # Start with 0 replicas
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
      - REDIS_ADDR=redis:6379
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-streamscale}
      - AWS_ACCESS_KEY=${AWS_ACCESS_KEY}
      - AWS_SECRET_KEY=${AWS_SECRET_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - S3_ENDPOINT=${S3_ENDPOINT:-https://s3.amazonaws.com}
      - S3_INPUT_BUCKET=${S3_INPUT_BUCKET:-streamscale-input}
      - S3_OUTPUT_BUCKET=${S3_OUTPUT_BUCKET:-streamscale-output}
      - S3_CDN_ENDPOINT=${S3_CDN_ENDPOINT:-https://cdn.streamscale.com}
    volumes:
      - ./config.yml:/app/config.yml
      - worker-tmp:/app/tmp_segments
    depends_on:
      - redis
      - postgres
    networks:
      - streamscale-network

  # Metrics exporter for queue monitoring
  metrics-exporter:
    build:
      context: .
      dockerfile: Dockerfile.metrics-exporter
    ports:
      - "9090:9090"
    environment:
      - REDIS_ADDR=redis:6379
    depends_on:
      - redis
    networks:
      - streamscale-network

  # Auto-scaler service for Docker Compose (simulates Kubernetes HPA)
  autoscaler:
    build:
      context: .
      dockerfile: Dockerfile.autoscaler
    environment:
      - METRICS_URL=http://metrics-exporter:9090/metrics
      - DOCKER_HOST=unix:///var/run/docker.sock
      - SERVICE_NAME=worker
      - MIN_REPLICAS=0
      - MAX_REPLICAS=10
      - QUEUE_THRESHOLD=1
      - SCALE_UP_COOLDOWN=10
      - SCALE_DOWN_COOLDOWN=300
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - metrics-exporter
    networks:
      - streamscale-network

networks:
  streamscale-network:
    driver: bridge

volumes:
  redis-data:
  postgres-data:
  worker-tmp:
