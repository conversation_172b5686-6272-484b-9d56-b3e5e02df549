{"version": 3, "sources": ["../../@vidstack/react/dev/vidstack.js", "../../@vidstack/react/dev/chunks/vidstack-BUd8DcBH.js", "../../@vidstack/react/dev/chunks/vidstack-Xovmcdt1.js", "../../@vidstack/react/dev/chunks/vidstack-CBF7iUqu.js", "../../@vidstack/react/dev/chunks/vidstack-C4TlCb-3.js"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>TrackSymbol, RadioGroupController, useMediaContext, menuContext, Primitive, MediaPlayerInstance, isRemotionProvider, MediaProviderInstance, mediaState, TextTrack, ToggleButtonInstance, PosterInstance, useMediaState, ThumbnailsLoader, updateSliderPreviewPlacement } from './chunks/vidstack-C-WrcxmD.js';\nexport { ARIAKeyShortcuts, AUDIO_EXTENSIONS, AUDIO_TYPES, AirPlayButtonInstance, AudioGainSliderInstance, AudioProviderLoader, AudioTrackList, CaptionButtonInstance, CaptionsInstance, ControlsGroupInstance, ControlsInstance, DASHProviderLoader, DASH_VIDEO_EXTENSIONS, DASH_VIDEO_TYPES, FullscreenButtonInstance, FullscreenController, GestureInstance, GoogleCastButtonInstance, HLSProviderLoader, HLS_VIDEO_EXTENSIONS, HLS_VIDEO_TYPES, List, LiveButtonInstance, LocalMediaStorage, Logger, MEDIA_KEY_SHORTCUTS, MediaAnnouncerInstance, MediaControls, MediaRemoteControl, MenuButtonInstance, MenuInstance, MenuItemInstance, MenuItemsInstance, MenuPortalInstance, MuteButtonInstance, PIPButtonInstance, PlayButtonInstance, QualitySliderInstance, RadioGroupInstance, RadioInstance, ScreenOrientationController, SeekButtonInstance, SliderChaptersInstance, SliderInstance, SliderPreviewInstance, SliderThumbnailInstance, SliderValueInstance, SliderVideoInstance, SpeedSliderInstance, TextRenderers, TextTrackList, ThumbnailInstance, TimeInstance, TimeRange, TimeSliderInstance, TooltipContentInstance, TooltipInstance, TooltipTriggerInstance, VIDEO_EXTENSIONS, VIDEO_TYPES, VideoProviderLoader, VideoQualityList, VimeoProviderLoader, VolumeSliderInstance, YouTubeProviderLoader, boundTime, canChangeVolume, canFullscreen, canGoogleCastSrc, canOrientScreen, canPlayHLSNatively, canRotateScreen, canUsePictureInPicture, canUseVideoPresentation, findActiveCue, formatSpokenTime, formatTime, getDownloadFile, getTimeRangesEnd, getTimeRangesStart, isAudioProvider, isAudioSrc, isCueActive, isDASHProvider, isDASHSrc, isGoogleCastProvider, isHLSProvider, isHLSSrc, isHTMLAudioElement, isHTMLIFrameElement, isHTMLMediaElement, isHTMLVideoElement, isMediaStream, isTrackCaptionKind, isVideoProvider, isVideoQualitySrc, isVideoSrc, isVimeoProvider, isYouTubeProvider, mediaContext, normalizeTimeIntervals, parseJSONCaptionsFile, sliderState, softResetMediaState, sortVideoQualities, updateTimeIntervals, useMediaStore, useSliderState, useSliderStore, watchActiveTextTrack, watchCueTextChange } from './chunks/vidstack-C-WrcxmD.js';\nimport * as React from 'react';\nimport { isString, EventsController, DOMEvent, prop, method, Component, hasProvidedContext, useContext, effect, createReactComponent, useStateContext, useSignal, composeRefs, useSignalRecord, useReactScope, signal } from './chunks/vidstack-CH225ns1.js';\nexport { appendTriggerEvent, findTriggerEvent, hasTriggerEvent, isKeyboardClick, isKeyboardEvent, isPointerEvent, walkTriggerEventChain } from './chunks/vidstack-CH225ns1.js';\nimport { createSignal, useScoped } from './chunks/vidstack-BUd8DcBH.js';\nexport { audioGainSlider as AudioGainSlider, Captions, ChapterTitle, controls as Controls, GoogleCastButton, MediaAnnouncer, qualitySlider as QualitySlider, speedSlider as SpeedSlider, spinner as Spinner, Title, tooltip as Tooltip, useActiveTextCues, useActiveTextTrack, useChapterOptions, useChapterTitle, useTextCues } from './chunks/vidstack-BUd8DcBH.js';\nimport { useMediaContext as useMediaContext$1 } from './chunks/vidstack-Xovmcdt1.js';\nexport { AirPlayButton, CaptionButton, FullscreenButton, Gesture, LiveButton, menu as Menu, MuteButton, PIPButton, PlayButton, radioGroup as RadioGroup, SeekButton, slider as Slider, thumbnail as Thumbnail, Time, timeSlider as TimeSlider, volumeSlider as VolumeSlider, useAudioOptions, useCaptionOptions, useMediaPlayer } from './chunks/vidstack-Xovmcdt1.js';\nimport { Icon } from './chunks/vidstack-CBF7iUqu.js';\nexport { DEFAULT_PLAYBACK_RATES, useMediaRemote, usePlaybackRateOptions, useVideoQualityOptions } from './chunks/vidstack-C4TlCb-3.js';\nimport '@floating-ui/dom';\nimport 'react-dom';\n\nclass LibASSTextRenderer {\n  constructor(loader, config) {\n    this.loader = loader;\n    this.config = config;\n  }\n  priority = 1;\n  #instance = null;\n  #track = null;\n  #typeRE = /(ssa|ass)$/;\n  canRender(track, video) {\n    return !!video && !!track.src && (isString(track.type) && this.#typeRE.test(track.type) || this.#typeRE.test(track.src));\n  }\n  attach(video) {\n    if (!video) return;\n    this.loader().then(async (mod) => {\n      this.#instance = new mod.default({\n        ...this.config,\n        video,\n        subUrl: this.#track?.src || \"\"\n      });\n      new EventsController(this.#instance).add(\"ready\", () => {\n        const canvas = this.#instance?._canvas;\n        if (canvas) canvas.style.pointerEvents = \"none\";\n      }).add(\"error\", (event) => {\n        if (!this.#track) return;\n        this.#track[TextTrackSymbol.readyState] = 3;\n        this.#track.dispatchEvent(\n          new DOMEvent(\"error\", {\n            trigger: event,\n            detail: event.error\n          })\n        );\n      });\n    });\n  }\n  changeTrack(track) {\n    if (!track || track.readyState === 3) {\n      this.#freeTrack();\n    } else if (this.#track !== track) {\n      this.#instance?.setTrackByUrl(track.src);\n      this.#track = track;\n    }\n  }\n  detach() {\n    this.#freeTrack();\n  }\n  #freeTrack() {\n    this.#instance?.freeTrack();\n    this.#track = null;\n  }\n}\n\nconst DEFAULT_AUDIO_GAINS = [1, 1.25, 1.5, 1.75, 2, 2.5, 3, 4];\nclass AudioGainRadioGroup extends Component {\n  static props = {\n    normalLabel: \"Disabled\",\n    gains: DEFAULT_AUDIO_GAINS\n  };\n  #media;\n  #menu;\n  #controller;\n  get value() {\n    return this.#controller.value;\n  }\n  get disabled() {\n    const { gains } = this.$props, { canSetAudioGain } = this.#media.$state;\n    return !canSetAudioGain() || gains().length === 0;\n  }\n  constructor() {\n    super();\n    this.#controller = new RadioGroupController();\n    this.#controller.onValueChange = this.#onValueChange.bind(this);\n  }\n  onSetup() {\n    this.#media = useMediaContext();\n    if (hasProvidedContext(menuContext)) {\n      this.#menu = useContext(menuContext);\n    }\n  }\n  onConnect(el) {\n    effect(this.#watchValue.bind(this));\n    effect(this.#watchHintText.bind(this));\n    effect(this.#watchControllerDisabled.bind(this));\n  }\n  getOptions() {\n    const { gains, normalLabel } = this.$props;\n    return gains().map((gain) => ({\n      label: gain === 1 || gain === null ? normalLabel : String(gain * 100) + \"%\",\n      value: gain.toString()\n    }));\n  }\n  #watchValue() {\n    this.#controller.value = this.#getValue();\n  }\n  #watchHintText() {\n    const { normalLabel } = this.$props, { audioGain } = this.#media.$state, gain = audioGain();\n    this.#menu?.hint.set(gain === 1 || gain == null ? normalLabel() : String(gain * 100) + \"%\");\n  }\n  #watchControllerDisabled() {\n    this.#menu?.disable(this.disabled);\n  }\n  #getValue() {\n    const { audioGain } = this.#media.$state;\n    return audioGain()?.toString() ?? \"1\";\n  }\n  #onValueChange(value, trigger) {\n    if (this.disabled) return;\n    const gain = +value;\n    this.#media.remote.changeAudioGain(gain, trigger);\n    this.dispatch(\"change\", { detail: gain, trigger });\n  }\n}\nconst audiogainradiogroup__proto = AudioGainRadioGroup.prototype;\nprop(audiogainradiogroup__proto, \"value\");\nprop(audiogainradiogroup__proto, \"disabled\");\nmethod(audiogainradiogroup__proto, \"getOptions\");\n\nconst playerCallbacks = [\n  \"onAbort\",\n  \"onAudioTrackChange\",\n  \"onAudioTracksChange\",\n  \"onAutoPlay\",\n  \"onAutoPlayChange\",\n  \"onAutoPlayFail\",\n  \"onCanLoad\",\n  \"onCanPlay\",\n  \"onCanPlayThrough\",\n  \"onControlsChange\",\n  \"onDestroy\",\n  \"onDurationChange\",\n  \"onEmptied\",\n  \"onEnd\",\n  \"onEnded\",\n  \"onError\",\n  \"onFindMediaPlayer\",\n  \"onFullscreenChange\",\n  \"onFullscreenError\",\n  \"onLiveChange\",\n  \"onLiveEdgeChange\",\n  \"onLoadedData\",\n  \"onLoadedMetadata\",\n  \"onLoadStart\",\n  \"onLoopChange\",\n  \"onOrientationChange\",\n  \"onPause\",\n  \"onPictureInPictureChange\",\n  \"onPictureInPictureError\",\n  \"onPlay\",\n  \"onPlayFail\",\n  \"onPlaying\",\n  \"onPlaysInlineChange\",\n  \"onPosterChange\",\n  \"onProgress\",\n  \"onProviderChange\",\n  \"onProviderLoaderChange\",\n  \"onProviderSetup\",\n  \"onQualitiesChange\",\n  \"onQualityChange\",\n  \"onRateChange\",\n  \"onReplay\",\n  \"onSeeked\",\n  \"onSeeking\",\n  \"onSourceChange\",\n  \"onSourceChange\",\n  \"onStalled\",\n  \"onStarted\",\n  \"onStreamTypeChange\",\n  \"onSuspend\",\n  \"onTextTrackChange\",\n  \"onTextTracksChange\",\n  \"onTimeUpdate\",\n  \"onTitleChange\",\n  \"onVdsLog\",\n  \"onVideoPresentationChange\",\n  \"onVolumeChange\",\n  \"onWaiting\"\n];\n\nconst MediaPlayerBridge = createReactComponent(MediaPlayerInstance, {\n  events: playerCallbacks,\n  eventsRegex: /^onHls/,\n  domEventsRegex: /^onMedia/\n});\nconst MediaPlayer = React.forwardRef(\n  ({ aspectRatio, children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(\n      MediaPlayerBridge,\n      {\n        ...props,\n        src: props.src,\n        ref: forwardRef,\n        style: {\n          aspectRatio,\n          ...props.style\n        }\n      },\n      (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children)\n    );\n  }\n);\nMediaPlayer.displayName = \"MediaPlayer\";\n\nconst MediaProviderBridge = createReactComponent(MediaProviderInstance);\nconst MediaProvider = React.forwardRef(\n  ({ loaders = [], children, iframeProps, mediaProps, ...props }, forwardRef) => {\n    const reactLoaders = React.useMemo(() => loaders.map((Loader) => new Loader()), loaders);\n    return /* @__PURE__ */ React.createElement(MediaProviderBridge, { ...props, loaders: reactLoaders, ref: forwardRef }, (props2, instance) => /* @__PURE__ */ React.createElement(\"div\", { ...props2 }, /* @__PURE__ */ React.createElement(MediaOutlet, { provider: instance, mediaProps, iframeProps }), children));\n  }\n);\nMediaProvider.displayName = \"MediaProvider\";\nfunction MediaOutlet({ provider, mediaProps, iframeProps }) {\n  const { sources, crossOrigin, poster, remotePlaybackInfo, nativeControls, viewType } = useStateContext(mediaState), { loader } = provider.$state, { $provider: $$provider, $providerSetup: $$providerSetup } = useMediaContext$1(), $sources = useSignal(sources), $nativeControls = useSignal(nativeControls), $crossOrigin = useSignal(crossOrigin), $poster = useSignal(poster), $loader = useSignal(loader), $provider = useSignal($$provider), $providerSetup = useSignal($$providerSetup), $remoteInfo = useSignal(remotePlaybackInfo), $mediaType = $loader?.mediaType(), $viewType = useSignal(viewType), isAudioView = $viewType === \"audio\", isYouTubeEmbed = $loader?.name === \"youtube\", isVimeoEmbed = $loader?.name === \"vimeo\", isEmbed = isYouTubeEmbed || isVimeoEmbed, isRemotion = $loader?.name === \"remotion\", isGoogleCast = $loader?.name === \"google-cast\", [googleCastIconPaths, setGoogleCastIconPaths] = React.useState(\"\"), [hasMounted, setHasMounted] = React.useState(false);\n  React.useEffect(() => {\n    if (!isGoogleCast || googleCastIconPaths) return;\n    import('./chunks/vidstack-CH225ns1.js').then(function (n) { return n.chromecast; }).then((mod) => {\n      setGoogleCastIconPaths(mod.default);\n    });\n  }, [isGoogleCast]);\n  React.useEffect(() => {\n    setHasMounted(true);\n  }, []);\n  if (isGoogleCast) {\n    return /* @__PURE__ */ React.createElement(\n      \"div\",\n      {\n        className: \"vds-google-cast\",\n        ref: (el) => {\n          provider.load(el);\n        }\n      },\n      /* @__PURE__ */ React.createElement(Icon, { paths: googleCastIconPaths }),\n      $remoteInfo?.deviceName ? /* @__PURE__ */ React.createElement(\"span\", { className: \"vds-google-cast-info\" }, \"Google Cast on\", \" \", /* @__PURE__ */ React.createElement(\"span\", { className: \"vds-google-cast-device-name\" }, $remoteInfo.deviceName)) : null\n    );\n  }\n  if (isRemotion) {\n    return /* @__PURE__ */ React.createElement(\"div\", { \"data-remotion-canvas\": true }, /* @__PURE__ */ React.createElement(\n      \"div\",\n      {\n        \"data-remotion-container\": true,\n        ref: (el) => {\n          provider.load(el);\n        }\n      },\n      isRemotionProvider($provider) && $providerSetup ? React.createElement($provider.render) : null\n    ));\n  }\n  return isEmbed ? React.createElement(\n    React.Fragment,\n    null,\n    React.createElement(\"iframe\", {\n      ...iframeProps,\n      className: (iframeProps?.className ? `${iframeProps.className} ` : \"\") + isYouTubeEmbed ? \"vds-youtube\" : \"vds-vimeo\",\n      suppressHydrationWarning: true,\n      tabIndex: !$nativeControls ? -1 : void 0,\n      \"aria-hidden\": \"true\",\n      \"data-no-controls\": !$nativeControls ? \"\" : void 0,\n      ref(el) {\n        provider.load(el);\n      }\n    }),\n    !$nativeControls && !isAudioView ? React.createElement(\"div\", { className: \"vds-blocker\" }) : null\n  ) : $mediaType ? React.createElement($mediaType === \"audio\" ? \"audio\" : \"video\", {\n    ...mediaProps,\n    controls: $nativeControls ? true : null,\n    crossOrigin: typeof $crossOrigin === \"boolean\" ? \"\" : $crossOrigin,\n    poster: $mediaType === \"video\" && $nativeControls && $poster ? $poster : null,\n    suppressHydrationWarning: true,\n    children: !hasMounted ? $sources.map(\n      ({ src, type }) => isString(src) ? /* @__PURE__ */ React.createElement(\"source\", { src, type: type !== \"?\" ? type : void 0, key: src }) : null\n    ) : null,\n    ref(el) {\n      provider.load(el);\n    }\n  }) : null;\n}\nMediaOutlet.displayName = \"MediaOutlet\";\n\nfunction createTextTrack(init) {\n  const media = useMediaContext$1(), track = React.useMemo(() => new TextTrack(init), Object.values(init));\n  React.useEffect(() => {\n    media.textTracks.add(track);\n    return () => void media.textTracks.remove(track);\n  }, [track]);\n  return track;\n}\n\nfunction Track({ lang, ...props }) {\n  createTextTrack({ language: lang, ...props });\n  return null;\n}\nTrack.displayName = \"Track\";\n\nconst ToggleButtonBridge = createReactComponent(ToggleButtonInstance);\nconst ToggleButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(ToggleButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nToggleButton.displayName = \"ToggleButton\";\n\nconst PosterBridge = createReactComponent(PosterInstance);\nconst Poster = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(\n      PosterBridge,\n      {\n        src: props.asChild && React.isValidElement(children) ? children.props.src : void 0,\n        ...props\n      },\n      (props2, instance) => /* @__PURE__ */ React.createElement(\n        PosterImg,\n        {\n          ...props2,\n          instance,\n          ref: composeRefs(props2.ref, forwardRef)\n        },\n        children\n      )\n    );\n  }\n);\nPoster.displayName = \"Poster\";\nconst PosterImg = React.forwardRef(\n  ({ instance, children, ...props }, forwardRef) => {\n    const { src, img, alt, crossOrigin, hidden } = instance.$state, $src = useSignal(src), $alt = useSignal(alt), $crossOrigin = useSignal(crossOrigin), $hidden = useSignal(hidden);\n    return /* @__PURE__ */ React.createElement(\n      Primitive.img,\n      {\n        ...props,\n        src: $src || void 0,\n        alt: $alt || void 0,\n        crossOrigin: $crossOrigin || void 0,\n        ref: composeRefs(img.set, forwardRef),\n        style: { display: $hidden ? \"none\" : void 0 }\n      },\n      children\n    );\n  }\n);\nPosterImg.displayName = \"PosterImg\";\n\nconst Root = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(\n    Primitive.div,\n    {\n      translate: \"yes\",\n      \"aria-live\": \"off\",\n      \"aria-atomic\": \"true\",\n      ...props,\n      ref: forwardRef\n    },\n    children\n  );\n});\nRoot.displayName = \"Caption\";\nconst Text = React.forwardRef((props, forwardRef) => {\n  const textTrack = useMediaState(\"textTrack\"), [activeCue, setActiveCue] = React.useState();\n  React.useEffect(() => {\n    if (!textTrack) return;\n    function onCueChange() {\n      setActiveCue(textTrack?.activeCues[0]);\n    }\n    textTrack.addEventListener(\"cue-change\", onCueChange);\n    return () => {\n      textTrack.removeEventListener(\"cue-change\", onCueChange);\n      setActiveCue(void 0);\n    };\n  }, [textTrack]);\n  return /* @__PURE__ */ React.createElement(\n    Primitive.span,\n    {\n      ...props,\n      \"data-part\": \"cue\",\n      dangerouslySetInnerHTML: {\n        __html: activeCue?.text || \"\"\n      },\n      ref: forwardRef\n    }\n  );\n});\nText.displayName = \"CaptionText\";\n\nvar caption = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Root: Root,\n  Text: Text\n});\n\nfunction useState(ctor, prop, ref) {\n  const initialValue = React.useMemo(() => ctor.state.record[prop], [ctor, prop]);\n  return useSignal(ref.current ? ref.current.$state[prop] : initialValue);\n}\nconst storesCache = /* @__PURE__ */ new Map();\nfunction useStore(ctor, ref) {\n  const initialStore = React.useMemo(() => {\n    let store = storesCache.get(ctor);\n    if (!store) {\n      store = new Proxy(ctor.state.record, {\n        get: (_, prop) => () => ctor.state.record[prop]\n      });\n      storesCache.set(ctor, store);\n    }\n    return store;\n  }, [ctor]);\n  return useSignalRecord(ref.current ? ref.current.$state : initialStore);\n}\n\nfunction useMediaProvider() {\n  const [provider, setProvider] = React.useState(null), context = useMediaContext$1();\n  if (!context) {\n    throw Error(\n      \"[vidstack] no media context was found - was this called outside of `<MediaPlayer>`?\"\n    );\n  }\n  React.useEffect(() => {\n    if (!context) return;\n    return effect(() => {\n      setProvider(context.$provider());\n    });\n  }, []);\n  return provider;\n}\n\nfunction useThumbnails(src, crossOrigin = null) {\n  const scope = useReactScope(), $src = createSignal(src), $crossOrigin = createSignal(crossOrigin), loader = useScoped(() => ThumbnailsLoader.create($src, $crossOrigin));\n  if (!scope) {\n    console.warn(\n      `[vidstack] \\`useThumbnails\\` must be called inside a child component of \\`<MediaPlayer>\\``\n    );\n  }\n  React.useEffect(() => {\n    $src.set(src);\n  }, [src]);\n  React.useEffect(() => {\n    $crossOrigin.set(crossOrigin);\n  }, [crossOrigin]);\n  return useSignal(loader.$images);\n}\nfunction useActiveThumbnail(thumbnails, time) {\n  return React.useMemo(() => {\n    let activeIndex = -1;\n    for (let i = thumbnails.length - 1; i >= 0; i--) {\n      const image = thumbnails[i];\n      if (time >= image.startTime && (!image.endTime || time < image.endTime)) {\n        activeIndex = i;\n        break;\n      }\n    }\n    return thumbnails[activeIndex] || null;\n  }, [thumbnails, time]);\n}\n\nfunction useSliderPreview({\n  clamp = false,\n  offset = 0,\n  orientation = \"horizontal\"\n} = {}) {\n  const [rootRef, setRootRef] = React.useState(null), [previewRef, setPreviewRef] = React.useState(null), [pointerValue, setPointerValue] = React.useState(0), [isVisible, setIsVisible] = React.useState(false);\n  React.useEffect(() => {\n    if (!rootRef) return;\n    const dragging = signal(false);\n    function updatePointerValue(event) {\n      if (!rootRef) return;\n      setPointerValue(getPointerValue(rootRef, event, orientation));\n    }\n    return effect(() => {\n      if (!dragging()) {\n        new EventsController(rootRef).add(\"pointerenter\", () => {\n          setIsVisible(true);\n          previewRef?.setAttribute(\"data-visible\", \"\");\n        }).add(\"pointerdown\", (event) => {\n          dragging.set(true);\n          updatePointerValue(event);\n        }).add(\"pointerleave\", () => {\n          setIsVisible(false);\n          previewRef?.removeAttribute(\"data-visible\");\n        }).add(\"pointermove\", updatePointerValue);\n      }\n      previewRef?.setAttribute(\"data-dragging\", \"\");\n      new EventsController(document).add(\"pointerup\", (event) => {\n        dragging.set(false);\n        previewRef?.removeAttribute(\"data-dragging\");\n        updatePointerValue(event);\n      }).add(\"pointermove\", updatePointerValue).add(\"touchmove\", (e) => e.preventDefault(), { passive: false });\n    });\n  }, [rootRef]);\n  React.useEffect(() => {\n    if (previewRef) {\n      previewRef.style.setProperty(\"--slider-pointer\", pointerValue + \"%\");\n    }\n  }, [previewRef, pointerValue]);\n  React.useEffect(() => {\n    if (!previewRef) return;\n    const update = () => {\n      updateSliderPreviewPlacement(previewRef, {\n        offset,\n        clamp,\n        orientation\n      });\n    };\n    update();\n    const resize = new ResizeObserver(update);\n    resize.observe(previewRef);\n    return () => resize.disconnect();\n  }, [previewRef, clamp, offset, orientation]);\n  return {\n    previewRootRef: setRootRef,\n    previewRef: setPreviewRef,\n    previewValue: pointerValue,\n    isPreviewVisible: isVisible\n  };\n}\nfunction getPointerValue(root, event, orientation) {\n  let thumbPositionRate, rect = root.getBoundingClientRect();\n  if (orientation === \"vertical\") {\n    const { bottom: trackBottom, height: trackHeight } = rect;\n    thumbPositionRate = (trackBottom - event.clientY) / trackHeight;\n  } else {\n    const { left: trackLeft, width: trackWidth } = rect;\n    thumbPositionRate = (event.clientX - trackLeft) / trackWidth;\n  }\n  return round(Math.max(0, Math.min(100, 100 * thumbPositionRate)));\n}\nfunction round(num) {\n  return Number(num.toFixed(3));\n}\n\nfunction useAudioGainOptions({\n  gains = DEFAULT_AUDIO_GAINS,\n  disabledLabel = \"disabled\"\n} = {}) {\n  const media = useMediaContext$1(), { audioGain, canSetAudioGain } = media.$state;\n  useSignal(audioGain);\n  useSignal(canSetAudioGain);\n  return React.useMemo(() => {\n    const options = gains.map((opt) => {\n      const label = typeof opt === \"number\" ? opt === 1 && disabledLabel ? disabledLabel : opt * 100 + \"%\" : opt.label, gain = typeof opt === \"number\" ? opt : opt.gain;\n      return {\n        label,\n        value: gain.toString(),\n        gain,\n        get selected() {\n          return audioGain() === gain;\n        },\n        select(trigger) {\n          media.remote.changeAudioGain(gain, trigger);\n        }\n      };\n    });\n    Object.defineProperty(options, \"disabled\", {\n      get() {\n        return !canSetAudioGain() || !options.length;\n      }\n    });\n    Object.defineProperty(options, \"selectedValue\", {\n      get() {\n        return audioGain()?.toString();\n      }\n    });\n    return options;\n  }, [gains]);\n}\n\nexport { caption as Caption, DEFAULT_AUDIO_GAINS, Icon, LibASSTextRenderer, MediaPlayer, MediaPlayerInstance, MediaProvider, MediaProviderInstance, Poster, PosterInstance, TextTrack, ToggleButton, ToggleButtonInstance, Track, createTextTrack, mediaState, useActiveThumbnail, useAudioGainOptions, useMediaContext$1 as useMediaContext, useMediaProvider, useMediaState, useSliderPreview, useState, useStore, useThumbnails };\n", "\"use client\"\n\nimport * as React from 'react';\nimport { createReactComponent, composeRefs, listenEvent, useReactScope, scoped, signal, computed, effect, EventsController, useSignal } from './vidstack-CH225ns1.js';\nimport { Primitive, MediaAnnouncerInstance, ControlsInstance, ControlsGroupInstance, TooltipInstance, TooltipTriggerInstance, TooltipContentInstance, GoogleCastButtonInstance, QualitySliderInstance, AudioGainSliderInstance, SpeedSliderInstance, useMediaState, watchActiveTextTrack, CaptionsInstance, formatTime, formatSpokenTime } from './vidstack-C-WrcxmD.js';\nimport { sliderCallbacks, Preview, Steps, Thumb, Track as Track$1, TrackFill as TrackFill$1, Value, useMediaContext } from './vidstack-Xovmcdt1.js';\n\nconst MediaAnnouncerBridge = createReactComponent(MediaAnnouncerInstance, {\n  events: [\"onChange\"]\n});\nconst MediaAnnouncer = React.forwardRef(\n  ({ style, children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(MediaAnnouncerBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.div,\n      {\n        ...props2,\n        style: { display: \"contents\", ...style },\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nMediaAnnouncer.displayName = \"MediaAnnouncer\";\n\nconst ControlsBridge = createReactComponent(ControlsInstance);\nconst Root$5 = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(ControlsBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n    Primitive.div,\n    {\n      ...props2,\n      ref: composeRefs(props2.ref, forwardRef)\n    },\n    children\n  ));\n});\nRoot$5.displayName = \"Controls\";\nconst ControlsGroupBridge = createReactComponent(ControlsGroupInstance);\nconst Group = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(ControlsGroupBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n    Primitive.div,\n    {\n      ...props2,\n      ref: composeRefs(props2.ref, forwardRef)\n    },\n    children\n  ));\n});\nGroup.displayName = \"ControlsGroup\";\n\nvar controls = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Group: Group,\n  Root: Root$5\n});\n\nconst TooltipBridge = createReactComponent(TooltipInstance);\nfunction Root$4({ children, ...props }) {\n  return /* @__PURE__ */ React.createElement(TooltipBridge, { ...props }, children);\n}\nRoot$4.displayName = \"Tooltip\";\nconst TriggerBridge = createReactComponent(TooltipTriggerInstance);\nconst Trigger = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(TriggerBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nTrigger.displayName = \"TooltipTrigger\";\nconst ContentBridge = createReactComponent(TooltipContentInstance);\nconst Content = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(ContentBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.div,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nContent.displayName = \"TooltipContent\";\n\nvar tooltip = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Content: Content,\n  Root: Root$4,\n  Trigger: Trigger\n});\n\nconst GoogleCastButtonBridge = createReactComponent(GoogleCastButtonInstance, {\n  domEventsRegex: /^onMedia/\n});\nconst GoogleCastButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(GoogleCastButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nGoogleCastButton.displayName = \"GoogleCastButton\";\n\nconst QualitySliderBridge = createReactComponent(QualitySliderInstance, {\n  events: sliderCallbacks,\n  domEventsRegex: /^onMedia/\n});\nconst Root$3 = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(QualitySliderBridge, { ...props, ref: forwardRef }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children));\n  }\n);\nRoot$3.displayName = \"QualitySlider\";\n\nvar qualitySlider = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Preview: Preview,\n  Root: Root$3,\n  Steps: Steps,\n  Thumb: Thumb,\n  Track: Track$1,\n  TrackFill: TrackFill$1,\n  Value: Value\n});\n\nconst AudioGainSliderBridge = createReactComponent(AudioGainSliderInstance, {\n  events: sliderCallbacks,\n  domEventsRegex: /^onMedia/\n});\nconst Root$2 = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(AudioGainSliderBridge, { ...props, ref: forwardRef }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children));\n  }\n);\nRoot$2.displayName = \"AudioGainSlider\";\n\nvar audioGainSlider = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Preview: Preview,\n  Root: Root$2,\n  Steps: Steps,\n  Thumb: Thumb,\n  Track: Track$1,\n  TrackFill: TrackFill$1,\n  Value: Value\n});\n\nconst SpeedSliderBridge = createReactComponent(SpeedSliderInstance, {\n  events: sliderCallbacks,\n  domEventsRegex: /^onMedia/\n});\nconst Root$1 = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(SpeedSliderBridge, { ...props, ref: forwardRef }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children));\n  }\n);\nRoot$1.displayName = \"SpeedSlider\";\n\nvar speedSlider = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Preview: Preview,\n  Root: Root$1,\n  Steps: Steps,\n  Thumb: Thumb,\n  Track: Track$1,\n  TrackFill: TrackFill$1,\n  Value: Value\n});\n\nconst Title = React.forwardRef(({ children, ...props }, forwardRef) => {\n  const $title = useMediaState(\"title\");\n  return /* @__PURE__ */ React.createElement(Primitive.span, { ...props, ref: forwardRef }, $title, children);\n});\nTitle.displayName = \"Title\";\n\nfunction useActiveTextCues(track) {\n  const [activeCues, setActiveCues] = React.useState([]);\n  React.useEffect(() => {\n    if (!track) {\n      setActiveCues([]);\n      return;\n    }\n    function onCuesChange() {\n      if (track) setActiveCues(track.activeCues);\n    }\n    onCuesChange();\n    return listenEvent(track, \"cue-change\", onCuesChange);\n  }, [track]);\n  return activeCues;\n}\n\nfunction useActiveTextTrack(kind) {\n  const media = useMediaContext(), [track, setTrack] = React.useState(null);\n  React.useEffect(() => {\n    return watchActiveTextTrack(media.textTracks, kind, setTrack);\n  }, [kind]);\n  return track;\n}\n\nfunction useChapterTitle() {\n  const $track = useActiveTextTrack(\"chapters\"), $cues = useActiveTextCues($track);\n  return $cues[0]?.text || \"\";\n}\n\nconst ChapterTitle = React.forwardRef(\n  ({ defaultText = \"\", children, ...props }, forwardRef) => {\n    const $chapterTitle = useChapterTitle();\n    return /* @__PURE__ */ React.createElement(Primitive.span, { ...props, ref: forwardRef }, $chapterTitle || defaultText, children);\n  }\n);\nChapterTitle.displayName = \"ChapterTitle\";\n\nconst CaptionsBridge = createReactComponent(CaptionsInstance);\nconst Captions = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(CaptionsBridge, { ...props, ref: forwardRef }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children));\n  }\n);\nCaptions.displayName = \"Captions\";\n\nconst Root = React.forwardRef(\n  ({ size = 96, children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(\n      \"svg\",\n      {\n        width: size,\n        height: size,\n        fill: \"none\",\n        viewBox: \"0 0 120 120\",\n        \"aria-hidden\": \"true\",\n        \"data-part\": \"root\",\n        ...props,\n        ref: forwardRef\n      },\n      children\n    );\n  }\n);\nconst Track = React.forwardRef(\n  ({ width = 8, children, ...props }, ref) => /* @__PURE__ */ React.createElement(\n    \"circle\",\n    {\n      cx: \"60\",\n      cy: \"60\",\n      r: \"54\",\n      stroke: \"currentColor\",\n      strokeWidth: width,\n      \"data-part\": \"track\",\n      ...props,\n      ref\n    },\n    children\n  )\n);\nconst TrackFill = React.forwardRef(\n  ({ width = 8, fillPercent = 50, children, ...props }, ref) => /* @__PURE__ */ React.createElement(\n    \"circle\",\n    {\n      cx: \"60\",\n      cy: \"60\",\n      r: \"54\",\n      stroke: \"currentColor\",\n      pathLength: \"100\",\n      strokeWidth: width,\n      strokeDasharray: 100,\n      strokeDashoffset: 100 - fillPercent,\n      \"data-part\": \"track-fill\",\n      ...props,\n      ref\n    },\n    children\n  )\n);\n\nvar spinner = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Root: Root,\n  Track: Track,\n  TrackFill: TrackFill\n});\n\nfunction createSignal(initialValue, deps = []) {\n  const scope = useReactScope();\n  return React.useMemo(() => scoped(() => signal(initialValue), scope), [scope, ...deps]);\n}\nfunction createComputed(compute, deps = []) {\n  const scope = useReactScope();\n  return React.useMemo(() => scoped(() => computed(compute), scope), [scope, ...deps]);\n}\nfunction createEffect(compute, deps = []) {\n  const scope = useReactScope();\n  React.useEffect(() => scoped(() => effect(compute), scope), [scope, ...deps]);\n}\nfunction useScoped(compute) {\n  const scope = useReactScope();\n  return React.useMemo(() => scoped(compute, scope), [scope]);\n}\n\nfunction useTextCues(track) {\n  const [cues, setCues] = React.useState([]);\n  React.useEffect(() => {\n    if (!track) return;\n    function onCuesChange() {\n      if (track) setCues([...track.cues]);\n    }\n    const events = new EventsController(track).add(\"add-cue\", onCuesChange).add(\"remove-cue\", onCuesChange);\n    onCuesChange();\n    return () => {\n      setCues([]);\n      events.abort();\n    };\n  }, [track]);\n  return cues;\n}\n\nfunction useChapterOptions() {\n  const media = useMediaContext(), track = useActiveTextTrack(\"chapters\"), cues = useTextCues(track), $startTime = useSignal(media.$state.seekableStart), $endTime = useSignal(media.$state.seekableEnd);\n  useActiveTextCues(track);\n  return React.useMemo(() => {\n    const options = track ? cues.filter((cue) => cue.startTime <= $endTime && cue.endTime >= $startTime).map((cue, i) => {\n      let currentRef = null, stopProgressEffect;\n      return {\n        cue,\n        label: cue.text,\n        value: i.toString(),\n        startTimeText: formatTime(Math.max(0, cue.startTime - $startTime)),\n        durationText: formatSpokenTime(\n          Math.min($endTime, cue.endTime) - Math.max($startTime, cue.startTime)\n        ),\n        get selected() {\n          return cue === track.activeCues[0];\n        },\n        setProgressVar(ref) {\n          if (!ref || cue !== track.activeCues[0]) {\n            stopProgressEffect?.();\n            stopProgressEffect = void 0;\n            ref?.style.setProperty(\"--progress\", \"0%\");\n            currentRef = null;\n            return;\n          }\n          if (currentRef === ref) return;\n          currentRef = ref;\n          stopProgressEffect?.();\n          stopProgressEffect = effect(() => {\n            const { realCurrentTime } = media.$state, time = realCurrentTime(), cueStartTime = Math.max($startTime, cue.startTime), duration = Math.min($endTime, cue.endTime) - cueStartTime, progress = Math.max(0, time - cueStartTime) / duration * 100;\n            ref.style.setProperty(\"--progress\", progress.toFixed(3) + \"%\");\n          });\n        },\n        select(trigger) {\n          media.remote.seek(cue.startTime - $startTime, trigger);\n        }\n      };\n    }) : [];\n    Object.defineProperty(options, \"selectedValue\", {\n      get() {\n        const index = options.findIndex((option) => option.selected);\n        return (index >= 0 ? index : 0).toString();\n      }\n    });\n    return options;\n  }, [cues, $startTime, $endTime]);\n}\n\nexport { Captions, ChapterTitle, Content, GoogleCastButton, Group, MediaAnnouncer, Root$4 as Root, Root$2 as Root$1, Root$3 as Root$2, Root$1 as Root$3, Root$5 as Root$4, Root as Root$5, Title, Track, TrackFill, Trigger, audioGainSlider, controls, createComputed, createEffect, createSignal, qualitySlider, speedSlider, spinner, tooltip, useActiveTextCues, useActiveTextTrack, useChapterOptions, useChapterTitle, useScoped, useTextCues };\n", "\"use client\"\n\nimport * as React from 'react';\nimport { useReactContext, createReactComponent, composeRefs, useSignal, noop, useStateContext, signal, effect, isString } from './vidstack-CH225ns1.js';\nimport { mediaContext, Primitive, AirPlayButtonInstance, PlayButtonInstance, CaptionButtonInstance, FullscreenButtonInstance, MuteButtonInstance, PIPButtonInstance, SeekButtonInstance, LiveButtonInstance, SliderValueInstance, useSliderState, SliderInstance, SliderPreviewInstance, VolumeSliderInstance, IS_SERVER, ThumbnailInstance, TimeSliderInstance, SliderChaptersInstance, SliderThumbnailInstance, SliderVideoInstance, mediaState, RadioGroupInstance, RadioInstance, useMediaState, MenuInstance, MenuButtonInstance, MenuItemsInstance, MenuItemInstance, GestureInstance, TimeInstance, isTrackCaptionKind } from './vidstack-C-WrcxmD.js';\nimport { createPortal } from 'react-dom';\n\nfunction useMediaContext() {\n  return useReactContext(mediaContext);\n}\n\nconst AirPlayButtonBridge = createReactComponent(AirPlayButtonInstance, {\n  domEventsRegex: /^onMedia/\n});\nconst AirPlayButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(AirPlayButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nAirPlayButton.displayName = \"AirPlayButton\";\n\nconst PlayButtonBridge = createReactComponent(PlayButtonInstance, {\n  domEventsRegex: /^onMedia/\n});\nconst PlayButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(PlayButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nPlayButton.displayName = \"PlayButton\";\n\nconst CaptionButtonBridge = createReactComponent(CaptionButtonInstance, {\n  domEventsRegex: /^onMedia/\n});\nconst CaptionButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(CaptionButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nCaptionButton.displayName = \"CaptionButton\";\n\nconst FullscreenButtonBridge = createReactComponent(FullscreenButtonInstance, {\n  domEventsRegex: /^onMedia/\n});\nconst FullscreenButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(FullscreenButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nFullscreenButton.displayName = \"FullscreenButton\";\n\nconst MuteButtonBridge = createReactComponent(MuteButtonInstance, {\n  domEventsRegex: /^onMedia/\n});\nconst MuteButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(MuteButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nMuteButton.displayName = \"MuteButton\";\n\nconst PIPButtonBridge = createReactComponent(PIPButtonInstance, {\n  domEventsRegex: /^onMedia/\n});\nconst PIPButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(PIPButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nPIPButton.displayName = \"PIPButton\";\n\nconst SeekButtonBridge = createReactComponent(SeekButtonInstance, {\n  domEventsRegex: /^onMedia/\n});\nconst SeekButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(SeekButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nSeekButton.displayName = \"SeekButton\";\n\nconst LiveButtonBridge = createReactComponent(LiveButtonInstance, {\n  domEventsRegex: /^onMedia/\n});\nconst LiveButton = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(LiveButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nLiveButton.displayName = \"LiveButton\";\n\nconst sliderCallbacks = [\n  \"onDragStart\",\n  \"onDragEnd\",\n  \"onDragValueChange\",\n  \"onValueChange\",\n  \"onPointerValueChange\"\n];\n\nconst SliderValueBridge = createReactComponent(SliderValueInstance);\n\nconst SliderBridge = createReactComponent(SliderInstance, {\n  events: sliderCallbacks\n});\nconst Root$5 = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(SliderBridge, { ...props, ref: forwardRef }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children));\n});\nRoot$5.displayName = \"Slider\";\nconst Thumb = React.forwardRef((props, forwardRef) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props, ref: forwardRef }));\nThumb.displayName = \"SliderThumb\";\nconst Track = React.forwardRef((props, forwardRef) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props, ref: forwardRef }));\nTrack.displayName = \"SliderTrack\";\nconst TrackFill = React.forwardRef((props, forwardRef) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props, ref: forwardRef }));\nTrackFill.displayName = \"SliderTrackFill\";\nconst PreviewBridge = createReactComponent(SliderPreviewInstance);\nconst Preview = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(PreviewBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.div,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nPreview.displayName = \"SliderPreview\";\nconst Value = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(SliderValueBridge, { ...props }, (props2, instance) => {\n    const $text = useSignal(() => instance.getValueText(), instance);\n    return /* @__PURE__ */ React.createElement(Primitive.div, { ...props2, ref: forwardRef }, $text, children);\n  });\n});\nValue.displayName = \"SliderValue\";\nconst Steps = React.forwardRef(({ children, ...props }, forwardRef) => {\n  const $min = useSliderState(\"min\"), $max = useSliderState(\"max\"), $step = useSliderState(\"step\"), steps = ($max - $min) / $step;\n  return /* @__PURE__ */ React.createElement(Primitive.div, { ...props, ref: forwardRef }, Array.from({ length: Math.floor(steps) + 1 }).map((_, step) => children(step)));\n});\nSteps.displayName = \"SliderSteps\";\n\nvar slider = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Preview: Preview,\n  Root: Root$5,\n  Steps: Steps,\n  Thumb: Thumb,\n  Track: Track,\n  TrackFill: TrackFill,\n  Value: Value\n});\n\nconst VolumeSliderBridge = createReactComponent(VolumeSliderInstance, {\n  events: sliderCallbacks,\n  domEventsRegex: /^onMedia/\n});\nconst Root$4 = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(VolumeSliderBridge, { ...props, ref: forwardRef }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children));\n  }\n);\nRoot$4.displayName = \"VolumeSlider\";\n\nvar volumeSlider = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Preview: Preview,\n  Root: Root$4,\n  Steps: Steps,\n  Thumb: Thumb,\n  Track: Track,\n  TrackFill: TrackFill,\n  Value: Value\n});\n\nfunction createVTTCue(startTime = 0, endTime = 0, text = \"\") {\n  if (IS_SERVER) {\n    return {\n      startTime,\n      endTime,\n      text,\n      addEventListener: noop,\n      removeEventListener: noop,\n      dispatchEvent: noop\n    };\n  }\n  return new window.VTTCue(startTime, endTime, text);\n}\nfunction appendParamsToURL(baseUrl, params) {\n  const url = new URL(baseUrl);\n  for (const key of Object.keys(params)) {\n    url.searchParams.set(key, params[key] + \"\");\n  }\n  return url.toString();\n}\n\nconst ThumbnailBridge = createReactComponent(ThumbnailInstance);\nconst Root$3 = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(ThumbnailBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n    Primitive.div,\n    {\n      ...props2,\n      ref: composeRefs(props2.ref, forwardRef)\n    },\n    children\n  ));\n});\nRoot$3.displayName = \"Thumbnail\";\nconst Img = React.forwardRef(({ children, ...props }, forwardRef) => {\n  const { src, img, crossOrigin } = useStateContext(ThumbnailInstance.state), $src = useSignal(src), $crossOrigin = useSignal(crossOrigin);\n  return /* @__PURE__ */ React.createElement(\n    Primitive.img,\n    {\n      crossOrigin: $crossOrigin,\n      ...props,\n      src: $src || void 0,\n      ref: composeRefs(img.set, forwardRef)\n    },\n    children\n  );\n});\nImg.displayName = \"ThumbnailImg\";\n\nvar thumbnail = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Img: Img,\n  Root: Root$3\n});\n\nconst TimeSliderContext = React.createContext({\n  $chapters: signal(null)\n});\nTimeSliderContext.displayName = \"TimeSliderContext\";\nconst TimeSliderBridge = createReactComponent(TimeSliderInstance, {\n  events: sliderCallbacks,\n  domEventsRegex: /^onMedia/\n});\nconst Root$2 = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    const $chapters = React.useMemo(() => signal(null), []);\n    return /* @__PURE__ */ React.createElement(TimeSliderContext.Provider, { value: { $chapters } }, /* @__PURE__ */ React.createElement(TimeSliderBridge, { ...props, ref: forwardRef }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children)));\n  }\n);\nRoot$2.displayName = \"TimeSlider\";\nconst SliderChaptersBridge = createReactComponent(SliderChaptersInstance);\nconst Chapters = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(SliderChaptersBridge, { ...props }, (props2, instance) => /* @__PURE__ */ React.createElement(\n      Primitive.div,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      /* @__PURE__ */ React.createElement(ChapterTracks, { instance }, children)\n    ));\n  }\n);\nChapters.displayName = \"SliderChapters\";\nfunction ChapterTracks({ instance, children }) {\n  const $cues = useSignal(() => instance.cues, instance), refs = React.useRef([]), emptyCue = React.useRef(), { $chapters } = React.useContext(TimeSliderContext);\n  if (!emptyCue.current) {\n    emptyCue.current = createVTTCue();\n  }\n  React.useEffect(() => {\n    $chapters.set(instance);\n    return () => void $chapters.set(null);\n  }, [instance]);\n  React.useEffect(() => {\n    instance.setRefs(refs.current);\n  }, [$cues]);\n  return children($cues.length ? $cues : [emptyCue.current], (el) => {\n    if (!el) {\n      refs.current.length = 0;\n      return;\n    }\n    refs.current.push(el);\n  });\n}\nChapterTracks.displayName = \"SliderChapterTracks\";\nconst ChapterTitle = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    const { $chapters } = React.useContext(TimeSliderContext), [title, setTitle] = React.useState();\n    React.useEffect(() => {\n      return effect(() => {\n        const chapters = $chapters(), cue = chapters?.activePointerCue || chapters?.activeCue;\n        setTitle(cue?.text || \"\");\n      });\n    }, []);\n    return /* @__PURE__ */ React.createElement(Primitive.div, { ...props, ref: forwardRef }, title, children);\n  }\n);\nChapterTitle.displayName = \"SliderChapterTitle\";\nconst Progress = React.forwardRef((props, forwardRef) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props, ref: forwardRef }));\nProgress.displayName = \"SliderProgress\";\nconst SliderThumbnailBridge = createReactComponent(SliderThumbnailInstance);\nconst ThumbnailRoot = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(SliderThumbnailBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2, ref: composeRefs(props2.ref, forwardRef) }, children));\n  }\n);\nThumbnailRoot.displayName = \"SliderThumbnail\";\nconst Thumbnail = {\n  Root: ThumbnailRoot,\n  Img: Img\n};\nconst VideoBridge = createReactComponent(SliderVideoInstance, {\n  events: [\"onCanPlay\", \"onError\"]\n});\nconst Video = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(VideoBridge, { ...props }, (props2, instance) => /* @__PURE__ */ React.createElement(\n      VideoProvider,\n      {\n        ...props2,\n        instance,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nVideo.displayName = \"SliderVideo\";\nconst VideoProvider = React.forwardRef(\n  ({ instance, children, ...props }, forwardRef) => {\n    const { canLoad } = useStateContext(mediaState), { src, video, crossOrigin } = instance.$state, $src = useSignal(src), $canLoad = useSignal(canLoad), $crossOrigin = useSignal(crossOrigin);\n    return /* @__PURE__ */ React.createElement(\n      Primitive.video,\n      {\n        style: { maxWidth: \"unset\" },\n        ...props,\n        src: $src || void 0,\n        muted: true,\n        playsInline: true,\n        preload: $canLoad ? \"auto\" : \"none\",\n        crossOrigin: $crossOrigin || void 0,\n        ref: composeRefs(video.set, forwardRef)\n      },\n      children\n    );\n  }\n);\nVideoProvider.displayName = \"SliderVideoProvider\";\n\nvar timeSlider = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  ChapterTitle: ChapterTitle,\n  Chapters: Chapters,\n  Preview: Preview,\n  Progress: Progress,\n  Root: Root$2,\n  Steps: Steps,\n  Thumb: Thumb,\n  Thumbnail: Thumbnail,\n  Track: Track,\n  TrackFill: TrackFill,\n  Value: Value,\n  Video: Video\n});\n\nconst RadioGroupBridge = createReactComponent(RadioGroupInstance, {\n  events: [\"onChange\"]\n});\nconst Root$1 = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(RadioGroupBridge, { ...props, ref: forwardRef }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children));\n  }\n);\nRoot$1.displayName = \"RadioGroup\";\nconst ItemBridge$1 = createReactComponent(RadioInstance, {\n  events: [\"onChange\", \"onSelect\"]\n});\nconst Item$1 = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(ItemBridge$1, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n    Primitive.div,\n    {\n      ...props2,\n      ref: composeRefs(props2.ref, forwardRef)\n    },\n    children\n  ));\n});\nItem$1.displayName = \"RadioItem\";\n\nvar radioGroup = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Item: Item$1,\n  Root: Root$1\n});\n\nconst MenuBridge = createReactComponent(MenuInstance, {\n  events: [\"onOpen\", \"onClose\"],\n  domEventsRegex: /^onMedia/\n});\nconst Root = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(MenuBridge, { ...props, ref: forwardRef }, (props2, instance) => /* @__PURE__ */ React.createElement(\n    Primitive.div,\n    {\n      ...props2,\n      style: { display: !instance.isSubmenu ? \"contents\" : void 0, ...props2.style }\n    },\n    children\n  ));\n});\nRoot.displayName = \"Menu\";\nconst ButtonBridge = createReactComponent(MenuButtonInstance, {\n  events: [\"onSelect\"]\n});\nconst Button = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(ButtonBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n      Primitive.button,\n      {\n        ...props2,\n        ref: composeRefs(props2.ref, forwardRef)\n      },\n      children\n    ));\n  }\n);\nButton.displayName = \"MenuButton\";\nconst Portal = React.forwardRef(\n  ({ container = null, disabled = false, children, ...props }, forwardRef) => {\n    let fullscreen = useMediaState(\"fullscreen\"), shouldPortal = disabled === \"fullscreen\" ? !fullscreen : !disabled;\n    const target = React.useMemo(() => {\n      if (IS_SERVER) return null;\n      const node = isString(container) ? document.querySelector(container) : container;\n      return node ?? document.body;\n    }, [container]);\n    return !target || !shouldPortal ? children : createPortal(\n      /* @__PURE__ */ React.createElement(\n        Primitive.div,\n        {\n          ...props,\n          style: { display: \"contents\", ...props.style },\n          ref: forwardRef\n        },\n        children\n      ),\n      target\n    );\n  }\n);\nPortal.displayName = \"MenuPortal\";\nconst ItemsBridge = createReactComponent(MenuItemsInstance);\nconst Items = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(ItemsBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n    Primitive.div,\n    {\n      ...props2,\n      ref: composeRefs(props2.ref, forwardRef)\n    },\n    children\n  ));\n});\nItems.displayName = \"MenuItems\";\nconst ItemBridge = createReactComponent(MenuItemInstance);\nconst Item = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(ItemBridge, { ...props }, (props2) => /* @__PURE__ */ React.createElement(\n    Primitive.div,\n    {\n      ...props2,\n      ref: composeRefs(props2.ref, forwardRef)\n    },\n    children\n  ));\n});\nItem.displayName = \"MenuItem\";\n\nvar menu = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Button: Button,\n  Content: Items,\n  Item: Item,\n  Items: Items,\n  Portal: Portal,\n  Radio: Item$1,\n  RadioGroup: Root$1,\n  Root: Root\n});\n\nconst GestureBridge = createReactComponent(GestureInstance, {\n  events: [\"onWillTrigger\", \"onTrigger\"]\n});\nconst Gesture = React.forwardRef(\n  ({ children, ...props }, forwardRef) => {\n    return /* @__PURE__ */ React.createElement(GestureBridge, { ...props, ref: forwardRef }, (props2) => /* @__PURE__ */ React.createElement(Primitive.div, { ...props2 }, children));\n  }\n);\nGesture.displayName = \"Gesture\";\n\nconst TimeBridge = createReactComponent(TimeInstance);\nconst Time = React.forwardRef(({ children, ...props }, forwardRef) => {\n  return /* @__PURE__ */ React.createElement(TimeBridge, { ...props }, (props2, instance) => /* @__PURE__ */ React.createElement(\n    TimeText,\n    {\n      ...props2,\n      instance,\n      ref: composeRefs(props2.ref, forwardRef)\n    },\n    children\n  ));\n});\nTime.displayName = \"Time\";\nconst TimeText = React.forwardRef(\n  ({ instance, children, ...props }, forwardRef) => {\n    const { timeText } = instance.$state, $timeText = useSignal(timeText);\n    return /* @__PURE__ */ React.createElement(Primitive.div, { ...props, ref: forwardRef }, $timeText, children);\n  }\n);\nTimeText.displayName = \"TimeText\";\n\nfunction useMediaPlayer() {\n  const context = useMediaContext();\n  if (!context) {\n    throw Error(\n      \"[vidstack] no media context was found - was this called outside of `<MediaPlayer>`?\"\n    );\n  }\n  return context?.player || null;\n}\n\nfunction useAudioOptions() {\n  const media = useMediaContext(), { audioTracks, audioTrack } = media.$state, $audioTracks = useSignal(audioTracks);\n  useSignal(audioTrack);\n  return React.useMemo(() => {\n    const options = $audioTracks.map((track) => ({\n      track,\n      label: track.label,\n      value: getTrackValue$1(track),\n      get selected() {\n        return audioTrack() === track;\n      },\n      select(trigger) {\n        const index = audioTracks().indexOf(track);\n        if (index >= 0) media.remote.changeAudioTrack(index, trigger);\n      }\n    }));\n    Object.defineProperty(options, \"disabled\", {\n      get() {\n        return options.length <= 1;\n      }\n    });\n    Object.defineProperty(options, \"selectedTrack\", {\n      get() {\n        return audioTrack();\n      }\n    });\n    Object.defineProperty(options, \"selectedValue\", {\n      get() {\n        const track = audioTrack();\n        return track ? getTrackValue$1(track) : void 0;\n      }\n    });\n    return options;\n  }, [$audioTracks]);\n}\nfunction getTrackValue$1(track) {\n  return track.label.toLowerCase();\n}\n\nfunction useCaptionOptions({ off = true } = {}) {\n  const media = useMediaContext(), { textTracks, textTrack } = media.$state, $textTracks = useSignal(textTracks);\n  useSignal(textTrack);\n  return React.useMemo(() => {\n    const captionTracks = $textTracks.filter(isTrackCaptionKind), options = captionTracks.map((track) => ({\n      track,\n      label: track.label,\n      value: getTrackValue(track),\n      get selected() {\n        return textTrack() === track;\n      },\n      select(trigger) {\n        const index = textTracks().indexOf(track);\n        if (index >= 0) media.remote.changeTextTrackMode(index, \"showing\", trigger);\n      }\n    }));\n    if (off) {\n      options.unshift({\n        track: null,\n        label: isString(off) ? off : \"Off\",\n        value: \"off\",\n        get selected() {\n          return !textTrack();\n        },\n        select(trigger) {\n          media.remote.toggleCaptions(trigger);\n        }\n      });\n    }\n    Object.defineProperty(options, \"disabled\", {\n      get() {\n        return !captionTracks.length;\n      }\n    });\n    Object.defineProperty(options, \"selectedTrack\", {\n      get() {\n        return textTrack();\n      }\n    });\n    Object.defineProperty(options, \"selectedValue\", {\n      get() {\n        const track = textTrack();\n        return track ? getTrackValue(track) : \"off\";\n      }\n    });\n    return options;\n  }, [$textTracks]);\n}\nfunction getTrackValue(track) {\n  return track.id + \":\" + track.kind + \"-\" + track.label.toLowerCase();\n}\n\nexport { AirPlayButton, Button, CaptionButton, ChapterTitle, Chapters, FullscreenButton, Gesture, Img, Item$1 as Item, Items, LiveButton, MuteButton, PIPButton, PlayButton, Portal, Preview, Progress, Root$3 as Root, Root$2 as Root$1, Root as Root$2, Root$1 as Root$3, Root$4, Root$5, SeekButton, Steps, Thumb, Thumbnail, Time, Track, TrackFill, Value, appendParamsToURL, menu, radioGroup, slider, sliderCallbacks, thumbnail, timeSlider, useAudioOptions, useCaptionOptions, useMediaContext, useMediaPlayer, volumeSlider };\n", "\"use client\"\n\nimport * as React from 'react';\n\nconst Icon = /* @__PURE__ */ React.forwardRef((props, ref) => {\n  const { width, height, size = null, paths, ...restProps } = props;\n  return React.createElement(\"svg\", {\n    viewBox: \"0 0 32 32\",\n    ...restProps,\n    width: width ?? size,\n    height: height ?? size,\n    fill: \"none\",\n    \"aria-hidden\": \"true\",\n    focusable: \"false\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref,\n    dangerouslySetInnerHTML: { __html: paths }\n  });\n});\nIcon.displayName = \"VidstackIcon\";\n\nexport { Icon };\n", "\"use client\"\n\nimport * as React from 'react';\nimport { RadioGroupController, useMediaContext, menuContext, MediaRemoteControl, MediaPlayerInstance, sortVideoQualities } from './vidstack-C-WrcxmD.js';\nimport { useMediaContext as useMediaContext$1 } from './vidstack-Xovmcdt1.js';\nimport { prop, method, Component, hasProvidedContext, useContext, effect, useSignal, isString } from './vidstack-CH225ns1.js';\n\nconst DEFAULT_PLAYBACK_RATES = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];\nclass SpeedRadioGroup extends Component {\n  static props = {\n    normalLabel: \"Normal\",\n    rates: DEFAULT_PLAYBACK_RATES\n  };\n  #media;\n  #menu;\n  #controller;\n  get value() {\n    return this.#controller.value;\n  }\n  get disabled() {\n    const { rates } = this.$props, { canSetPlaybackRate } = this.#media.$state;\n    return !canSetPlaybackRate() || rates().length === 0;\n  }\n  constructor() {\n    super();\n    this.#controller = new RadioGroupController();\n    this.#controller.onValueChange = this.#onValueChange.bind(this);\n  }\n  onSetup() {\n    this.#media = useMediaContext();\n    if (hasProvidedContext(menuContext)) {\n      this.#menu = useContext(menuContext);\n    }\n  }\n  onConnect(el) {\n    effect(this.#watchValue.bind(this));\n    effect(this.#watchHintText.bind(this));\n    effect(this.#watchControllerDisabled.bind(this));\n  }\n  getOptions() {\n    const { rates, normalLabel } = this.$props;\n    return rates().map((rate) => ({\n      label: rate === 1 ? normalLabel : rate + \"\\xD7\",\n      value: rate.toString()\n    }));\n  }\n  #watchValue() {\n    this.#controller.value = this.#getValue();\n  }\n  #watchHintText() {\n    const { normalLabel } = this.$props, { playbackRate } = this.#media.$state, rate = playbackRate();\n    this.#menu?.hint.set(rate === 1 ? normalLabel() : rate + \"\\xD7\");\n  }\n  #watchControllerDisabled() {\n    this.#menu?.disable(this.disabled);\n  }\n  #getValue() {\n    const { playbackRate } = this.#media.$state;\n    return playbackRate().toString();\n  }\n  #onValueChange(value, trigger) {\n    if (this.disabled) return;\n    const rate = +value;\n    this.#media.remote.changePlaybackRate(rate, trigger);\n    this.dispatch(\"change\", { detail: rate, trigger });\n  }\n}\nconst speedradiogroup__proto = SpeedRadioGroup.prototype;\nprop(speedradiogroup__proto, \"value\");\nprop(speedradiogroup__proto, \"disabled\");\nmethod(speedradiogroup__proto, \"getOptions\");\n\nfunction useMediaRemote(target) {\n  const media = useMediaContext$1(), remote = React.useRef();\n  if (!remote.current) {\n    remote.current = new MediaRemoteControl();\n  }\n  React.useEffect(() => {\n    const ref = target && \"current\" in target ? target.current : target, isPlayerRef = ref instanceof MediaPlayerInstance, player = isPlayerRef ? ref : media?.player;\n    remote.current.setPlayer(player ?? null);\n    remote.current.setTarget(ref ?? null);\n  }, [media, target && \"current\" in target ? target.current : target]);\n  return remote.current;\n}\n\nfunction useVideoQualityOptions({\n  auto = true,\n  sort = \"descending\"\n} = {}) {\n  const media = useMediaContext$1(), { qualities, quality, autoQuality, canSetQuality } = media.$state, $qualities = useSignal(qualities);\n  useSignal(quality);\n  useSignal(autoQuality);\n  useSignal(canSetQuality);\n  return React.useMemo(() => {\n    const sortedQualities = sortVideoQualities($qualities, sort === \"descending\"), options = sortedQualities.map((q) => {\n      return {\n        quality: q,\n        label: q.height + \"p\",\n        value: getQualityValue(q),\n        bitrateText: q.bitrate && q.bitrate > 0 ? `${(q.bitrate / 1e6).toFixed(2)} Mbps` : null,\n        get selected() {\n          return q === quality();\n        },\n        get autoSelected() {\n          return autoQuality();\n        },\n        select(trigger) {\n          const index = qualities().indexOf(q);\n          if (index >= 0) media.remote.changeQuality(index, trigger);\n        }\n      };\n    });\n    if (auto) {\n      options.unshift({\n        quality: null,\n        label: isString(auto) ? auto : \"Auto\",\n        value: \"auto\",\n        bitrateText: null,\n        get selected() {\n          return autoQuality();\n        },\n        get autoSelected() {\n          return autoQuality();\n        },\n        select(trigger) {\n          media.remote.requestAutoQuality(trigger);\n        }\n      });\n    }\n    Object.defineProperty(options, \"disabled\", {\n      get() {\n        return !canSetQuality() || $qualities.length <= 1;\n      }\n    });\n    Object.defineProperty(options, \"selectedQuality\", {\n      get() {\n        return quality();\n      }\n    });\n    Object.defineProperty(options, \"selectedValue\", {\n      get() {\n        const $quality = quality();\n        return !autoQuality() && $quality ? getQualityValue($quality) : \"auto\";\n      }\n    });\n    return options;\n  }, [$qualities, sort]);\n}\nfunction getQualityValue(quality) {\n  return quality.height + \"_\" + quality.bitrate;\n}\n\nfunction usePlaybackRateOptions({\n  rates = DEFAULT_PLAYBACK_RATES,\n  normalLabel = \"Normal\"\n} = {}) {\n  const media = useMediaContext$1(), { playbackRate, canSetPlaybackRate } = media.$state;\n  useSignal(playbackRate);\n  useSignal(canSetPlaybackRate);\n  return React.useMemo(() => {\n    const options = rates.map((opt) => {\n      const label = typeof opt === \"number\" ? opt === 1 && normalLabel ? normalLabel : opt + \"x\" : opt.label, rate = typeof opt === \"number\" ? opt : opt.rate;\n      return {\n        label,\n        value: rate.toString(),\n        rate,\n        get selected() {\n          return playbackRate() === rate;\n        },\n        select(trigger) {\n          media.remote.changePlaybackRate(rate, trigger);\n        }\n      };\n    });\n    Object.defineProperty(options, \"disabled\", {\n      get() {\n        return !canSetPlaybackRate() || !options.length;\n      }\n    });\n    Object.defineProperty(options, \"selectedValue\", {\n      get() {\n        return playbackRate().toString();\n      }\n    });\n    return options;\n  }, [rates]);\n}\n\nexport { DEFAULT_PLAYBACK_RATES, useMediaRemote, usePlaybackRateOptions, useVideoQualityOptions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAAA,SAAuB;;;ACFvB,IAAAC,SAAuB;;;ACAvB,YAAuB;AAGvB,uBAA6B;AAE7B,SAASC,mBAAkB;AACzB,SAAO,gBAAgB,YAAY;AACrC;AAEA,IAAM,sBAAsB,qBAAqB,uBAAuB;AAAA,EACtE,gBAAgB;AAClB,CAAC;AACD,IAAM,gBAAsB;AAAA,EAC1B,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGC,gBAAe;AACtC,WAA6B,oBAAc,qBAAqB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MAC9G,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,cAAc,cAAc;AAE5B,IAAM,mBAAmB,qBAAqB,oBAAoB;AAAA,EAChE,gBAAgB;AAClB,CAAC;AACD,IAAM,aAAmB;AAAA,EACvB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,kBAAkB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MAC3G,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,WAAW,cAAc;AAEzB,IAAM,sBAAsB,qBAAqB,uBAAuB;AAAA,EACtE,gBAAgB;AAClB,CAAC;AACD,IAAM,gBAAsB;AAAA,EAC1B,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,qBAAqB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MAC9G,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,cAAc,cAAc;AAE5B,IAAM,yBAAyB,qBAAqB,0BAA0B;AAAA,EAC5E,gBAAgB;AAClB,CAAC;AACD,IAAM,mBAAyB;AAAA,EAC7B,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,wBAAwB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MACjH,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,iBAAiB,cAAc;AAE/B,IAAM,mBAAmB,qBAAqB,oBAAoB;AAAA,EAChE,gBAAgB;AAClB,CAAC;AACD,IAAM,aAAmB;AAAA,EACvB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,kBAAkB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MAC3G,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,WAAW,cAAc;AAEzB,IAAM,kBAAkB,qBAAqB,mBAAmB;AAAA,EAC9D,gBAAgB;AAClB,CAAC;AACD,IAAM,YAAkB;AAAA,EACtB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,iBAAiB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MAC1G,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,UAAU,cAAc;AAExB,IAAM,mBAAmB,qBAAqB,oBAAoB;AAAA,EAChE,gBAAgB;AAClB,CAAC;AACD,IAAM,aAAmB;AAAA,EACvB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,kBAAkB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MAC3G,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,WAAW,cAAc;AAEzB,IAAM,mBAAmB,qBAAqB,oBAAoB;AAAA,EAChE,gBAAgB;AAClB,CAAC;AACD,IAAM,aAAmB;AAAA,EACvB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,kBAAkB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MAC3G,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,WAAW,cAAc;AAEzB,IAAM,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,oBAAoB,qBAAqB,mBAAmB;AAElE,IAAM,eAAe,qBAAqB,gBAAgB;AAAA,EACxD,QAAQ;AACV,CAAC;AACD,IAAM,SAAe,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtE,SAA6B,oBAAc,cAAc,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,WAAiC,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC;AACjL,CAAC;AACD,OAAO,cAAc;AACrB,IAAM,QAAc,iBAAW,CAAC,OAAOA,gBAAqC,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,KAAKA,YAAW,CAAC,CAAC;AACvI,MAAM,cAAc;AACpB,IAAM,QAAc,iBAAW,CAAC,OAAOA,gBAAqC,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,KAAKA,YAAW,CAAC,CAAC;AACvI,MAAM,cAAc;AACpB,IAAM,YAAkB,iBAAW,CAAC,OAAOA,gBAAqC,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,KAAKA,YAAW,CAAC,CAAC;AAC3I,UAAU,cAAc;AACxB,IAAM,gBAAgB,qBAAqB,qBAAqB;AAChE,IAAM,UAAgB;AAAA,EACpB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,eAAe,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MACxG,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,QAAQ,cAAc;AACtB,IAAM,QAAc,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACrE,SAA6B,oBAAc,mBAAmB,EAAE,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa;AAChG,UAAM,QAAQ,UAAU,MAAM,SAAS,aAAa,GAAG,QAAQ;AAC/D,WAA6B,oBAAc,UAAU,KAAK,EAAE,GAAG,QAAQ,KAAKA,YAAW,GAAG,OAAO,QAAQ;AAAA,EAC3G,CAAC;AACH,CAAC;AACD,MAAM,cAAc;AACpB,IAAM,QAAc,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACrE,QAAM,OAAO,eAAe,KAAK,GAAG,OAAO,eAAe,KAAK,GAAG,QAAQ,eAAe,MAAM,GAAG,SAAS,OAAO,QAAQ;AAC1H,SAA6B,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,MAAM,KAAK,EAAE,QAAQ,KAAK,MAAM,KAAK,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,SAAS,SAAS,IAAI,CAAC,CAAC;AACzK,CAAC;AACD,MAAM,cAAc;AAEpB,IAAI,SAAsB,OAAO,OAAO;AAAA,EACtC,WAAW;AAAA,EACX;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,qBAAqB,qBAAqB,sBAAsB;AAAA,EACpE,QAAQ;AAAA,EACR,gBAAgB;AAClB,CAAC;AACD,IAAM,SAAe;AAAA,EACnB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,oBAAoB,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,WAAiC,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC;AAAA,EACvL;AACF;AACA,OAAO,cAAc;AAErB,IAAI,eAA4B,OAAO,OAAO;AAAA,EAC5C,WAAW;AAAA,EACX;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,SAAS,aAAa,YAAY,GAAG,UAAU,GAAG,OAAO,IAAI;AAC3D,MAAI,WAAW;AACb,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,eAAe;AAAA,IACjB;AAAA,EACF;AACA,SAAO,IAAI,OAAO,OAAO,WAAW,SAAS,IAAI;AACnD;AASA,IAAM,kBAAkB,qBAAqB,iBAAiB;AAC9D,IAAM,SAAe,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGC,gBAAe;AACtE,SAA6B,oBAAc,iBAAiB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,IAC1G,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,IACzC;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,OAAO,cAAc;AACrB,IAAM,MAAY,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACnE,QAAM,EAAE,KAAK,KAAK,YAAY,IAAI,gBAAgB,kBAAkB,KAAK,GAAG,OAAO,UAAU,GAAG,GAAG,eAAe,UAAU,WAAW;AACvI,SAA6B;AAAA,IAC3B,UAAU;AAAA,IACV;AAAA,MACE,aAAa;AAAA,MACb,GAAG;AAAA,MACH,KAAK,QAAQ;AAAA,MACb,KAAK,YAAY,IAAI,KAAKA,WAAU;AAAA,IACtC;AAAA,IACA;AAAA,EACF;AACF,CAAC;AACD,IAAI,cAAc;AAElB,IAAI,YAAyB,OAAO,OAAO;AAAA,EACzC,WAAW;AAAA,EACX;AAAA,EACA,MAAM;AACR,CAAC;AAED,IAAM,oBAA0B,oBAAc;AAAA,EAC5C,WAAW,OAAO,IAAI;AACxB,CAAC;AACD,kBAAkB,cAAc;AAChC,IAAM,mBAAmB,qBAAqB,oBAAoB;AAAA,EAChE,QAAQ;AAAA,EACR,gBAAgB;AAClB,CAAC;AACD,IAAM,SAAe;AAAA,EACnB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,UAAM,YAAkB,cAAQ,MAAM,OAAO,IAAI,GAAG,CAAC,CAAC;AACtD,WAA6B,oBAAc,kBAAkB,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,GAAyB,oBAAc,kBAAkB,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,WAAiC,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC,CAAC;AAAA,EAChR;AACF;AACA,OAAO,cAAc;AACrB,IAAM,uBAAuB,qBAAqB,sBAAsB;AACxE,IAAM,WAAiB;AAAA,EACrB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,sBAAsB,EAAE,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAmC;AAAA,MACzH,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACsB,oBAAc,eAAe,EAAE,SAAS,GAAG,QAAQ;AAAA,IAC3E,CAAC;AAAA,EACH;AACF;AACA,SAAS,cAAc;AACvB,SAAS,cAAc,EAAE,UAAU,SAAS,GAAG;AAC7C,QAAM,QAAQ,UAAU,MAAM,SAAS,MAAM,QAAQ,GAAG,OAAa,aAAO,CAAC,CAAC,GAAG,WAAiB,aAAO,GAAG,EAAE,UAAU,IAAU,iBAAW,iBAAiB;AAC9J,MAAI,CAAC,SAAS,SAAS;AACrB,aAAS,UAAU,aAAa;AAAA,EAClC;AACA,EAAM,gBAAU,MAAM;AACpB,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM,KAAK,UAAU,IAAI,IAAI;AAAA,EACtC,GAAG,CAAC,QAAQ,CAAC;AACb,EAAM,gBAAU,MAAM;AACpB,aAAS,QAAQ,KAAK,OAAO;AAAA,EAC/B,GAAG,CAAC,KAAK,CAAC;AACV,SAAO,SAAS,MAAM,SAAS,QAAQ,CAAC,SAAS,OAAO,GAAG,CAAC,OAAO;AACjE,QAAI,CAAC,IAAI;AACP,WAAK,QAAQ,SAAS;AACtB;AAAA,IACF;AACA,SAAK,QAAQ,KAAK,EAAE;AAAA,EACtB,CAAC;AACH;AACA,cAAc,cAAc;AAC5B,IAAM,eAAqB;AAAA,EACzB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,UAAM,EAAE,UAAU,IAAU,iBAAW,iBAAiB,GAAG,CAAC,OAAO,QAAQ,IAAU,eAAS;AAC9F,IAAM,gBAAU,MAAM;AACpB,aAAO,OAAO,MAAM;AAClB,cAAM,WAAW,UAAU,GAAG,OAAM,qCAAU,sBAAoB,qCAAU;AAC5E,kBAAS,2BAAK,SAAQ,EAAE;AAAA,MAC1B,CAAC;AAAA,IACH,GAAG,CAAC,CAAC;AACL,WAA6B,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,OAAO,QAAQ;AAAA,EAC1G;AACF;AACA,aAAa,cAAc;AAC3B,IAAM,WAAiB,iBAAW,CAAC,OAAOA,gBAAqC,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,KAAKA,YAAW,CAAC,CAAC;AAC1I,SAAS,cAAc;AACvB,IAAM,wBAAwB,qBAAqB,uBAAuB;AAC1E,IAAM,gBAAsB;AAAA,EAC1B,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,uBAAuB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC,oBAAc,UAAU,KAAK,EAAE,GAAG,QAAQ,KAAK,YAAY,OAAO,KAAKA,WAAU,EAAE,GAAG,QAAQ,CAAC;AAAA,EACnN;AACF;AACA,cAAc,cAAc;AAC5B,IAAM,YAAY;AAAA,EAChB,MAAM;AAAA,EACN;AACF;AACA,IAAM,cAAc,qBAAqB,qBAAqB;AAAA,EAC5D,QAAQ,CAAC,aAAa,SAAS;AACjC,CAAC;AACD,IAAM,QAAc;AAAA,EAClB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,aAAa,EAAE,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAmC;AAAA,MAChH;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH;AAAA,QACA,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,MAAM,cAAc;AACpB,IAAM,gBAAsB;AAAA,EAC1B,CAAC,EAAE,UAAU,UAAU,GAAG,MAAM,GAAGA,gBAAe;AAChD,UAAM,EAAE,QAAQ,IAAI,gBAAgB,UAAU,GAAG,EAAE,KAAK,OAAO,YAAY,IAAI,SAAS,QAAQ,OAAO,UAAU,GAAG,GAAG,WAAW,UAAU,OAAO,GAAG,eAAe,UAAU,WAAW;AAC1L,WAA6B;AAAA,MAC3B,UAAU;AAAA,MACV;AAAA,QACE,OAAO,EAAE,UAAU,QAAQ;AAAA,QAC3B,GAAG;AAAA,QACH,KAAK,QAAQ;AAAA,QACb,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS,WAAW,SAAS;AAAA,QAC7B,aAAa,gBAAgB;AAAA,QAC7B,KAAK,YAAY,MAAM,KAAKA,WAAU;AAAA,MACxC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,cAAc,cAAc;AAE5B,IAAI,aAA0B,OAAO,OAAO;AAAA,EAC1C,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,mBAAmB,qBAAqB,oBAAoB;AAAA,EAChE,QAAQ,CAAC,UAAU;AACrB,CAAC;AACD,IAAM,SAAe;AAAA,EACnB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,kBAAkB,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,WAAiC,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC;AAAA,EACrL;AACF;AACA,OAAO,cAAc;AACrB,IAAM,eAAe,qBAAqB,eAAe;AAAA,EACvD,QAAQ,CAAC,YAAY,UAAU;AACjC,CAAC;AACD,IAAM,SAAe,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtE,SAA6B,oBAAc,cAAc,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,IACvG,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,IACzC;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,OAAO,cAAc;AAErB,IAAI,aAA0B,OAAO,OAAO;AAAA,EAC1C,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AAED,IAAM,aAAa,qBAAqB,cAAc;AAAA,EACpD,QAAQ,CAAC,UAAU,SAAS;AAAA,EAC5B,gBAAgB;AAClB,CAAC;AACD,IAAM,OAAa,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACpE,SAA6B,oBAAc,YAAY,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,QAAQ,aAAmC;AAAA,IAChI,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,OAAO,EAAE,SAAS,CAAC,SAAS,YAAY,aAAa,QAAQ,GAAG,OAAO,MAAM;AAAA,IAC/E;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,KAAK,cAAc;AACnB,IAAM,eAAe,qBAAqB,oBAAoB;AAAA,EAC5D,QAAQ,CAAC,UAAU;AACrB,CAAC;AACD,IAAM,SAAe;AAAA,EACnB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,cAAc,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MACvG,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,OAAO,cAAc;AACrB,IAAM,SAAe;AAAA,EACnB,CAAC,EAAE,YAAY,MAAM,WAAW,OAAO,UAAU,GAAG,MAAM,GAAGA,gBAAe;AAC1E,QAAI,aAAa,cAAc,YAAY,GAAG,eAAe,aAAa,eAAe,CAAC,aAAa,CAAC;AACxG,UAAM,SAAe,cAAQ,MAAM;AACjC,UAAI,UAAW,QAAO;AACtB,YAAM,OAAO,SAAS,SAAS,IAAI,SAAS,cAAc,SAAS,IAAI;AACvE,aAAO,QAAQ,SAAS;AAAA,IAC1B,GAAG,CAAC,SAAS,CAAC;AACd,WAAO,CAAC,UAAU,CAAC,eAAe,eAAW;AAAA,MACrB;AAAA,QACpB,UAAU;AAAA,QACV;AAAA,UACE,GAAG;AAAA,UACH,OAAO,EAAE,SAAS,YAAY,GAAG,MAAM,MAAM;AAAA,UAC7C,KAAKA;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,OAAO,cAAc;AACrB,IAAM,cAAc,qBAAqB,iBAAiB;AAC1D,IAAM,QAAc,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACrE,SAA6B,oBAAc,aAAa,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,IACtG,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,IACzC;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,MAAM,cAAc;AACpB,IAAM,aAAa,qBAAqB,gBAAgB;AACxD,IAAM,OAAa,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACpE,SAA6B,oBAAc,YAAY,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,IACrG,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,IACzC;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,KAAK,cAAc;AAEnB,IAAI,OAAoB,OAAO,OAAO;AAAA,EACpC,WAAW;AAAA,EACX;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,YAAY;AAAA,EACZ;AACF,CAAC;AAED,IAAM,gBAAgB,qBAAqB,iBAAiB;AAAA,EAC1D,QAAQ,CAAC,iBAAiB,WAAW;AACvC,CAAC;AACD,IAAM,UAAgB;AAAA,EACpB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,oBAAc,eAAe,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,WAAiC,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC;AAAA,EAClL;AACF;AACA,QAAQ,cAAc;AAEtB,IAAM,aAAa,qBAAqB,YAAY;AACpD,IAAM,OAAa,iBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACpE,SAA6B,oBAAc,YAAY,EAAE,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAmC;AAAA,IAC/G;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH;AAAA,MACA,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,IACzC;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,KAAK,cAAc;AACnB,IAAM,WAAiB;AAAA,EACrB,CAAC,EAAE,UAAU,UAAU,GAAG,MAAM,GAAGA,gBAAe;AAChD,UAAM,EAAE,SAAS,IAAI,SAAS,QAAQ,YAAY,UAAU,QAAQ;AACpE,WAA6B,oBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,WAAW,QAAQ;AAAA,EAC9G;AACF;AACA,SAAS,cAAc;AAEvB,SAAS,iBAAiB;AACxB,QAAM,UAAUC,iBAAgB;AAChC,MAAI,CAAC,SAAS;AACZ,UAAM;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACA,UAAO,mCAAS,WAAU;AAC5B;AAEA,SAAS,kBAAkB;AACzB,QAAM,QAAQA,iBAAgB,GAAG,EAAE,aAAa,WAAW,IAAI,MAAM,QAAQ,eAAe,UAAU,WAAW;AACjH,YAAU,UAAU;AACpB,SAAa,cAAQ,MAAM;AACzB,UAAM,UAAU,aAAa,IAAI,CAAC,WAAW;AAAA,MAC3C;AAAA,MACA,OAAO,MAAM;AAAA,MACb,OAAO,gBAAgB,KAAK;AAAA,MAC5B,IAAI,WAAW;AACb,eAAO,WAAW,MAAM;AAAA,MAC1B;AAAA,MACA,OAAO,SAAS;AACd,cAAM,QAAQ,YAAY,EAAE,QAAQ,KAAK;AACzC,YAAI,SAAS,EAAG,OAAM,OAAO,iBAAiB,OAAO,OAAO;AAAA,MAC9D;AAAA,IACF,EAAE;AACF,WAAO,eAAe,SAAS,YAAY;AAAA,MACzC,MAAM;AACJ,eAAO,QAAQ,UAAU;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,MAAM;AACJ,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,MAAM;AACJ,cAAM,QAAQ,WAAW;AACzB,eAAO,QAAQ,gBAAgB,KAAK,IAAI;AAAA,MAC1C;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,YAAY,CAAC;AACnB;AACA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,MAAM,MAAM,YAAY;AACjC;AAEA,SAAS,kBAAkB,EAAE,MAAM,KAAK,IAAI,CAAC,GAAG;AAC9C,QAAM,QAAQA,iBAAgB,GAAG,EAAE,YAAY,UAAU,IAAI,MAAM,QAAQ,cAAc,UAAU,UAAU;AAC7G,YAAU,SAAS;AACnB,SAAa,cAAQ,MAAM;AACzB,UAAM,gBAAgB,YAAY,OAAO,kBAAkB,GAAG,UAAU,cAAc,IAAI,CAAC,WAAW;AAAA,MACpG;AAAA,MACA,OAAO,MAAM;AAAA,MACb,OAAO,cAAc,KAAK;AAAA,MAC1B,IAAI,WAAW;AACb,eAAO,UAAU,MAAM;AAAA,MACzB;AAAA,MACA,OAAO,SAAS;AACd,cAAM,QAAQ,WAAW,EAAE,QAAQ,KAAK;AACxC,YAAI,SAAS,EAAG,OAAM,OAAO,oBAAoB,OAAO,WAAW,OAAO;AAAA,MAC5E;AAAA,IACF,EAAE;AACF,QAAI,KAAK;AACP,cAAQ,QAAQ;AAAA,QACd,OAAO;AAAA,QACP,OAAO,SAAS,GAAG,IAAI,MAAM;AAAA,QAC7B,OAAO;AAAA,QACP,IAAI,WAAW;AACb,iBAAO,CAAC,UAAU;AAAA,QACpB;AAAA,QACA,OAAO,SAAS;AACd,gBAAM,OAAO,eAAe,OAAO;AAAA,QACrC;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,eAAe,SAAS,YAAY;AAAA,MACzC,MAAM;AACJ,eAAO,CAAC,cAAc;AAAA,MACxB;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,MAAM;AACJ,eAAO,UAAU;AAAA,MACnB;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,MAAM;AACJ,cAAM,QAAQ,UAAU;AACxB,eAAO,QAAQ,cAAc,KAAK,IAAI;AAAA,MACxC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,CAAC;AAClB;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,MAAM,KAAK,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,YAAY;AACrE;;;ADlpBA,IAAM,uBAAuB,qBAAqB,wBAAwB;AAAA,EACxE,QAAQ,CAAC,UAAU;AACrB,CAAC;AACD,IAAM,iBAAuB;AAAA,EAC3B,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,GAAGC,gBAAe;AAC7C,WAA6B,qBAAc,sBAAsB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MAC/G,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,OAAO,EAAE,SAAS,YAAY,GAAG,MAAM;AAAA,QACvC,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,eAAe,cAAc;AAE7B,IAAM,iBAAiB,qBAAqB,gBAAgB;AAC5D,IAAMC,UAAe,kBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGD,gBAAe;AACtE,SAA6B,qBAAc,gBAAgB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,IACzG,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,IACzC;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACDC,QAAO,cAAc;AACrB,IAAM,sBAAsB,qBAAqB,qBAAqB;AACtE,IAAM,QAAc,kBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGD,gBAAe;AACrE,SAA6B,qBAAc,qBAAqB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,IAC9G,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,IACzC;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,MAAM,cAAc;AAEpB,IAAI,WAAwB,OAAO,OAAO;AAAA,EACxC,WAAW;AAAA,EACX;AAAA,EACA,MAAMC;AACR,CAAC;AAED,IAAM,gBAAgB,qBAAqB,eAAe;AAC1D,SAASC,QAAO,EAAE,UAAU,GAAG,MAAM,GAAG;AACtC,SAA6B,qBAAc,eAAe,EAAE,GAAG,MAAM,GAAG,QAAQ;AAClF;AACAA,QAAO,cAAc;AACrB,IAAM,gBAAgB,qBAAqB,sBAAsB;AACjE,IAAM,UAAgB;AAAA,EACpB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGF,gBAAe;AACtC,WAA6B,qBAAc,eAAe,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MACxG,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,QAAQ,cAAc;AACtB,IAAM,gBAAgB,qBAAqB,sBAAsB;AACjE,IAAM,UAAgB;AAAA,EACpB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B,qBAAc,eAAe,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MACxG,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,QAAQ,cAAc;AAEtB,IAAI,UAAuB,OAAO,OAAO;AAAA,EACvC,WAAW;AAAA,EACX;AAAA,EACA,MAAME;AAAA,EACN;AACF,CAAC;AAED,IAAM,yBAAyB,qBAAqB,0BAA0B;AAAA,EAC5E,gBAAgB;AAClB,CAAC;AACD,IAAM,mBAAyB;AAAA,EAC7B,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGF,gBAAe;AACtC,WAA6B,qBAAc,wBAAwB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MACjH,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,iBAAiB,cAAc;AAE/B,IAAM,sBAAsB,qBAAqB,uBAAuB;AAAA,EACtE,QAAQ;AAAA,EACR,gBAAgB;AAClB,CAAC;AACD,IAAMG,UAAe;AAAA,EACnB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGH,gBAAe;AACtC,WAA6B,qBAAc,qBAAqB,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,WAAiC,qBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC;AAAA,EACxL;AACF;AACAG,QAAO,cAAc;AAErB,IAAI,gBAA6B,OAAO,OAAO;AAAA,EAC7C,WAAW;AAAA,EACX;AAAA,EACA,MAAMA;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,wBAAwB,qBAAqB,yBAAyB;AAAA,EAC1E,QAAQ;AAAA,EACR,gBAAgB;AAClB,CAAC;AACD,IAAMC,UAAe;AAAA,EACnB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGJ,gBAAe;AACtC,WAA6B,qBAAc,uBAAuB,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,WAAiC,qBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC;AAAA,EAC1L;AACF;AACAI,QAAO,cAAc;AAErB,IAAI,kBAA+B,OAAO,OAAO;AAAA,EAC/C,WAAW;AAAA,EACX;AAAA,EACA,MAAMA;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,oBAAoB,qBAAqB,qBAAqB;AAAA,EAClE,QAAQ;AAAA,EACR,gBAAgB;AAClB,CAAC;AACD,IAAMC,UAAe;AAAA,EACnB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGL,gBAAe;AACtC,WAA6B,qBAAc,mBAAmB,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,WAAiC,qBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC;AAAA,EACtL;AACF;AACAK,QAAO,cAAc;AAErB,IAAI,cAA2B,OAAO,OAAO;AAAA,EAC3C,WAAW;AAAA,EACX;AAAA,EACA,MAAMA;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,QAAc,kBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGL,gBAAe;AACrE,QAAM,SAAS,cAAc,OAAO;AACpC,SAA6B,qBAAc,UAAU,MAAM,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,QAAQ,QAAQ;AAC5G,CAAC;AACD,MAAM,cAAc;AAEpB,SAAS,kBAAkB,OAAO;AAChC,QAAM,CAAC,YAAY,aAAa,IAAU,gBAAS,CAAC,CAAC;AACrD,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,OAAO;AACV,oBAAc,CAAC,CAAC;AAChB;AAAA,IACF;AACA,aAAS,eAAe;AACtB,UAAI,MAAO,eAAc,MAAM,UAAU;AAAA,IAC3C;AACA,iBAAa;AACb,WAAO,YAAY,OAAO,cAAc,YAAY;AAAA,EACtD,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;AAEA,SAAS,mBAAmB,MAAM;AAChC,QAAM,QAAQM,iBAAgB,GAAG,CAAC,OAAO,QAAQ,IAAU,gBAAS,IAAI;AACxE,EAAM,iBAAU,MAAM;AACpB,WAAO,qBAAqB,MAAM,YAAY,MAAM,QAAQ;AAAA,EAC9D,GAAG,CAAC,IAAI,CAAC;AACT,SAAO;AACT;AAEA,SAAS,kBAAkB;AAlN3B;AAmNE,QAAM,SAAS,mBAAmB,UAAU,GAAG,QAAQ,kBAAkB,MAAM;AAC/E,WAAO,WAAM,CAAC,MAAP,mBAAU,SAAQ;AAC3B;AAEA,IAAMC,gBAAqB;AAAA,EACzB,CAAC,EAAE,cAAc,IAAI,UAAU,GAAG,MAAM,GAAGP,gBAAe;AACxD,UAAM,gBAAgB,gBAAgB;AACtC,WAA6B,qBAAc,UAAU,MAAM,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,iBAAiB,aAAa,QAAQ;AAAA,EAClI;AACF;AACAO,cAAa,cAAc;AAE3B,IAAM,iBAAiB,qBAAqB,gBAAgB;AAC5D,IAAM,WAAiB;AAAA,EACrB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGP,gBAAe;AACtC,WAA6B,qBAAc,gBAAgB,EAAE,GAAG,OAAO,KAAKA,YAAW,GAAG,CAAC,WAAiC,qBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC;AAAA,EACnL;AACF;AACA,SAAS,cAAc;AAEvB,IAAMQ,QAAa;AAAA,EACjB,CAAC,EAAE,OAAO,IAAI,UAAU,GAAG,MAAM,GAAGR,gBAAe;AACjD,WAA6B;AAAA,MAC3B;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,QACT,eAAe;AAAA,QACf,aAAa;AAAA,QACb,GAAG;AAAA,QACH,KAAKA;AAAA,MACP;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAMS,SAAc;AAAA,EAClB,CAAC,EAAE,QAAQ,GAAG,UAAU,GAAG,MAAM,GAAG,QAA8B;AAAA,IAChE;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,aAAa;AAAA,MACb,GAAG;AAAA,MACH;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAMC,aAAkB;AAAA,EACtB,CAAC,EAAE,QAAQ,GAAG,cAAc,IAAI,UAAU,GAAG,MAAM,GAAG,QAA8B;AAAA,IAClF;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,kBAAkB,MAAM;AAAA,MACxB,aAAa;AAAA,MACb,GAAG;AAAA,MACH;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,UAAuB,OAAO,OAAO;AAAA,EACvC,WAAW;AAAA,EACX,MAAMF;AAAA,EACN,OAAOC;AAAA,EACP,WAAWC;AACb,CAAC;AAED,SAAS,aAAa,cAAc,OAAO,CAAC,GAAG;AAC7C,QAAM,QAAQ,cAAc;AAC5B,SAAa,eAAQ,MAAM,OAAO,MAAM,OAAO,YAAY,GAAG,KAAK,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;AACxF;AASA,SAAS,UAAU,SAAS;AAC1B,QAAM,QAAQ,cAAc;AAC5B,SAAa,eAAQ,MAAM,OAAO,SAAS,KAAK,GAAG,CAAC,KAAK,CAAC;AAC5D;AAEA,SAAS,YAAY,OAAO;AAC1B,QAAM,CAAC,MAAM,OAAO,IAAU,gBAAS,CAAC,CAAC;AACzC,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,MAAO;AACZ,aAAS,eAAe;AACtB,UAAI,MAAO,SAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,IACpC;AACA,UAAM,SAAS,IAAI,iBAAiB,KAAK,EAAE,IAAI,WAAW,YAAY,EAAE,IAAI,cAAc,YAAY;AACtG,iBAAa;AACb,WAAO,MAAM;AACX,cAAQ,CAAC,CAAC;AACV,aAAO,MAAM;AAAA,IACf;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;AAEA,SAAS,oBAAoB;AAC3B,QAAM,QAAQC,iBAAgB,GAAG,QAAQ,mBAAmB,UAAU,GAAG,OAAO,YAAY,KAAK,GAAG,aAAa,UAAU,MAAM,OAAO,aAAa,GAAG,WAAW,UAAU,MAAM,OAAO,WAAW;AACrM,oBAAkB,KAAK;AACvB,SAAa,eAAQ,MAAM;AACzB,UAAM,UAAU,QAAQ,KAAK,OAAO,CAAC,QAAQ,IAAI,aAAa,YAAY,IAAI,WAAW,UAAU,EAAE,IAAI,CAAC,KAAK,MAAM;AACnH,UAAI,aAAa,MAAM;AACvB,aAAO;AAAA,QACL;AAAA,QACA,OAAO,IAAI;AAAA,QACX,OAAO,EAAE,SAAS;AAAA,QAClB,eAAe,WAAW,KAAK,IAAI,GAAG,IAAI,YAAY,UAAU,CAAC;AAAA,QACjE,cAAc;AAAA,UACZ,KAAK,IAAI,UAAU,IAAI,OAAO,IAAI,KAAK,IAAI,YAAY,IAAI,SAAS;AAAA,QACtE;AAAA,QACA,IAAI,WAAW;AACb,iBAAO,QAAQ,MAAM,WAAW,CAAC;AAAA,QACnC;AAAA,QACA,eAAe,KAAK;AAClB,cAAI,CAAC,OAAO,QAAQ,MAAM,WAAW,CAAC,GAAG;AACvC;AACA,iCAAqB;AACrB,uCAAK,MAAM,YAAY,cAAc;AACrC,yBAAa;AACb;AAAA,UACF;AACA,cAAI,eAAe,IAAK;AACxB,uBAAa;AACb;AACA,+BAAqB,OAAO,MAAM;AAChC,kBAAM,EAAE,gBAAgB,IAAI,MAAM,QAAQ,OAAO,gBAAgB,GAAG,eAAe,KAAK,IAAI,YAAY,IAAI,SAAS,GAAG,WAAW,KAAK,IAAI,UAAU,IAAI,OAAO,IAAI,cAAc,WAAW,KAAK,IAAI,GAAG,OAAO,YAAY,IAAI,WAAW;AAC5O,gBAAI,MAAM,YAAY,cAAc,SAAS,QAAQ,CAAC,IAAI,GAAG;AAAA,UAC/D,CAAC;AAAA,QACH;AAAA,QACA,OAAO,SAAS;AACd,gBAAM,OAAO,KAAK,IAAI,YAAY,YAAY,OAAO;AAAA,QACvD;AAAA,MACF;AAAA,IACF,CAAC,IAAI,CAAC;AACN,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,MAAM;AACJ,cAAM,QAAQ,QAAQ,UAAU,CAAC,WAAW,OAAO,QAAQ;AAC3D,gBAAQ,SAAS,IAAI,QAAQ,GAAG,SAAS;AAAA,MAC3C;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,MAAM,YAAY,QAAQ,CAAC;AACjC;;;AElXA,IAAAC,SAAuB;AAEvB,IAAM,OAA6B,kBAAW,CAAC,OAAO,QAAQ;AAC5D,QAAM,EAAE,OAAO,QAAQ,OAAO,MAAM,OAAO,GAAG,UAAU,IAAI;AAC5D,SAAa,qBAAc,OAAO;AAAA,IAChC,SAAS;AAAA,IACT,GAAG;AAAA,IACH,OAAO,SAAS;AAAA,IAChB,QAAQ,UAAU;AAAA,IAClB,MAAM;AAAA,IACN,eAAe;AAAA,IACf,WAAW;AAAA,IACX,OAAO;AAAA,IACP;AAAA,IACA,yBAAyB,EAAE,QAAQ,MAAM;AAAA,EAC3C,CAAC;AACH,CAAC;AACD,KAAK,cAAc;;;ACjBnB,IAAAC,SAAuB;AAKvB,IAAM,yBAAyB,CAAC,MAAM,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM,CAAC;AAPtE;AAQA,IAAM,kBAAN,cAA8B,UAAU;AAAA,EAetC,cAAc;AACZ,UAAM;AAhBV;AAKE;AACA;AACA;AAUE,uBAAK,aAAc,IAAI,qBAAqB;AAC5C,uBAAK,aAAY,gBAAgB,sBAAK,8CAAe,KAAK,IAAI;AAAA,EAChE;AAAA,EAXA,IAAI,QAAQ;AACV,WAAO,mBAAK,aAAY;AAAA,EAC1B;AAAA,EACA,IAAI,WAAW;AACb,UAAM,EAAE,MAAM,IAAI,KAAK,QAAQ,EAAE,mBAAmB,IAAI,mBAAK,QAAO;AACpE,WAAO,CAAC,mBAAmB,KAAK,MAAM,EAAE,WAAW;AAAA,EACrD;AAAA,EAMA,UAAU;AACR,uBAAK,QAAS,gBAAgB;AAC9B,QAAI,mBAAmB,WAAW,GAAG;AACnC,yBAAK,OAAQ,WAAW,WAAW;AAAA,IACrC;AAAA,EACF;AAAA,EACA,UAAU,IAAI;AACZ,WAAO,sBAAK,2CAAY,KAAK,IAAI,CAAC;AAClC,WAAO,sBAAK,8CAAe,KAAK,IAAI,CAAC;AACrC,WAAO,sBAAK,wDAAyB,KAAK,IAAI,CAAC;AAAA,EACjD;AAAA,EACA,aAAa;AACX,UAAM,EAAE,OAAO,YAAY,IAAI,KAAK;AACpC,WAAO,MAAM,EAAE,IAAI,CAAC,UAAU;AAAA,MAC5B,OAAO,SAAS,IAAI,cAAc,OAAO;AAAA,MACzC,OAAO,KAAK,SAAS;AAAA,IACvB,EAAE;AAAA,EACJ;AAqBF;AArDE;AACA;AACA;AAPF;AAsCE,gBAAW,WAAG;AACZ,qBAAK,aAAY,QAAQ,sBAAK,yCAAL;AAC3B;AACA,mBAAc,WAAG;AAjDnB;AAkDI,QAAM,EAAE,YAAY,IAAI,KAAK,QAAQ,EAAE,aAAa,IAAI,mBAAK,QAAO,QAAQ,OAAO,aAAa;AAChG,2BAAK,WAAL,mBAAY,KAAK,IAAI,SAAS,IAAI,YAAY,IAAI,OAAO;AAC3D;AACA,6BAAwB,WAAG;AArD7B;AAsDI,2BAAK,WAAL,mBAAY,QAAQ,KAAK;AAC3B;AACA,cAAS,WAAG;AACV,QAAM,EAAE,aAAa,IAAI,mBAAK,QAAO;AACrC,SAAO,aAAa,EAAE,SAAS;AACjC;AACA,mBAAc,SAAC,OAAO,SAAS;AAC7B,MAAI,KAAK,SAAU;AACnB,QAAM,OAAO,CAAC;AACd,qBAAK,QAAO,OAAO,mBAAmB,MAAM,OAAO;AACnD,OAAK,SAAS,UAAU,EAAE,QAAQ,MAAM,QAAQ,CAAC;AACnD;AAxDA,cADI,iBACG,SAAQ;AAAA,EACb,aAAa;AAAA,EACb,OAAO;AACT;AAuDF,IAAM,yBAAyB,gBAAgB;AAC/C,KAAK,wBAAwB,OAAO;AACpC,KAAK,wBAAwB,UAAU;AACvC,OAAO,wBAAwB,YAAY;AAE3C,SAAS,eAAe,QAAQ;AAC9B,QAAM,QAAQC,iBAAkB,GAAG,SAAe,cAAO;AACzD,MAAI,CAAC,OAAO,SAAS;AACnB,WAAO,UAAU,IAAI,mBAAmB;AAAA,EAC1C;AACA,EAAM,iBAAU,MAAM;AACpB,UAAM,MAAM,UAAU,aAAa,SAAS,OAAO,UAAU,QAAQ,cAAc,eAAe,qBAAqB,SAAS,cAAc,MAAM,+BAAO;AAC3J,WAAO,QAAQ,UAAU,UAAU,IAAI;AACvC,WAAO,QAAQ,UAAU,OAAO,IAAI;AAAA,EACtC,GAAG,CAAC,OAAO,UAAU,aAAa,SAAS,OAAO,UAAU,MAAM,CAAC;AACnE,SAAO,OAAO;AAChB;AAEA,SAAS,uBAAuB;AAAA,EAC9B,OAAO;AAAA,EACP,OAAO;AACT,IAAI,CAAC,GAAG;AACN,QAAM,QAAQA,iBAAkB,GAAG,EAAE,WAAW,SAAS,aAAa,cAAc,IAAI,MAAM,QAAQ,aAAa,UAAU,SAAS;AACtI,YAAU,OAAO;AACjB,YAAU,WAAW;AACrB,YAAU,aAAa;AACvB,SAAa,eAAQ,MAAM;AACzB,UAAM,kBAAkB,mBAAmB,YAAY,SAAS,YAAY,GAAG,UAAU,gBAAgB,IAAI,CAAC,MAAM;AAClH,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,EAAE,SAAS;AAAA,QAClB,OAAO,gBAAgB,CAAC;AAAA,QACxB,aAAa,EAAE,WAAW,EAAE,UAAU,IAAI,IAAI,EAAE,UAAU,KAAK,QAAQ,CAAC,CAAC,UAAU;AAAA,QACnF,IAAI,WAAW;AACb,iBAAO,MAAM,QAAQ;AAAA,QACvB;AAAA,QACA,IAAI,eAAe;AACjB,iBAAO,YAAY;AAAA,QACrB;AAAA,QACA,OAAO,SAAS;AACd,gBAAM,QAAQ,UAAU,EAAE,QAAQ,CAAC;AACnC,cAAI,SAAS,EAAG,OAAM,OAAO,cAAc,OAAO,OAAO;AAAA,QAC3D;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,MAAM;AACR,cAAQ,QAAQ;AAAA,QACd,SAAS;AAAA,QACT,OAAO,SAAS,IAAI,IAAI,OAAO;AAAA,QAC/B,OAAO;AAAA,QACP,aAAa;AAAA,QACb,IAAI,WAAW;AACb,iBAAO,YAAY;AAAA,QACrB;AAAA,QACA,IAAI,eAAe;AACjB,iBAAO,YAAY;AAAA,QACrB;AAAA,QACA,OAAO,SAAS;AACd,gBAAM,OAAO,mBAAmB,OAAO;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,eAAe,SAAS,YAAY;AAAA,MACzC,MAAM;AACJ,eAAO,CAAC,cAAc,KAAK,WAAW,UAAU;AAAA,MAClD;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,mBAAmB;AAAA,MAChD,MAAM;AACJ,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,MAAM;AACJ,cAAM,WAAW,QAAQ;AACzB,eAAO,CAAC,YAAY,KAAK,WAAW,gBAAgB,QAAQ,IAAI;AAAA,MAClE;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,YAAY,IAAI,CAAC;AACvB;AACA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ,SAAS,MAAM,QAAQ;AACxC;AAEA,SAAS,uBAAuB;AAAA,EAC9B,QAAQ;AAAA,EACR,cAAc;AAChB,IAAI,CAAC,GAAG;AACN,QAAM,QAAQA,iBAAkB,GAAG,EAAE,cAAc,mBAAmB,IAAI,MAAM;AAChF,YAAU,YAAY;AACtB,YAAU,kBAAkB;AAC5B,SAAa,eAAQ,MAAM;AACzB,UAAM,UAAU,MAAM,IAAI,CAAC,QAAQ;AACjC,YAAM,QAAQ,OAAO,QAAQ,WAAW,QAAQ,KAAK,cAAc,cAAc,MAAM,MAAM,IAAI,OAAO,OAAO,OAAO,QAAQ,WAAW,MAAM,IAAI;AACnJ,aAAO;AAAA,QACL;AAAA,QACA,OAAO,KAAK,SAAS;AAAA,QACrB;AAAA,QACA,IAAI,WAAW;AACb,iBAAO,aAAa,MAAM;AAAA,QAC5B;AAAA,QACA,OAAO,SAAS;AACd,gBAAM,OAAO,mBAAmB,MAAM,OAAO;AAAA,QAC/C;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,YAAY;AAAA,MACzC,MAAM;AACJ,eAAO,CAAC,mBAAmB,KAAK,CAAC,QAAQ;AAAA,MAC3C;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,MAAM;AACJ,eAAO,aAAa,EAAE,SAAS;AAAA,MACjC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,KAAK,CAAC;AACZ;;;AJ5KA,IAAAC,oBAAO;AAdP;AAgBA,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY,QAAQ,QAAQ;AAD9B;AAKE,oCAAW;AACX,kCAAY;AACZ,+BAAS;AACT,gCAAU;AANR,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AAAA,EAKA,UAAU,OAAO,OAAO;AACtB,WAAO,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,QAAQ,SAAS,MAAM,IAAI,KAAK,mBAAK,SAAQ,KAAK,MAAM,IAAI,KAAK,mBAAK,SAAQ,KAAK,MAAM,GAAG;AAAA,EACxH;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,CAAC,MAAO;AACZ,SAAK,OAAO,EAAE,KAAK,OAAO,QAAQ;AA9BtC;AA+BM,yBAAK,WAAY,IAAI,IAAI,QAAQ;AAAA,QAC/B,GAAG,KAAK;AAAA,QACR;AAAA,QACA,UAAQ,wBAAK,YAAL,mBAAa,QAAO;AAAA,MAC9B,CAAC;AACD,UAAI,iBAAiB,mBAAK,UAAS,EAAE,IAAI,SAAS,MAAM;AApC9D,YAAAC;AAqCQ,cAAM,UAASA,MAAA,mBAAK,eAAL,gBAAAA,IAAgB;AAC/B,YAAI,OAAQ,QAAO,MAAM,gBAAgB;AAAA,MAC3C,CAAC,EAAE,IAAI,SAAS,CAAC,UAAU;AACzB,YAAI,CAAC,mBAAK,QAAQ;AAClB,2BAAK,QAAO,gBAAgB,UAAU,IAAI;AAC1C,2BAAK,QAAO;AAAA,UACV,IAAI,SAAS,SAAS;AAAA,YACpB,SAAS;AAAA,YACT,QAAQ,MAAM;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,YAAY,OAAO;AAnDrB;AAoDI,QAAI,CAAC,SAAS,MAAM,eAAe,GAAG;AACpC,4BAAK,6CAAL;AAAA,IACF,WAAW,mBAAK,YAAW,OAAO;AAChC,+BAAK,eAAL,mBAAgB,cAAc,MAAM;AACpC,yBAAK,QAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,SAAS;AACP,0BAAK,6CAAL;AAAA,EACF;AAKF;AA5CE;AACA;AACA;AARF;AA8CE,eAAU,WAAG;AA9Df;AA+DI,2BAAK,eAAL,mBAAgB;AAChB,qBAAK,QAAS;AAChB;AAGF,IAAM,sBAAsB,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,CAAC;AApE7D,IAAAC,SAAAC,QAAAC,cAAA,gCAAAC,gBAAAC,mBAAAC,6BAAAC,cAAAC;AAqEA,IAAM,sBAAN,cAAkC,UAAU;AAAA,EAe1C,cAAc;AACZ,UAAM;AAhBV;AAKE,uBAAAP;AACA,uBAAAC;AACA,uBAAAC;AAUE,uBAAKA,cAAc,IAAI,qBAAqB;AAC5C,uBAAKA,cAAY,gBAAgB,sBAAK,gCAAAK,mBAAe,KAAK,IAAI;AAAA,EAChE;AAAA,EAXA,IAAI,QAAQ;AACV,WAAO,mBAAKL,cAAY;AAAA,EAC1B;AAAA,EACA,IAAI,WAAW;AACb,UAAM,EAAE,MAAM,IAAI,KAAK,QAAQ,EAAE,gBAAgB,IAAI,mBAAKF,SAAO;AACjE,WAAO,CAAC,gBAAgB,KAAK,MAAM,EAAE,WAAW;AAAA,EAClD;AAAA,EAMA,UAAU;AACR,uBAAKA,SAAS,gBAAgB;AAC9B,QAAI,mBAAmB,WAAW,GAAG;AACnC,yBAAKC,QAAQ,WAAW,WAAW;AAAA,IACrC;AAAA,EACF;AAAA,EACA,UAAU,IAAI;AACZ,WAAO,sBAAK,gCAAAE,gBAAY,KAAK,IAAI,CAAC;AAClC,WAAO,sBAAK,gCAAAC,mBAAe,KAAK,IAAI,CAAC;AACrC,WAAO,sBAAK,gCAAAC,6BAAyB,KAAK,IAAI,CAAC;AAAA,EACjD;AAAA,EACA,aAAa;AACX,UAAM,EAAE,OAAO,YAAY,IAAI,KAAK;AACpC,WAAO,MAAM,EAAE,IAAI,CAAC,UAAU;AAAA,MAC5B,OAAO,SAAS,KAAK,SAAS,OAAO,cAAc,OAAO,OAAO,GAAG,IAAI;AAAA,MACxE,OAAO,KAAK,SAAS;AAAA,IACvB,EAAE;AAAA,EACJ;AAqBF;AArDEL,UAAA;AACAC,SAAA;AACAC,eAAA;AAPF;AAsCEC,iBAAW,WAAG;AACZ,qBAAKD,cAAY,QAAQ,sBAAK,gCAAAI,cAAL;AAC3B;AACAF,oBAAc,WAAG;AA9GnB;AA+GI,QAAM,EAAE,YAAY,IAAI,KAAK,QAAQ,EAAE,UAAU,IAAI,mBAAKJ,SAAO,QAAQ,OAAO,UAAU;AAC1F,2BAAKC,YAAL,mBAAY,KAAK,IAAI,SAAS,KAAK,QAAQ,OAAO,YAAY,IAAI,OAAO,OAAO,GAAG,IAAI;AACzF;AACAI,8BAAwB,WAAG;AAlH7B;AAmHI,2BAAKJ,YAAL,mBAAY,QAAQ,KAAK;AAC3B;AACAK,eAAS,WAAG;AArHd;AAsHI,QAAM,EAAE,UAAU,IAAI,mBAAKN,SAAO;AAClC,WAAO,eAAU,MAAV,mBAAa,eAAc;AACpC;AACAO,oBAAc,SAAC,OAAO,SAAS;AAC7B,MAAI,KAAK,SAAU;AACnB,QAAM,OAAO,CAAC;AACd,qBAAKP,SAAO,OAAO,gBAAgB,MAAM,OAAO;AAChD,OAAK,SAAS,UAAU,EAAE,QAAQ,MAAM,QAAQ,CAAC;AACnD;AAxDA,cADI,qBACG,SAAQ;AAAA,EACb,aAAa;AAAA,EACb,OAAO;AACT;AAuDF,IAAM,6BAA6B,oBAAoB;AACvD,KAAK,4BAA4B,OAAO;AACxC,KAAK,4BAA4B,UAAU;AAC3C,OAAO,4BAA4B,YAAY;AAE/C,IAAM,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,oBAAoB,qBAAqB,qBAAqB;AAAA,EAClE,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,gBAAgB;AAClB,CAAC;AACD,IAAM,cAAoB;AAAA,EACxB,CAAC,EAAE,aAAa,UAAU,GAAG,MAAM,GAAGQ,gBAAe;AACnD,WAA6B;AAAA,MAC3B;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,KAAK,MAAM;AAAA,QACX,KAAKA;AAAA,QACL,OAAO;AAAA,UACL;AAAA,UACA,GAAG,MAAM;AAAA,QACX;AAAA,MACF;AAAA,MACA,CAAC,WAAiC,qBAAc,UAAU,KAAK,EAAE,GAAG,OAAO,GAAG,QAAQ;AAAA,IACxF;AAAA,EACF;AACF;AACA,YAAY,cAAc;AAE1B,IAAM,sBAAsB,qBAAqB,qBAAqB;AACtE,IAAM,gBAAsB;AAAA,EAC1B,CAAC,EAAE,UAAU,CAAC,GAAG,UAAU,aAAa,YAAY,GAAG,MAAM,GAAGA,gBAAe;AAC7E,UAAM,eAAqB,eAAQ,MAAM,QAAQ,IAAI,CAAC,WAAW,IAAI,OAAO,CAAC,GAAG,OAAO;AACvF,WAA6B,qBAAc,qBAAqB,EAAE,GAAG,OAAO,SAAS,cAAc,KAAKA,YAAW,GAAG,CAAC,QAAQ,aAAmC,qBAAc,OAAO,EAAE,GAAG,OAAO,GAAyB,qBAAc,aAAa,EAAE,UAAU,UAAU,YAAY,YAAY,CAAC,GAAG,QAAQ,CAAC;AAAA,EACpT;AACF;AACA,cAAc,cAAc;AAC5B,SAAS,YAAY,EAAE,UAAU,YAAY,YAAY,GAAG;AAC1D,QAAM,EAAE,SAAS,aAAa,QAAQ,oBAAoB,gBAAgB,SAAS,IAAI,gBAAgB,UAAU,GAAG,EAAE,OAAO,IAAI,SAAS,QAAQ,EAAE,WAAW,YAAY,gBAAgB,gBAAgB,IAAIC,iBAAkB,GAAG,WAAW,UAAU,OAAO,GAAG,kBAAkB,UAAU,cAAc,GAAG,eAAe,UAAU,WAAW,GAAG,UAAU,UAAU,MAAM,GAAG,UAAU,UAAU,MAAM,GAAG,YAAY,UAAU,UAAU,GAAG,iBAAiB,UAAU,eAAe,GAAG,cAAc,UAAU,kBAAkB,GAAG,aAAa,mCAAS,aAAa,YAAY,UAAU,QAAQ,GAAG,cAAc,cAAc,SAAS,kBAAiB,mCAAS,UAAS,WAAW,gBAAe,mCAAS,UAAS,SAAS,UAAU,kBAAkB,cAAc,cAAa,mCAAS,UAAS,YAAY,gBAAe,mCAAS,UAAS,eAAe,CAAC,qBAAqB,sBAAsB,IAAU,gBAAS,EAAE,GAAG,CAAC,YAAY,aAAa,IAAU,gBAAS,KAAK;AAC18B,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,gBAAgB,oBAAqB;AAC1C,WAAO,iCAA+B,EAAE,KAAK,SAAU,GAAG;AAAE,aAAO,EAAE;AAAA,IAAY,CAAC,EAAE,KAAK,CAAC,QAAQ;AAChG,6BAAuB,IAAI,OAAO;AAAA,IACpC,CAAC;AAAA,EACH,GAAG,CAAC,YAAY,CAAC;AACjB,EAAM,iBAAU,MAAM;AACpB,kBAAc,IAAI;AAAA,EACpB,GAAG,CAAC,CAAC;AACL,MAAI,cAAc;AAChB,WAA6B;AAAA,MAC3B;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,KAAK,CAAC,OAAO;AACX,mBAAS,KAAK,EAAE;AAAA,QAClB;AAAA,MACF;AAAA,MACsB,qBAAc,MAAM,EAAE,OAAO,oBAAoB,CAAC;AAAA,OACxE,2CAAa,cAAmC,qBAAc,QAAQ,EAAE,WAAW,uBAAuB,GAAG,kBAAkB,KAA2B,qBAAc,QAAQ,EAAE,WAAW,8BAA8B,GAAG,YAAY,UAAU,CAAC,IAAI;AAAA,IAC3P;AAAA,EACF;AACA,MAAI,YAAY;AACd,WAA6B,qBAAc,OAAO,EAAE,wBAAwB,KAAK,GAAyB;AAAA,MACxG;AAAA,MACA;AAAA,QACE,2BAA2B;AAAA,QAC3B,KAAK,CAAC,OAAO;AACX,mBAAS,KAAK,EAAE;AAAA,QAClB;AAAA,MACF;AAAA,MACA,mBAAmB,SAAS,KAAK,iBAAuB,qBAAc,UAAU,MAAM,IAAI;AAAA,IAC5F,CAAC;AAAA,EACH;AACA,SAAO,UAAgB;AAAA,IACf;AAAA,IACN;AAAA,IACM,qBAAc,UAAU;AAAA,MAC5B,GAAG;AAAA,MACH,aAAY,2CAAa,aAAY,GAAG,YAAY,SAAS,MAAM,MAAM,iBAAiB,gBAAgB;AAAA,MAC1G,0BAA0B;AAAA,MAC1B,UAAU,CAAC,kBAAkB,KAAK;AAAA,MAClC,eAAe;AAAA,MACf,oBAAoB,CAAC,kBAAkB,KAAK;AAAA,MAC5C,IAAI,IAAI;AACN,iBAAS,KAAK,EAAE;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,IACD,CAAC,mBAAmB,CAAC,cAAoB,qBAAc,OAAO,EAAE,WAAW,cAAc,CAAC,IAAI;AAAA,EAChG,IAAI,aAAmB,qBAAc,eAAe,UAAU,UAAU,SAAS;AAAA,IAC/E,GAAG;AAAA,IACH,UAAU,kBAAkB,OAAO;AAAA,IACnC,aAAa,OAAO,iBAAiB,YAAY,KAAK;AAAA,IACtD,QAAQ,eAAe,WAAW,mBAAmB,UAAU,UAAU;AAAA,IACzE,0BAA0B;AAAA,IAC1B,UAAU,CAAC,aAAa,SAAS;AAAA,MAC/B,CAAC,EAAE,KAAK,KAAK,MAAM,SAAS,GAAG,IAA0B,qBAAc,UAAU,EAAE,KAAK,MAAM,SAAS,MAAM,OAAO,QAAQ,KAAK,IAAI,CAAC,IAAI;AAAA,IAC5I,IAAI;AAAA,IACJ,IAAI,IAAI;AACN,eAAS,KAAK,EAAE;AAAA,IAClB;AAAA,EACF,CAAC,IAAI;AACP;AACA,YAAY,cAAc;AAE1B,SAAS,gBAAgB,MAAM;AAC7B,QAAM,QAAQA,iBAAkB,GAAG,QAAc,eAAQ,MAAM,IAAI,UAAU,IAAI,GAAG,OAAO,OAAO,IAAI,CAAC;AACvG,EAAM,iBAAU,MAAM;AACpB,UAAM,WAAW,IAAI,KAAK;AAC1B,WAAO,MAAM,KAAK,MAAM,WAAW,OAAO,KAAK;AAAA,EACjD,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;AAEA,SAASC,OAAM,EAAE,MAAM,GAAG,MAAM,GAAG;AACjC,kBAAgB,EAAE,UAAU,MAAM,GAAG,MAAM,CAAC;AAC5C,SAAO;AACT;AACAA,OAAM,cAAc;AAEpB,IAAM,qBAAqB,qBAAqB,oBAAoB;AACpE,IAAM,eAAqB;AAAA,EACzB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGF,gBAAe;AACtC,WAA6B,qBAAc,oBAAoB,EAAE,GAAG,MAAM,GAAG,CAAC,WAAiC;AAAA,MAC7G,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,MACzC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,aAAa,cAAc;AAE3B,IAAM,eAAe,qBAAqB,cAAc;AACxD,IAAM,SAAe;AAAA,EACnB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACtC,WAA6B;AAAA,MAC3B;AAAA,MACA;AAAA,QACE,KAAK,MAAM,WAAiB,sBAAe,QAAQ,IAAI,SAAS,MAAM,MAAM;AAAA,QAC5E,GAAG;AAAA,MACL;AAAA,MACA,CAAC,QAAQ,aAAmC;AAAA,QAC1C;AAAA,QACA;AAAA,UACE,GAAG;AAAA,UACH;AAAA,UACA,KAAK,YAAY,OAAO,KAAKA,WAAU;AAAA,QACzC;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,OAAO,cAAc;AACrB,IAAM,YAAkB;AAAA,EACtB,CAAC,EAAE,UAAU,UAAU,GAAG,MAAM,GAAGA,gBAAe;AAChD,UAAM,EAAE,KAAK,KAAK,KAAK,aAAa,OAAO,IAAI,SAAS,QAAQ,OAAO,UAAU,GAAG,GAAG,OAAO,UAAU,GAAG,GAAG,eAAe,UAAU,WAAW,GAAG,UAAU,UAAU,MAAM;AAC/K,WAA6B;AAAA,MAC3B,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK,QAAQ;AAAA,QACb,KAAK,QAAQ;AAAA,QACb,aAAa,gBAAgB;AAAA,QAC7B,KAAK,YAAY,IAAI,KAAKA,WAAU;AAAA,QACpC,OAAO,EAAE,SAAS,UAAU,SAAS,OAAO;AAAA,MAC9C;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,UAAU,cAAc;AAExB,IAAMG,QAAa,kBAAW,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGH,gBAAe;AACpE,SAA6B;AAAA,IAC3B,UAAU;AAAA,IACV;AAAA,MACE,WAAW;AAAA,MACX,aAAa;AAAA,MACb,eAAe;AAAA,MACf,GAAG;AAAA,MACH,KAAKA;AAAA,IACP;AAAA,IACA;AAAA,EACF;AACF,CAAC;AACDG,MAAK,cAAc;AACnB,IAAM,OAAa,kBAAW,CAAC,OAAOH,gBAAe;AACnD,QAAM,YAAY,cAAc,WAAW,GAAG,CAAC,WAAW,YAAY,IAAU,gBAAS;AACzF,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,UAAW;AAChB,aAAS,cAAc;AACrB,mBAAa,uCAAW,WAAW,EAAE;AAAA,IACvC;AACA,cAAU,iBAAiB,cAAc,WAAW;AACpD,WAAO,MAAM;AACX,gBAAU,oBAAoB,cAAc,WAAW;AACvD,mBAAa,MAAM;AAAA,IACrB;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AACd,SAA6B;AAAA,IAC3B,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,aAAa;AAAA,MACb,yBAAyB;AAAA,QACvB,SAAQ,uCAAW,SAAQ;AAAA,MAC7B;AAAA,MACA,KAAKA;AAAA,IACP;AAAA,EACF;AACF,CAAC;AACD,KAAK,cAAc;AAEnB,IAAI,UAAuB,OAAO,OAAO;AAAA,EACvC,WAAW;AAAA,EACX,MAAMG;AAAA,EACN;AACF,CAAC;AAED,SAASC,UAAS,MAAMC,OAAM,KAAK;AACjC,QAAM,eAAqB,eAAQ,MAAM,KAAK,MAAM,OAAOA,KAAI,GAAG,CAAC,MAAMA,KAAI,CAAC;AAC9E,SAAO,UAAU,IAAI,UAAU,IAAI,QAAQ,OAAOA,KAAI,IAAI,YAAY;AACxE;AACA,IAAM,cAA8B,oBAAI,IAAI;AAC5C,SAAS,SAAS,MAAM,KAAK;AAC3B,QAAM,eAAqB,eAAQ,MAAM;AACvC,QAAI,QAAQ,YAAY,IAAI,IAAI;AAChC,QAAI,CAAC,OAAO;AACV,cAAQ,IAAI,MAAM,KAAK,MAAM,QAAQ;AAAA,QACnC,KAAK,CAAC,GAAGA,UAAS,MAAM,KAAK,MAAM,OAAOA,KAAI;AAAA,MAChD,CAAC;AACD,kBAAY,IAAI,MAAM,KAAK;AAAA,IAC7B;AACA,WAAO;AAAA,EACT,GAAG,CAAC,IAAI,CAAC;AACT,SAAO,gBAAgB,IAAI,UAAU,IAAI,QAAQ,SAAS,YAAY;AACxE;AAEA,SAAS,mBAAmB;AAC1B,QAAM,CAAC,UAAU,WAAW,IAAU,gBAAS,IAAI,GAAG,UAAUJ,iBAAkB;AAClF,MAAI,CAAC,SAAS;AACZ,UAAM;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACA,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,QAAS;AACd,WAAO,OAAO,MAAM;AAClB,kBAAY,QAAQ,UAAU,CAAC;AAAA,IACjC,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AAEA,SAAS,cAAc,KAAK,cAAc,MAAM;AAC9C,QAAM,QAAQ,cAAc,GAAG,OAAO,aAAa,GAAG,GAAG,eAAe,aAAa,WAAW,GAAG,SAAS,UAAU,MAAM,iBAAiB,OAAO,MAAM,YAAY,CAAC;AACvK,MAAI,CAAC,OAAO;AACV,YAAQ;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACA,EAAM,iBAAU,MAAM;AACpB,SAAK,IAAI,GAAG;AAAA,EACd,GAAG,CAAC,GAAG,CAAC;AACR,EAAM,iBAAU,MAAM;AACpB,iBAAa,IAAI,WAAW;AAAA,EAC9B,GAAG,CAAC,WAAW,CAAC;AAChB,SAAO,UAAU,OAAO,OAAO;AACjC;AACA,SAAS,mBAAmB,YAAY,MAAM;AAC5C,SAAa,eAAQ,MAAM;AACzB,QAAI,cAAc;AAClB,aAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,YAAM,QAAQ,WAAW,CAAC;AAC1B,UAAI,QAAQ,MAAM,cAAc,CAAC,MAAM,WAAW,OAAO,MAAM,UAAU;AACvE,sBAAc;AACd;AAAA,MACF;AAAA,IACF;AACA,WAAO,WAAW,WAAW,KAAK;AAAA,EACpC,GAAG,CAAC,YAAY,IAAI,CAAC;AACvB;AAEA,SAAS,iBAAiB;AAAA,EACxB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,cAAc;AAChB,IAAI,CAAC,GAAG;AACN,QAAM,CAAC,SAAS,UAAU,IAAU,gBAAS,IAAI,GAAG,CAAC,YAAY,aAAa,IAAU,gBAAS,IAAI,GAAG,CAAC,cAAc,eAAe,IAAU,gBAAS,CAAC,GAAG,CAAC,WAAW,YAAY,IAAU,gBAAS,KAAK;AAC7M,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,QAAS;AACd,UAAM,WAAW,OAAO,KAAK;AAC7B,aAAS,mBAAmB,OAAO;AACjC,UAAI,CAAC,QAAS;AACd,sBAAgB,gBAAgB,SAAS,OAAO,WAAW,CAAC;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM;AAClB,UAAI,CAAC,SAAS,GAAG;AACf,YAAI,iBAAiB,OAAO,EAAE,IAAI,gBAAgB,MAAM;AACtD,uBAAa,IAAI;AACjB,mDAAY,aAAa,gBAAgB;AAAA,QAC3C,CAAC,EAAE,IAAI,eAAe,CAAC,UAAU;AAC/B,mBAAS,IAAI,IAAI;AACjB,6BAAmB,KAAK;AAAA,QAC1B,CAAC,EAAE,IAAI,gBAAgB,MAAM;AAC3B,uBAAa,KAAK;AAClB,mDAAY,gBAAgB;AAAA,QAC9B,CAAC,EAAE,IAAI,eAAe,kBAAkB;AAAA,MAC1C;AACA,+CAAY,aAAa,iBAAiB;AAC1C,UAAI,iBAAiB,QAAQ,EAAE,IAAI,aAAa,CAAC,UAAU;AACzD,iBAAS,IAAI,KAAK;AAClB,iDAAY,gBAAgB;AAC5B,2BAAmB,KAAK;AAAA,MAC1B,CAAC,EAAE,IAAI,eAAe,kBAAkB,EAAE,IAAI,aAAa,CAAC,MAAM,EAAE,eAAe,GAAG,EAAE,SAAS,MAAM,CAAC;AAAA,IAC1G,CAAC;AAAA,EACH,GAAG,CAAC,OAAO,CAAC;AACZ,EAAM,iBAAU,MAAM;AACpB,QAAI,YAAY;AACd,iBAAW,MAAM,YAAY,oBAAoB,eAAe,GAAG;AAAA,IACrE;AAAA,EACF,GAAG,CAAC,YAAY,YAAY,CAAC;AAC7B,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,WAAY;AACjB,UAAM,SAAS,MAAM;AACnB,mCAA6B,YAAY;AAAA,QACvC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AACP,UAAM,SAAS,IAAI,eAAe,MAAM;AACxC,WAAO,QAAQ,UAAU;AACzB,WAAO,MAAM,OAAO,WAAW;AAAA,EACjC,GAAG,CAAC,YAAY,OAAO,QAAQ,WAAW,CAAC;AAC3C,SAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,kBAAkB;AAAA,EACpB;AACF;AACA,SAAS,gBAAgB,MAAM,OAAO,aAAa;AACjD,MAAI,mBAAmB,OAAO,KAAK,sBAAsB;AACzD,MAAI,gBAAgB,YAAY;AAC9B,UAAM,EAAE,QAAQ,aAAa,QAAQ,YAAY,IAAI;AACrD,yBAAqB,cAAc,MAAM,WAAW;AAAA,EACtD,OAAO;AACL,UAAM,EAAE,MAAM,WAAW,OAAO,WAAW,IAAI;AAC/C,yBAAqB,MAAM,UAAU,aAAa;AAAA,EACpD;AACA,SAAO,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,MAAM,iBAAiB,CAAC,CAAC;AAClE;AACA,SAAS,MAAM,KAAK;AAClB,SAAO,OAAO,IAAI,QAAQ,CAAC,CAAC;AAC9B;AAEA,SAAS,oBAAoB;AAAA,EAC3B,QAAQ;AAAA,EACR,gBAAgB;AAClB,IAAI,CAAC,GAAG;AACN,QAAM,QAAQA,iBAAkB,GAAG,EAAE,WAAW,gBAAgB,IAAI,MAAM;AAC1E,YAAU,SAAS;AACnB,YAAU,eAAe;AACzB,SAAa,eAAQ,MAAM;AACzB,UAAM,UAAU,MAAM,IAAI,CAAC,QAAQ;AACjC,YAAM,QAAQ,OAAO,QAAQ,WAAW,QAAQ,KAAK,gBAAgB,gBAAgB,MAAM,MAAM,MAAM,IAAI,OAAO,OAAO,OAAO,QAAQ,WAAW,MAAM,IAAI;AAC7J,aAAO;AAAA,QACL;AAAA,QACA,OAAO,KAAK,SAAS;AAAA,QACrB;AAAA,QACA,IAAI,WAAW;AACb,iBAAO,UAAU,MAAM;AAAA,QACzB;AAAA,QACA,OAAO,SAAS;AACd,gBAAM,OAAO,gBAAgB,MAAM,OAAO;AAAA,QAC5C;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,YAAY;AAAA,MACzC,MAAM;AACJ,eAAO,CAAC,gBAAgB,KAAK,CAAC,QAAQ;AAAA,MACxC;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,MAAM;AAlkBZ;AAmkBQ,gBAAO,eAAU,MAAV,mBAAa;AAAA,MACtB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,KAAK,CAAC;AACZ;", "names": ["React", "React", "useMediaContext", "forwardRef", "forwardRef", "useMediaContext", "forwardRef", "Root$5", "Root$4", "Root$3", "Root$2", "Root$1", "useMediaContext", "ChapterTitle", "Root", "Track", "TrackFill", "useMediaContext", "React", "React", "useMediaContext", "import_react_dom", "_a", "_media", "_menu", "_controller", "watchValue_fn", "watchHintText_fn", "watchControllerDisabled_fn", "getValue_fn", "onValueChange_fn", "forwardRef", "useMediaContext", "Track", "Root", "useState", "prop"]}