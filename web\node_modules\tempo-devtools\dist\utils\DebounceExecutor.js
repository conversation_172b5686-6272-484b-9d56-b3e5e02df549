"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DebounceExecutor = void 0;
const TIME_PER_FRAME_MS = 16; // One frame at 60fps
class DebounceExecutor {
    constructor() {
        this.animationFrameId = null;
    }
    /**
     * Schedules a task to be executed using requestAnimationFrame.
     * If there's already a pending task, it will be replaced with the new one.
     * @param task The callback function to be executed
     * @param timeoutMs Optional timeout in milliseconds for warning (default: 16ms)
     */
    schedule(task) {
        // Cancel any existing animation frame
        if (this.animationFrameId !== null) {
            cancelAnimationFrame(this.animationFrameId);
        }
        this.animationFrameId = requestAnimationFrame(() => {
            const startTime = performance.now();
            task();
            const duration = performance.now() - startTime;
            if (duration > TIME_PER_FRAME_MS) {
                console.warn(`Took ${duration.toFixed(2)}ms to execute, which may affect app responsiveness`);
            }
            this.animationFrameId = null;
        });
    }
}
exports.DebounceExecutor = DebounceExecutor;
//# sourceMappingURL=data:application/json;base64,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