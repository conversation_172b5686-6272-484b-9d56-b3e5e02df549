"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initChannelMessagingFunctions = void 0;
const identifierUtils_1 = require("./identifierUtils");
const sessionStorageUtils_1 = require("./sessionStorageUtils");
const navTreeUtils_1 = require("./navTreeUtils");
// @ts-ignore
const jquery_1 = __importDefault(require("jquery"));
const lodash_1 = __importDefault(require("lodash"));
const outlineUtils_1 = require("./outlineUtils");
const cssFunctions_1 = require("./cssFunctions");
const constantsAndTypes_1 = require("./constantsAndTypes");
const changeItemFunctions_1 = require("./changeItemFunctions");
const tempoElement_1 = require("./tempoElement");
const editTextUtils_1 = require("./editTextUtils");
const DebounceExecutor_1 = require("../utils/DebounceExecutor");
const domUtils_1 = require("./domUtils");
const PIXELS_TO_MOVE_BEFORE_DRAG = 20;
const IMMEDIATELY_REMOVE_POINTER_LOCK = 'IMMEDIATELY_REMOVE_POINTER_LOCK';
const LAST_NAV_TREE_REFRESH_TIME = 'LAST_NAV_TREE_REFRESH_TIME';
// TODO: Change all of this to be a react wrapper library
const initChannelMessagingFunctions = () => {
    (0, changeItemFunctions_1.resetIntermediateClassesForSliderInstantUpdate)();
    // All processes that involves updating the UI should use this runner to avoid triggering a cascade of updates
    let globalUIUpdateRunner = domUtils_1.defaultUIUpdateRunner;
    // @ts-ignore
    String.prototype.hashCode = function () {
        var hash = 0, i, chr;
        if (this.length === 0)
            return hash;
        for (i = 0; i < this.length; i++) {
            chr = this.charCodeAt(i);
            hash = (hash << 5) - hash + chr;
            hash |= 0; // Convert to 32bit integer
        }
        return hash;
    };
    // We want to make event listeners non-passive, and to do so have to check
    // that browsers support EventListenerOptions in the first place.
    // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support
    let passiveSupported = false;
    const makePassiveEventOption = () => {
        try {
            const options = {
                get passive() {
                    // This function will be called when the browser
                    //   attempts to access the passive property.
                    passiveSupported = true;
                    return false;
                },
            };
            return options;
        }
        catch (err) {
            passiveSupported = false;
            return passiveSupported;
        }
    };
    /**
     * Taken from: https://stackoverflow.com/questions/3219758/detect-changes-in-the-dom
     *
     * Returns the function to disconnect the observer
     */
    const observeDOM = (function () {
        // @ts-ignore
        var MutationObserver = 
        // @ts-ignore
        window.MutationObserver || window.WebKitMutationObserver;
        return function (objs, callback) {
            const filteredObjs = objs.filter((obj) => obj && obj.nodeType === 1);
            if (filteredObjs.length === 0) {
                return domUtils_1.defaultUIUpdateRunner;
            }
            var mutationObserver;
            const uiUpdateRunner = (innerScope) => {
                // Pause the observer
                if (mutationObserver) {
                    mutationObserver.disconnect();
                }
                innerScope();
                // Resume the observer
                if (mutationObserver) {
                    filteredObjs.forEach((obj) => {
                        mutationObserver.observe(obj, {
                            childList: true,
                            subtree: true,
                            attributes: true,
                            attributeOldValue: true,
                        });
                    });
                }
            };
            if (MutationObserver) {
                mutationObserver = new MutationObserver(callback);
                filteredObjs.forEach((obj) => {
                    // have the observer observe foo for changes in children
                    mutationObserver.observe(obj, {
                        childList: true,
                        subtree: true,
                        attributes: true,
                        attributeOldValue: true,
                    });
                });
            }
            // browser support fallback
            // @ts-ignore
            else if (window.addEventListener) {
                filteredObjs.forEach((obj) => {
                    obj.addEventListener('DOMNodeInserted', callback, false);
                    obj.addEventListener('DOMNodeRemoved', callback, false);
                });
            }
            return uiUpdateRunner;
        };
    })();
    /**
     * When selecting in normal mode (not meta key), can select one level down, a sibling
     * or a parent of the selected element
     */
    const getSelectableNavNode = (e) => {
        const selectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        const selectedElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
        const elementKeyToNavNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE);
        // Move up the tree until you find the first valid nav node
        let firstNavNode = null;
        let searchNode = e.target;
        while (searchNode && !firstNavNode) {
            firstNavNode =
                elementKeyToNavNode[(0, identifierUtils_1.getElementKeyFromNode)(searchNode) || ''];
            searchNode = searchNode.parentElement;
        }
        if (!firstNavNode) {
            return constantsAndTypes_1.SELECT_OR_HOVER_STORYBOARD;
        }
        const isNavNodeMatch = (navTreeNode) => {
            var _a, _b, _c, _d;
            if (selectedElement.isEmpty()) {
                // This function cannot be called if there is no selected element, see code logic below the function
                throw Error('No selected element when isNavNodeMatch called');
            }
            if (!navTreeNode) {
                return false;
            }
            // If there is no codebase ID it should not be selectable as there is nothing we can do with it
            if (!navTreeNode.tempoElement.codebaseId.startsWith('tempo-') ||
                navTreeNode.tempoElement.codebaseId === navTreeUtils_1.SKIP_ROOT_CODEBASE_ID) {
                return false;
            }
            // If it matches, we already passed all possible children, so re-select it
            if (selectedElement.isEqual(navTreeNode.tempoElement)) {
                return true;
            }
            // Any parent is ok to select
            if (navTreeNode.tempoElement.isParentOf(selectedElement)) {
                return true;
            }
            // Check parents
            // Pick the first parent with a codebase ID
            let parent = navTreeNode.parent;
            while (parent && !parent.tempoElement.codebaseId.startsWith('tempo-')) {
                parent = parent.parent;
            }
            // One level down
            if ((_a = parent === null || parent === void 0 ? void 0 : parent.tempoElement) === null || _a === void 0 ? void 0 : _a.isEqual(selectedElement)) {
                return true;
            }
            // Sibling of any parent
            const selectedNode = elementKeyToNavNode[selectedElement.getKey()];
            if (selectedNode &&
                ((_d = (_c = (_b = navTreeNode.parent) === null || _b === void 0 ? void 0 : _b.children) === null || _c === void 0 ? void 0 : _c.includes) === null || _d === void 0 ? void 0 : _d.call(_c, selectedNode))) {
                return true;
            }
            return false;
        };
        let foundNavNode = null;
        let searchNavNode = firstNavNode;
        while (searchNavNode) {
            if (!selectedElement.isEmpty() && !selectedElement.isStoryboard()) {
                // If there is a selected element key loop from this element up the stack to find the element that is the direct child
                // of the expected selected element, so that you can only hover one level deeper than you've selected
                if (isNavNodeMatch(searchNavNode)) {
                    foundNavNode = searchNavNode;
                    // Exit the loop as we found the node that matches
                    break;
                }
            }
            else {
                // If there is no selected element key, or the selection is the storyboard itself, loop up to the top-most element with a codebase ID
                if (searchNavNode.tempoElement.codebaseId &&
                    searchNavNode.tempoElement.codebaseId.startsWith('tempo-')) {
                    foundNavNode = searchNavNode;
                    // Note: we do not exit the loop here as we want to keep searching for the top-most element
                }
            }
            searchNavNode = searchNavNode.parent;
        }
        return foundNavNode || null;
    };
    const onPointerOver = (e, parentPort, storyboardId, selectBottomMostElement) => {
        const passedThrough = passThroughEventsIfNeeded(e, parentPort, storyboardId);
        const editingTextInfo = (0, editTextUtils_1.getEditingInfo)();
        // Allow on pointer over events if editing (so we can click out)
        if (e.altKey || (passedThrough && !editingTextInfo)) {
            return;
        }
        if ((0, sessionStorageUtils_1.getMemoryStorageItem)('mouseDragContext')) {
            return;
        }
        const currentHoveredKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY);
        const elementKeyToNavNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE) || {};
        let hoveredNavNode;
        if (e.metaKey || e.ctrlKey || selectBottomMostElement) {
            const elementKey = (0, identifierUtils_1.getElementKeyFromNode)(e.target);
            hoveredNavNode = elementKeyToNavNode[elementKey];
            // Special case -> this is the top-most node so it should trigger a hover on the storyboard
            if (!hoveredNavNode && e.target.parentNode === document.body) {
                hoveredNavNode = constantsAndTypes_1.SELECT_OR_HOVER_STORYBOARD;
            }
        }
        else {
            hoveredNavNode = getSelectableNavNode(e);
        }
        const currentSelectedKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        const currentSelectedElement = tempoElement_1.TempoElement.fromKey(currentSelectedKey);
        // If the user is holding shift, only allow selecting siblings
        if (e.shiftKey && hoveredNavNode && currentSelectedKey) {
            // Trying to select the entire storyboard, allow only if the other selected element is also a storyboard
            if (typeof hoveredNavNode === 'string' &&
                !currentSelectedElement.isStoryboard()) {
                hoveredNavNode = null;
            }
            if (typeof hoveredNavNode !== 'string' &&
                !(hoveredNavNode === null || hoveredNavNode === void 0 ? void 0 : hoveredNavNode.tempoElement.isSiblingOf(currentSelectedElement))) {
                hoveredNavNode = null;
            }
        }
        if (!hoveredNavNode) {
            if (currentHoveredKey !== null) {
                (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY, null);
                parentPort.postMessage({
                    id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY,
                    elementKey: null,
                });
                (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
            }
            return;
        }
        if (typeof hoveredNavNode === 'string') {
            if (hoveredNavNode === constantsAndTypes_1.SELECT_OR_HOVER_STORYBOARD) {
                const storyboardKey = tempoElement_1.TempoElement.forStoryboard(storyboardId).getKey();
                if (currentHoveredKey !== storyboardKey) {
                    (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY, storyboardKey);
                    parentPort.postMessage({
                        id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY,
                        elementKey: storyboardKey,
                    });
                    (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
                }
            }
            return;
        }
        const tempoElementKey = hoveredNavNode.tempoElement.getKey();
        if (currentHoveredKey !== tempoElementKey) {
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY,
                elementKey: tempoElementKey,
            });
            (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY, tempoElementKey);
            (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
        }
    };
    const clearHoveredElements = (parentPort, storyboardId) => {
        const currentHoveredKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY);
        if (!currentHoveredKey) {
            return;
        }
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY,
            elementKey: null,
        });
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY, null);
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    const onPointerMove = (e, parentPort, storyboardId) => __awaiter(void 0, void 0, void 0, function* () {
        var _a;
        passThroughEventsIfNeeded(e, parentPort, storyboardId);
        // If no buttons are pressed the drag end event may not have correctly triggered
        // reset the drag state
        let mouseDragData = (0, sessionStorageUtils_1.getMemoryStorageItem)('mouseDragContext');
        if (!e.buttons && mouseDragData) {
            (0, sessionStorageUtils_1.setMemoryStorageItem)('mouseDragContext', null);
            if (mouseDragData === null || mouseDragData === void 0 ? void 0 : mouseDragData.dragging) {
                parentPort.postMessage({
                    id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.DRAG_CANCEL_EVENT,
                    event: {},
                });
            }
            mouseDragData = null;
        }
        const importantFields = {
            pageX: e.pageX,
            pageY: e.pageY,
            clientX: e.clientX,
            clientY: e.clientY,
        };
        (0, sessionStorageUtils_1.setMemoryStorageItem)('mousePos', importantFields);
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.MOUSE_MOVE_EVENT,
            event: importantFields,
        });
        if (mouseDragData && !mouseDragData.dragging) {
            const zoomPerc = (0, sessionStorageUtils_1.getMemoryStorageItem)('zoomPerc') || 1;
            const totalMovementPixels = Math.abs(mouseDragData.pageX - e.pageX) +
                Math.abs(mouseDragData.pageY - e.pageY);
            // Start the drag event if the user has moved enough
            if (totalMovementPixels >= PIXELS_TO_MOVE_BEFORE_DRAG / zoomPerc) {
                // Reselect the parent if there was one to select
                if (mouseDragData.parentSelectedElementKey) {
                    const elementKeyToNavNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE) || {};
                    const navNodeToSelect = elementKeyToNavNode[mouseDragData.parentSelectedElementKey];
                    if (navNodeToSelect) {
                        parentPort.postMessage({
                            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
                            elementKey: mouseDragData.parentSelectedElementKey,
                            outerHTML: (_a = (0, identifierUtils_1.getNodeForElementKey)(mouseDragData.parentSelectedElementKey)) === null || _a === void 0 ? void 0 : _a.outerHTML,
                        });
                        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, mouseDragData.parentSelectedElementKey);
                    }
                }
                const aiContextSelection = (0, sessionStorageUtils_1.getMemoryStorageItem)('aiContext');
                // Don't enable dragging if the AI context is enabled
                if (!aiContextSelection) {
                    (0, sessionStorageUtils_1.setMemoryStorageItem)('mouseDragContext', Object.assign(Object.assign({}, mouseDragData), { dragging: true }));
                    const selectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
                    const selectedElement = (0, identifierUtils_1.getNodeForElementKey)(selectedElementKey);
                    // Trigger the drag start event
                    parentPort.postMessage({
                        id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.DRAG_START_EVENT,
                        event: mouseDragData,
                        outerHTML: selectedElement === null || selectedElement === void 0 ? void 0 : selectedElement.outerHTML,
                    });
                    const bodyObject = (0, jquery_1.default)('body').get(0);
                    // HACK: March 8, 2024
                    // Without this workaround events stay inside the iframe so it's not possible to
                    // track mouse movements outside the iframe when clicking & dragging.
                    // Set the pointer lock and immediately remove it so that
                    // the events start to propagate upwards in the outer application.
                    (0, sessionStorageUtils_1.setMemoryStorageItem)(IMMEDIATELY_REMOVE_POINTER_LOCK, true);
                    yield (bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.requestPointerLock());
                }
            }
        }
        if ((0, sessionStorageUtils_1.getMemoryStorageItem)('mouseDragContext')) {
            (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
        }
    });
    const getParentDomElementForNavNode = (navNode) => {
        if (!navNode) {
            return null;
        }
        if (!(navNode === null || navNode === void 0 ? void 0 : navNode.isComponent)) {
            const childDomElement = (0, identifierUtils_1.getNodeForElementKey)(navNode.tempoElement.getKey());
            return childDomElement === null || childDomElement === void 0 ? void 0 : childDomElement.parentElement;
        }
        // This is the list of real DOM elements that are at the top level of this component
        const elementKeyToLookupList = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_LOOKUP_LIST) || {};
        const lookupList = elementKeyToLookupList[navNode.tempoElement.getKey()] || [];
        let childDomElement;
        lookupList.forEach((lookupElementKey) => {
            if (childDomElement) {
                return;
            }
            childDomElement = (0, identifierUtils_1.getNodeForElementKey)(lookupElementKey);
        });
        return childDomElement === null || childDomElement === void 0 ? void 0 : childDomElement.parentElement;
    };
    const onPointerDown = (e, parentPort, storyboardId) => {
        // This variable determines which button was used
        // 1 -> left, 2 -> middle, 3 -> right
        if (e.which !== 1) {
            return;
        }
        // Allow the edit dynamic text button to be clicked
        if ((0, identifierUtils_1.hasClass)(e.target, identifierUtils_1.EDIT_TEXT_BUTTON)) {
            return;
        }
        const passedThrough = passThroughEventsIfNeeded(e, parentPort, storyboardId);
        if (passedThrough) {
            return;
        }
        const selectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        const selectedElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
        const selectedNavNode = onSelectElement(e, parentPort, storyboardId);
        const useSelectedIfDragging = !selectedElement.isEmpty() &&
            selectedElement.isParentOf(selectedNavNode === null || selectedNavNode === void 0 ? void 0 : selectedNavNode.tempoElement);
        let offsetX, offsetY;
        if (selectedNavNode === null || selectedNavNode === void 0 ? void 0 : selectedNavNode.pageBoundingBox) {
            offsetX =
                selectedNavNode.pageBoundingBox.pageX +
                    selectedNavNode.pageBoundingBox.width / 2 -
                    e.pageX;
            offsetY =
                selectedNavNode.pageBoundingBox.pageY +
                    selectedNavNode.pageBoundingBox.height / 2 -
                    e.pageY;
        }
        const importantFields = {
            pageX: e.pageX,
            pageY: e.pageY,
            // The difference between where the user clicked and the center of the element
            offsetX,
            offsetY,
            // Used to reselect the parent if the user starts to move
            parentSelectedElementKey: useSelectedIfDragging
                ? selectedElementKey
                : null,
        };
        const elementKeyToNavNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE) || {};
        // Get the parent element (actual DOM element) that this node is being dragged inside
        // To do this pick one child element that is being dragged (can be multiple children if the node being dragged is a component),
        // and get its parent in the DOM
        const navNodeToUseForDragging = useSelectedIfDragging
            ? elementKeyToNavNode[selectedElementKey]
            : selectedNavNode;
        const parentDomElement = getParentDomElementForNavNode(navNodeToUseForDragging);
        if (parentDomElement) {
            importantFields['selectedParentDisplay'] = (0, cssFunctions_1.cssEval)(parentDomElement, 'display');
            importantFields['selectedParentFlexDirection'] = (0, cssFunctions_1.cssEval)(parentDomElement, 'flex-direction');
        }
        const aiContextSelection = (0, sessionStorageUtils_1.getMemoryStorageItem)('aiContext');
        // Don't enable dragging if the AI context is enabled
        if (!aiContextSelection) {
            (0, sessionStorageUtils_1.setMemoryStorageItem)('mouseDragContext', importantFields);
        }
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    const onPointerUp = (e, parentPort, storyboardId) => {
        passThroughEventsIfNeeded(e, parentPort, storyboardId);
        const mouseDragData = (0, sessionStorageUtils_1.getMemoryStorageItem)('mouseDragContext');
        (0, sessionStorageUtils_1.setMemoryStorageItem)('mouseDragContext', null);
        if (mouseDragData === null || mouseDragData === void 0 ? void 0 : mouseDragData.dragging) {
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.DRAG_END_EVENT,
                event: {},
            });
        }
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    const onSelectElement = (e, parentPort, storyboardId) => {
        var _a, _b, _c;
        const driveModeEnabled = !!(0, sessionStorageUtils_1.getSessionStorageItem)('driveModeEnabled', storyboardId);
        if (driveModeEnabled) {
            return null;
        }
        const elementKeyToNavNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE) || {};
        let selectedNavNode;
        if (e.metaKey || e.ctrlKey) {
            const elementKey = (0, identifierUtils_1.getElementKeyFromNode)(e.target);
            selectedNavNode = elementKeyToNavNode[elementKey];
            // Special case -> this is the top-most node so it should trigger a select on the storyboard
            if (!selectedNavNode && e.target.parentNode === document.body) {
                selectedNavNode = constantsAndTypes_1.SELECT_OR_HOVER_STORYBOARD;
            }
        }
        else {
            selectedNavNode = getSelectableNavNode(e);
        }
        const currentSelectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        // If this is not a valid nav node, it's not something we track - deselect all
        if (!selectedNavNode) {
            if (currentSelectedElementKey) {
                parentPort.postMessage({
                    id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
                    elementKey: null,
                });
                (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, null);
                (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
            }
            return null;
        }
        const currentSelectedElement = tempoElement_1.TempoElement.fromKey(currentSelectedElementKey);
        const currentMultiSelectedKeys = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS) || [];
        let newSelectedElement = typeof selectedNavNode === 'string'
            ? tempoElement_1.TempoElement.forStoryboard(storyboardId)
            : selectedNavNode.tempoElement;
        let newMultiSelectKeys = [];
        // If the user is holding shift, check if we can multi-select (something has to be already selected)
        // Note: this logic generally matches the logic in the iframe slice on tempo-web
        if (e.shiftKey && currentSelectedElementKey) {
            // First check if we are deselecting
            const elementToDeselect = currentMultiSelectedKeys
                .map((elementKey) => tempoElement_1.TempoElement.fromKey(elementKey))
                .find((element) => {
                return (element.isParentOf(newSelectedElement) ||
                    element.isEqual(newSelectedElement));
            });
            if (elementToDeselect) {
                newMultiSelectKeys = currentMultiSelectedKeys.filter((elementKey) => {
                    return elementKey !== elementToDeselect.getKey();
                });
                // Pick a new element to be the main selected element
                // Note, if the length is 1, there is logic further down to handle that case explicitly (to exit multiselect mode)
                if (elementToDeselect.isEqual(currentSelectedElement) &&
                    newMultiSelectKeys.length > 1) {
                    parentPort.postMessage({
                        id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
                        elementKey: newMultiSelectKeys[0],
                        outerHTML: (_a = (0, identifierUtils_1.getNodeForElementKey)(newMultiSelectKeys[0])) === null || _a === void 0 ? void 0 : _a.outerHTML,
                    });
                    (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, newMultiSelectKeys[0]);
                }
                // Check if we can add this element
            }
            else if (currentSelectedElement.isSiblingOf(newSelectedElement)) {
                if (currentMultiSelectedKeys === null || currentMultiSelectedKeys === void 0 ? void 0 : currentMultiSelectedKeys.length) {
                    newMultiSelectKeys = currentMultiSelectedKeys.concat([
                        newSelectedElement.getKey(),
                    ]);
                }
                else {
                    newMultiSelectKeys = [
                        currentSelectedElementKey,
                        newSelectedElement.getKey(),
                    ];
                }
            }
            else {
                // This case the user is trying to multiselect but it's not something that's allowed, just return but don't make any changes
                return null;
            }
        }
        // In multiselect mode, set the necessary values
        if (newMultiSelectKeys.length > 1) {
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,
                elementKeys: newMultiSelectKeys,
                outerHTMLs: newMultiSelectKeys === null || newMultiSelectKeys === void 0 ? void 0 : newMultiSelectKeys.map((elementKey) => { var _a; return (_a = (0, identifierUtils_1.getNodeForElementKey)(elementKey)) === null || _a === void 0 ? void 0 : _a.outerHTML; }),
            });
            (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS, newMultiSelectKeys);
            (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
            (0, editTextUtils_1.teardownEditableText)(parentPort, storyboardId);
            return null; // Cannot perform regular actions on any particular node
        }
        // Special case - multiselecting but deselecting down to 1, stop the multiselect mode
        if (newMultiSelectKeys.length === 1) {
            newSelectedElement = tempoElement_1.TempoElement.fromKey(newMultiSelectKeys[0]);
        }
        const clearMultiSelectState = () => {
            // Not multi-selecting, so clear the multiselect state
            // Want to do this after setting the selected element to prevent flashing
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,
                elementKeys: [],
                outerHTMLs: [],
            });
            (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS, null);
        };
        // Selecting the storyboard from within
        if (newSelectedElement.isStoryboard()) {
            if (newSelectedElement.getKey() !== currentSelectedElementKey) {
                parentPort.postMessage({
                    id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
                    elementKey: newSelectedElement.getKey(),
                    outerHTML: (_b = (0, identifierUtils_1.getNodeForElementKey)(newSelectedElement.getKey())) === null || _b === void 0 ? void 0 : _b.outerHTML,
                });
                (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, newSelectedElement.getKey());
                (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
            }
            (0, editTextUtils_1.teardownEditableText)(parentPort, storyboardId);
            clearMultiSelectState();
            return null;
        }
        if ((0, editTextUtils_1.currentlyEditing)()) {
            const editingInfo = (0, editTextUtils_1.getEditingInfo)();
            if ((editingInfo === null || editingInfo === void 0 ? void 0 : editingInfo.key) !== currentSelectedElementKey) {
                (0, editTextUtils_1.teardownEditableText)(parentPort, storyboardId);
            }
            clearMultiSelectState();
            return null;
        }
        e.preventDefault();
        e.stopPropagation();
        if ((0, editTextUtils_1.canEditText)(newSelectedElement) &&
            newSelectedElement.getKey() === currentSelectedElementKey) {
            (0, editTextUtils_1.setupEditableText)(newSelectedElement, parentPort, storyboardId);
        }
        if (newSelectedElement.getKey() === currentSelectedElementKey) {
            clearMultiSelectState();
            return selectedNavNode;
        }
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
            elementKey: newSelectedElement.getKey(),
            outerHTML: (_c = (0, identifierUtils_1.getNodeForElementKey)(newSelectedElement.getKey())) === null || _c === void 0 ? void 0 : _c.outerHTML,
        });
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, newSelectedElement.getKey());
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
        clearMultiSelectState();
        return selectedNavNode;
    };
    /**
     * Returns if events were passed through
     */
    const passThroughEventsIfNeeded = (e, parentPort, storyboardId) => {
        var _a, _b;
        const driveModeEnabled = !!(0, sessionStorageUtils_1.getSessionStorageItem)('driveModeEnabled', storyboardId);
        const editingTextInfo = (0, editTextUtils_1.getEditingInfo)();
        if (driveModeEnabled || editingTextInfo) {
            return true;
        }
        (_a = e === null || e === void 0 ? void 0 : e.preventDefault) === null || _a === void 0 ? void 0 : _a.call(e);
        (_b = e === null || e === void 0 ? void 0 : e.stopPropagation) === null || _b === void 0 ? void 0 : _b.call(e);
        return false;
    };
    const onClickElementContextMenu = (e, parentPort, storyboardId) => {
        var _a;
        const passedThrough = passThroughEventsIfNeeded(e, parentPort, storyboardId);
        if (passedThrough) {
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        // Mouse down is called when a user clicks the context menu, but not mouse up, so clear the mouse down
        (0, sessionStorageUtils_1.setMemoryStorageItem)('mouseDragContext', null);
        const elementKeyToNavNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE) || {};
        let requestedNavNode;
        if (e.metaKey || e.ctrlKey) {
            const elementKey = (0, identifierUtils_1.getElementKeyFromNode)(e.target);
            requestedNavNode = elementKeyToNavNode[elementKey];
            // Special case -> this is the top-most node so it should trigger a context menu on the storyboard
            if (!requestedNavNode && e.target.parentNode === document.body) {
                requestedNavNode = constantsAndTypes_1.SELECT_OR_HOVER_STORYBOARD;
            }
        }
        else {
            requestedNavNode = getSelectableNavNode(e);
        }
        const currentSelectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        const currentMultiSelectedKeys = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS);
        if (!requestedNavNode || typeof requestedNavNode === 'string') {
            if (requestedNavNode === constantsAndTypes_1.SELECT_OR_HOVER_STORYBOARD &&
                !(currentMultiSelectedKeys === null || currentMultiSelectedKeys === void 0 ? void 0 : currentMultiSelectedKeys.length)) {
                const storyboardKey = tempoElement_1.TempoElement.forStoryboard(storyboardId).getKey();
                if (currentSelectedElementKey === storyboardKey) {
                    return;
                }
                parentPort.postMessage({
                    id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
                    elementKey: storyboardKey,
                });
                (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, storyboardKey);
                (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
            }
            return;
        }
        let contextRequestedElementKey = null;
        const selectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        const selectedElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
        // Don't select any children as the user might be right clicking a node they selected
        if (!requestedNavNode.tempoElement.isEqual(selectedElement) &&
            !selectedElement.isParentOf(requestedNavNode.tempoElement) &&
            !(currentMultiSelectedKeys === null || currentMultiSelectedKeys === void 0 ? void 0 : currentMultiSelectedKeys.length) // Also don't select anything new if in multiselect mode
        ) {
            contextRequestedElementKey = requestedNavNode.tempoElement.getKey();
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
                elementKey: contextRequestedElementKey,
                outerHTML: (_a = (0, identifierUtils_1.getNodeForElementKey)(contextRequestedElementKey)) === null || _a === void 0 ? void 0 : _a.outerHTML,
            });
            (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, contextRequestedElementKey);
            (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
        }
        const importantFields = {
            clientX: e.clientX,
            clientY: e.clientY,
        };
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.CONTEXT_REQUESTED,
            event: importantFields,
        });
    };
    const buildAndSendNavTree = (parentPort, storyboardId, treeElementLookup, scopeLookup, storyboardComponentElement) => __awaiter(void 0, void 0, void 0, function* () {
        let treeElements = treeElementLookup;
        if (!treeElements) {
            treeElements = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.TREE_ELEMENT_LOOKUP) || {};
        }
        let scopes = scopeLookup;
        if (!scopes) {
            scopes = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SCOPE_LOOKUP) || {};
        }
        let storyboardComponent = storyboardComponentElement;
        if (storyboardComponentElement === 'EXPLICIT_NONE') {
            storyboardComponent = null;
        }
        else if (!storyboardComponent) {
            storyboardComponent = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.STORYBOARD_COMPONENT) || {};
        }
        const knownComponentNames = new Set();
        const knownComponentInstanceNames = new Set();
        if (treeElements) {
            Object.values(treeElements).forEach((treeElement) => {
                if (treeElement.type === 'component' ||
                    treeElement.type === 'storybook-component') {
                    knownComponentNames.add(treeElement.componentName);
                }
                if (treeElement.type === 'component-instance') {
                    knownComponentInstanceNames.add(treeElement.componentName);
                }
            });
        }
        const elementKeyToLookupList = {};
        const elementKeyToNavNode = {};
        const builtNavTree = yield new Promise((resolve, reject) => {
            (0, navTreeUtils_1.buildNavForNodeNonBlocking)({
                storyboardId,
                parent: undefined,
                node: (0, jquery_1.default)('body').get(0),
                uniquePathBase: '',
                uniquePathAddon: 'root',
                scopeLookup: scopes,
                treeElements,
                knownComponentNames,
                knownComponentInstanceNames,
                elementKeyToLookupList,
                elementKeyToNavNode,
                domUniquePath: '0',
            }, (result) => {
                resolve(result);
            });
        });
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_LOOKUP_LIST, elementKeyToLookupList);
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.CURRENT_NAV_TREE, builtNavTree);
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE, elementKeyToNavNode);
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.NAV_TREE,
            navTree: builtNavTree,
            outerHtml: document.documentElement.outerHTML,
        });
        // Run callbacks
        (0, navTreeUtils_1.runNavTreeBuiltCallbacks)();
    });
    const onFlushStart = () => {
        // Find all instant update styling classes to delete
        const classesToDelete = [];
        (0, jquery_1.default)(`*[class*=${identifierUtils_1.TEMPO_INSTANT_UPDATE_STYLING_PREFIX}]`).each((i, element) => {
            const classes = (element.getAttribute('class') || '').split(' ');
            classes.forEach((className) => {
                if (className.startsWith(identifierUtils_1.TEMPO_INSTANT_UPDATE_STYLING_PREFIX)) {
                    classesToDelete.push(className);
                }
            });
        });
        (0, jquery_1.default)(`*[${identifierUtils_1.TEMPO_DELETE_AFTER_REFRESH}=true]`).attr(identifierUtils_1.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD, 'true');
        // Clear the add class instant update queue as those items will be applied in the hot reload
        (0, sessionStorageUtils_1.setMemoryStorageItem)(changeItemFunctions_1.ADD_CLASS_INSTANT_UPDATE_QUEUE, []);
        (0, sessionStorageUtils_1.setMemoryStorageItem)('POST_HOT_RELOAD_CLEAR', {
            classesToDelete,
        });
    };
    const clearInstantUpdatesAndSendNavTree = (parentPort, storyboardId) => __awaiter(void 0, void 0, void 0, function* () {
        globalUIUpdateRunner(() => {
            (0, sessionStorageUtils_1.setMemoryStorageItem)(LAST_NAV_TREE_REFRESH_TIME, new Date());
            const { classesToDelete } = (0, sessionStorageUtils_1.getMemoryStorageItem)('POST_HOT_RELOAD_CLEAR') || {};
            // Delete all instant update changed elements
            (0, jquery_1.default)(`*[${identifierUtils_1.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD}=true]`).remove();
            // Clear the added display nones
            (0, jquery_1.default)(`.${identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS}`).removeClass(identifierUtils_1.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS);
            (0, jquery_1.default)(`*[${identifierUtils_1.TEMPO_INSTANT_UPDATE}=true]`).removeAttr(identifierUtils_1.TEMPO_INSTANT_UPDATE);
            (0, jquery_1.default)(`*[${identifierUtils_1.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH}=true]`).removeAttr(identifierUtils_1.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH);
            (0, jquery_1.default)(`.${changeItemFunctions_1.TEMPORARY_STYLING_CLASS_NAME}`).removeClass(changeItemFunctions_1.TEMPORARY_STYLING_CLASS_NAME);
            // Any classes marked to delete before the hot reload
            classesToDelete === null || classesToDelete === void 0 ? void 0 : classesToDelete.forEach((cls) => {
                (0, jquery_1.default)(`.${cls}`).removeClass(cls);
            });
            const newAddClassQueue = (0, sessionStorageUtils_1.getMemoryStorageItem)(changeItemFunctions_1.ADD_CLASS_INSTANT_UPDATE_QUEUE) || [];
            // Any attributes that start with the styling prefix leftover mean that the class needs to be re-applied
            // these are classes that were added in instant updates while the hot reload was in progress
            newAddClassQueue.forEach((item) => {
                if (!item) {
                    return;
                }
                const { codebaseId, className } = item;
                if (codebaseId && className) {
                    (0, jquery_1.default)(`.${codebaseId}`).attr(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true');
                    (0, jquery_1.default)(`.${codebaseId}`).addClass(className);
                }
            });
        });
        // Rebuild the nav tree on DOM changed after some time has passed
        // this gives the react fiber time to be fully reconciled
        try {
            yield (0, domUtils_1.sleep)(300);
            globalUIUpdateRunner(() => __awaiter(void 0, void 0, void 0, function* () {
                yield buildAndSendNavTree(parentPort, storyboardId);
                (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
            }));
        }
        catch (e) {
            console.error('ERROR: Could not re-create nav tree on DOM change, ' + e);
        }
    });
    const onDOMChanged = ({ mutations, parentPort, storyboardId, fromNextJsLoader, }) => {
        var _a;
        // Udpate the href in the parent container
        if ((0, sessionStorageUtils_1.getMemoryStorageItem)('href') !== window.location.href) {
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.LATEST_HREF,
                href: window.location.href,
            });
            (0, sessionStorageUtils_1.setMemoryStorageItem)('href', window.location.href);
        }
        // Check if we should refresh the nav tree
        let refreshNavTree = false;
        if (fromNextJsLoader) {
            // From the nextjs loader, refresh when the loader gets hidden (means refresh is done)
            const mutationTarget = (_a = mutations === null || mutations === void 0 ? void 0 : mutations[0]) === null || _a === void 0 ? void 0 : _a.target;
            if (mutationTarget && mutationTarget.id === 'container') {
                const currentlyHotReloading = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.HOT_RELOADING);
                if (mutationTarget.classList.contains('visible')) {
                    (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.HOT_RELOADING, true);
                }
                else {
                    (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.HOT_RELOADING, false);
                    refreshNavTree = true;
                }
            }
        }
        else {
            mutations.forEach((e) => {
                if (refreshNavTree) {
                    return;
                }
                // If the class attribute has changed on an element we have to reparse the nav tree to add the element key
                if (e.type === 'attributes' &&
                    e.attributeName === 'class' &&
                    e.target &&
                    !(0, outlineUtils_1.isNodeOutline)(e.target) &&
                    !(0, identifierUtils_1.isMovingElement)(e.target) &&
                    // And not a script
                    // Bug found on Oct 8, 2024, for some reason the script kept triggering a reload
                    !e.target.tagName.toLowerCase().includes('script')) {
                    // An element which doesn't have an element key has changed
                    if (!(0, identifierUtils_1.isElementInSvg)(e.target)) {
                        refreshNavTree = true;
                    }
                    return;
                }
                [e.addedNodes, e.removedNodes].forEach((nodeList) => {
                    if (refreshNavTree) {
                        return;
                    }
                    if (!nodeList) {
                        return;
                    }
                    nodeList.forEach((node) => {
                        if (!(0, outlineUtils_1.isNodeOutline)(node) && !(0, identifierUtils_1.isMovingElement)(node)) {
                            refreshNavTree = true;
                            return;
                        }
                    });
                });
            });
        }
        if (!refreshNavTree) {
            return;
        }
        // In these cases we don't want to trigger a nav tree refresh right away
        // since the hot reload may not have happened yet. So we set a timeout and only
        // trigger a nav tree refresh if another one hasn't happened in between
        if (fromNextJsLoader) {
            const triggerTime = new Date();
            setTimeout(() => {
                const lastRefreshTime = (0, sessionStorageUtils_1.getMemoryStorageItem)(LAST_NAV_TREE_REFRESH_TIME);
                // Don't re-clear and send if another refresh has happened in the meantime
                if (!lastRefreshTime || lastRefreshTime < triggerTime) {
                    clearInstantUpdatesAndSendNavTree(parentPort, storyboardId);
                }
            }, 1000);
            return;
        }
        clearInstantUpdatesAndSendNavTree(parentPort, storyboardId);
    };
    const onWheel = (e, parentPort, storyboardId) => {
        const passedThrough = passThroughEventsIfNeeded(e, parentPort, storyboardId);
        const isScrollShortcut = e.altKey;
        const isZoomShortcut = e.ctrlKey || e.metaKey;
        // If the user wants to scroll (either by being in drive mode, or by holding alt)
        // and they aren't trying to zoom, fallback to default behaviour.
        if (!isZoomShortcut && (passedThrough || isScrollShortcut)) {
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        const importantFields = {
            deltaX: e.deltaX,
            deltaY: e.deltaY,
            wheelDelta: e.wheelDelta,
            x: e.x,
            y: e.y,
            altKey: e.altKey,
            ctrlKey: e.ctrlKey,
            shiftKey: e.shiftKey,
            metaKey: e.metaKey,
        };
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.WHEEL_EVENT,
            event: importantFields,
        });
    };
    const activeElementMetadata = () => {
        const activeElement = document.activeElement;
        let tagName, isContentEditable, elementType;
        if (activeElement) {
            tagName = activeElement.tagName;
            if (activeElement instanceof HTMLElement) {
                isContentEditable = activeElement.isContentEditable;
            }
            if (activeElement instanceof HTMLInputElement) {
                elementType = activeElement.type;
            }
        }
        return {
            tagName: tagName,
            isContentEditable: isContentEditable,
            elementType: elementType,
        };
    };
    const onKeyDown = (e, parentPort) => {
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.KEY_DOWN_EVENT,
            event: {
                key: e.key,
                metaKey: e.metaKey,
                shiftKey: e.shiftKey,
                ctrlKey: e.ctrlKey,
                activeElement: Object.assign({}, activeElementMetadata()),
            },
        });
    };
    const onKeyUp = (e, parentPort) => {
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.KEY_UP_EVENT,
            event: {
                key: e.key,
                metaKey: e.metaKey,
                shiftKey: e.shiftKey,
                ctrlKey: e.ctrlKey,
                activeElement: Object.assign({}, activeElementMetadata()),
            },
        });
    };
    const throttledUpdateOutlines = lodash_1.default.throttle((parentPort, storyboardId) => (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId), 15);
    const onScroll = (e, parentPort, storyboardId) => {
        throttledUpdateOutlines(parentPort, storyboardId);
    };
    // Need to register functions on the window for channel messaging to use them
    // @ts-ignore
    window.initProject = (parentPort, storyboardId, treeElementLookup, scopeLookup, storyboardComponentElement, options = {}, storyboardType, savedComponentFilename, originalStoryboardUrl) => {
        const passive = makePassiveEventOption();
        passive['capture'] = true;
        const body$ = (0, jquery_1.default)('body');
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.TREE_ELEMENT_LOOKUP, treeElementLookup);
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SCOPE_LOOKUP, scopeLookup);
        if (storyboardComponentElement) {
            (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.STORYBOARD_COMPONENT, storyboardComponentElement);
        }
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.STORYBOARD_TYPE, storyboardType);
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SAVED_STORYBOARD_COMPONENT_FILENAME, savedComponentFilename);
        // The URL that was originally loaded for this storyboard, it may be different from href
        // if the user navigated away to a new route
        if (originalStoryboardUrl) {
            (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.ORIGINAL_STORYBOARD_URL, originalStoryboardUrl);
        }
        // Clear iframe outlines
        (0, sessionStorageUtils_1.removeMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        (0, sessionStorageUtils_1.removeMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY);
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
        // Register event listeners
        const bodyObject = body$.get(0);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('click', (e) => {
            passThroughEventsIfNeeded(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('pointerover', (e) => {
            onPointerOver(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('pointerdown', (e) => {
            onPointerDown(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('pointerup', (e) => {
            onPointerUp(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('pointermove', (e) => {
            onPointerMove(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('pointerleave', (e) => {
            passThroughEventsIfNeeded(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('contextmenu', (e) => {
            onClickElementContextMenu(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('dblclick', (e) => {
            passThroughEventsIfNeeded(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('mouseover', (e) => {
            passThroughEventsIfNeeded(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('mouseout', (e) => {
            passThroughEventsIfNeeded(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('mousemove', (e) => {
            passThroughEventsIfNeeded(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('mousedown', (e) => {
            passThroughEventsIfNeeded(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('mouseup', (e) => {
            passThroughEventsIfNeeded(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('wheel', (e) => {
            onWheel(e, parentPort, storyboardId);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('keydown', (e) => {
            onKeyDown(e, parentPort);
        }, passive);
        bodyObject === null || bodyObject === void 0 ? void 0 : bodyObject.addEventListener('keyup', (e) => {
            onKeyUp(e, parentPort);
        }, passive);
        window.addEventListener('scroll', (e) => {
            onScroll(e, parentPort, storyboardId);
        }, passive);
        // Hack: this is used to
        document.addEventListener('pointerlockchange', () => {
            if (document.pointerLockElement &&
                (0, sessionStorageUtils_1.getMemoryStorageItem)(IMMEDIATELY_REMOVE_POINTER_LOCK)) {
                document.exitPointerLock();
                (0, sessionStorageUtils_1.setMemoryStorageItem)(IMMEDIATELY_REMOVE_POINTER_LOCK, false);
            }
        }, false);
        const debounceExecutor = new DebounceExecutor_1.DebounceExecutor();
        const objsToObserve = [bodyObject];
        const nextBuildWatcher = document.getElementById('__next-build-watcher');
        if (nextBuildWatcher && nextBuildWatcher.shadowRoot) {
            // If this is NextJS, also listen to the shadow root of the __next-build-watcher
            // This triggeres the onDOMChanged when the hot reload symbol shows up
            objsToObserve.push(...Array.from(nextBuildWatcher.shadowRoot.children));
        }
        globalUIUpdateRunner = observeDOM(objsToObserve, (e) => {
            debounceExecutor.schedule(() => {
                onDOMChanged({
                    mutations: e,
                    parentPort,
                    storyboardId,
                });
            });
        });
        if (options.driveModeEnabled) {
            enableDriveMode(parentPort, storyboardId);
        }
        else {
            disableDriveMode(parentPort, storyboardId);
        }
        if (options.aiContextSelection) {
            (0, sessionStorageUtils_1.setMemoryStorageItem)('aiContext', true);
            (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
        }
        else {
            (0, sessionStorageUtils_1.setMemoryStorageItem)('aiContext', false);
            (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
        }
        // Build the Nav Tree and send it back
        try {
            globalUIUpdateRunner(() => {
                buildAndSendNavTree(parentPort, storyboardId, treeElementLookup, scopeLookup, storyboardComponentElement || 'EXPLICIT_NONE');
            });
        }
        catch (e) {
            console.log(e);
            console.error('Error building nav tree: ' + e);
        }
    };
    const enableDriveMode = (parentPort, storyboardId) => {
        // @ts-ignore
        if (!(0, sessionStorageUtils_1.getSessionStorageItem)('driveModeEnabled', storyboardId)) {
            // @ts-ignore
            (0, sessionStorageUtils_1.setSessionStorageItem)('driveModeEnabled', 'enabled', storyboardId);
            clearHoveredElements(parentPort, storyboardId);
            (0, outlineUtils_1.clearAllOutlines)();
        }
        (0, jquery_1.default)('body').css('cursor', '');
    };
    const disableDriveMode = (parentPort, storyboardId) => {
        // @ts-ignore
        if ((0, sessionStorageUtils_1.getSessionStorageItem)('driveModeEnabled', storyboardId)) {
            // @ts-ignore
            (0, sessionStorageUtils_1.removeSessionStorageItem)('driveModeEnabled', storyboardId);
            (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
            clearHoveredElements(parentPort, storyboardId);
        }
        (0, jquery_1.default)('body').attr('style', function (i, s) {
            return (s || '') + 'cursor: default !important;';
        });
    };
    // @ts-ignore
    window.enableDriveMode = (parentPort, storyboardId) => {
        enableDriveMode(parentPort, storyboardId);
    };
    // @ts-ignore
    window.disableDriveMode = (parentPort, storyboardId) => {
        disableDriveMode(parentPort, storyboardId);
    };
    // @ts-ignore
    window.setNewLookups = (parentPort, storyboardId, treeElementLookup, scopeLookup) => {
        const prevTreeElemntLookup = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.TREE_ELEMENT_LOOKUP) || {};
        const prevScopeLookup = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SCOPE_LOOKUP) || {};
        const newTreeElements = Object.assign({}, prevTreeElemntLookup);
        // Delete any tree elements that were set to nul
        Object.keys(treeElementLookup).forEach((key) => {
            if (treeElementLookup[key]) {
                newTreeElements[key] = treeElementLookup[key];
            }
            else if (newTreeElements[key]) {
                delete newTreeElements[key];
            }
        });
        const newScopes = Object.assign({}, prevScopeLookup);
        // Delete any scopes that were set to nul
        Object.keys(scopeLookup).forEach((key) => {
            if (scopeLookup[key]) {
                newScopes[key] = scopeLookup[key];
            }
            else if (newScopes[key]) {
                delete newScopes[key];
            }
        });
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.TREE_ELEMENT_LOOKUP, newTreeElements);
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SCOPE_LOOKUP, newScopes);
    };
    // @ts-ignore
    window.setHoveredElement = (parentPort, storyboardId, elementKey) => {
        const driveModeEnabled = !!(0, sessionStorageUtils_1.getSessionStorageItem)('driveModeEnabled', storyboardId);
        if (driveModeEnabled) {
            return;
        }
        const prevHoveredElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY);
        if (prevHoveredElementKey === elementKey) {
            return;
        }
        if (elementKey) {
            (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY, elementKey);
        }
        else {
            (0, sessionStorageUtils_1.removeMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY);
        }
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    // @ts-ignore
    window.setSelectedElement = (parentPort, storyboardId, elementKey) => {
        var _a, _b;
        const prevSelectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        if (prevSelectedElementKey === elementKey) {
            return;
        }
        if (elementKey) {
            const tempoElement = tempoElement_1.TempoElement.fromKey(elementKey);
            let elementKeyToExtract = elementKey;
            if (tempoElement.isStoryboard(storyboardId)) {
                // Pass back the outerHTML of the top level node
                const topLevelNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.CURRENT_NAV_TREE);
                const topLevelElementKey = (_a = topLevelNode === null || topLevelNode === void 0 ? void 0 : topLevelNode.tempoElement) === null || _a === void 0 ? void 0 : _a.getKey();
                if (topLevelElementKey) {
                    elementKeyToExtract = topLevelElementKey;
                }
            }
            // Send back the message just to set the outerHTML only
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
                doNotSetElementKey: true,
                outerHTML: (_b = (0, identifierUtils_1.getNodeForElementKey)(elementKeyToExtract)) === null || _b === void 0 ? void 0 : _b.outerHTML,
            });
            (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, elementKey);
        }
        else {
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
                doNotSetElementKey: true,
                outerHTML: null,
            });
            (0, sessionStorageUtils_1.removeMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        }
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    // @ts-ignore
    window.setMultiselectedElementKeys = (parentPort, storyboardId, elementKeys) => {
        const prevMultiSelectedElementKeys = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS);
        const prevSet = new Set(prevMultiSelectedElementKeys || []);
        const newSet = new Set(elementKeys || []);
        const setsEqual = prevSet.size === newSet.size &&
            [...prevSet].every((value) => newSet.has(value));
        if (setsEqual) {
            return;
        }
        if (elementKeys) {
            (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS, elementKeys);
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,
                doNotSetElementKeys: true,
                outerHTMLs: elementKeys === null || elementKeys === void 0 ? void 0 : elementKeys.map((elementKey) => { var _a; return (_a = (0, identifierUtils_1.getNodeForElementKey)(elementKey)) === null || _a === void 0 ? void 0 : _a.outerHTML; }),
            });
        }
        else {
            (0, sessionStorageUtils_1.removeMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS);
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,
                doNotSetElementKeys: true,
                outerHTMLs: [],
            });
        }
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    // @ts-ignore
    window.processRulesForSelectedElement = (parentPort, storyboardId, cssElementLookup, selectedElementKey) => {
        (0, cssFunctions_1.processRulesForSelectedElement)(parentPort, cssElementLookup, selectedElementKey);
    };
    // @ts-ignore
    window.setModifiersForSelectedElement = (parentPort, storyboardId, modifiers, selectedElementKey) => {
        (0, cssFunctions_1.setModifiersForSelectedElement)(parentPort, modifiers, selectedElementKey);
    };
    // @ts-ignore
    window.getCssEvals = (parentPort, storyboardId, selectedElementKey) => {
        (0, cssFunctions_1.getCssEvals)(parentPort, selectedElementKey);
    };
    // @ts-ignore
    window.ruleMatchesElement = (parentPort, storyboardId, messageId, rule, selectedElementKey) => {
        (0, cssFunctions_1.ruleMatchesElement)(parentPort, messageId, rule, selectedElementKey);
    };
    // @ts-ignore
    window.getElementClassList = (parentPort, storyboardId, selectedElementKey) => {
        (0, cssFunctions_1.getElementClassList)(parentPort, selectedElementKey);
    };
    // @ts-ignore
    window.applyChangeItemToDocument = (parentPort, storyboardId, changeItem) => __awaiter(void 0, void 0, void 0, function* () {
        const { sendNewNavTree } = (0, changeItemFunctions_1.applyChangeItemToDocument)(parentPort, storyboardId, changeItem);
        // Update the nav tree & outlines
        if (sendNewNavTree) {
            yield buildAndSendNavTree(parentPort, storyboardId);
        }
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    });
    // @ts-ignore
    window.updateCodebaseIds = (parentPort, storyboardId, prevIdToNewIdMap, newTreeElementLookup, newScopeLookup) => __awaiter(void 0, void 0, void 0, function* () {
        const sendNewNavTree = (0, changeItemFunctions_1.updateCodebaseIds)(parentPort, prevIdToNewIdMap, true);
        if (sendNewNavTree) {
            yield buildAndSendNavTree(parentPort, storyboardId, newTreeElementLookup, newScopeLookup);
        }
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    });
    // @ts-ignore
    window.dispatchEvent = (parentPort, storyboardId, eventName, eventDetails) => {
        const event = new CustomEvent(eventName, Object.assign({}, eventDetails));
        document.dispatchEvent(event);
    };
    // @ts-ignore
    window.updateOutlines = (parentPort, storyboardId) => {
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    // @ts-ignore
    window.goBack = (parentPort, storyboardId) => {
        if (document.referrer !== '') {
            window.history.back();
        }
    };
    // @ts-ignore
    window.goForward = (parentPort, storyboardId) => {
        window.history.forward();
    };
    // @ts-ignore
    window.refresh = (parentPort, storyboardId) => {
        window.location.reload();
    };
    // @ts-ignore
    window.syntheticMouseOver = (parentPort, storyboardId, coords, dontHoverInsideSelected, selectBottomMostElement) => {
        const target = document.elementFromPoint(coords.x, coords.y);
        // If this is true we don't want to trigger a hover event inside a selected element, instead just set hovering on the selected element
        if (dontHoverInsideSelected) {
            const selectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
            const selectedElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
            if (!selectedElement.isEmpty()) {
                const selectedDomElement = (0, identifierUtils_1.getNodeForElementKey)(selectedElementKey);
                if (selectedDomElement === null || selectedDomElement === void 0 ? void 0 : selectedDomElement.contains(target)) {
                    onPointerOver({ target: selectedDomElement }, parentPort, storyboardId);
                    return;
                }
            }
        }
        onPointerOver({ target }, parentPort, storyboardId, selectBottomMostElement);
    };
    // @ts-ignore
    window.syntheticMouseMove = (parentPort, storyboardId, syntheticEvent) => {
        const eventWithClient = Object.assign(Object.assign({}, syntheticEvent), { pageX: syntheticEvent.clientX +
                (document.documentElement.scrollLeft || document.body.scrollLeft), pageY: syntheticEvent.clientY +
                (document.documentElement.scrollTop || document.body.scrollTop) });
        onPointerMove(eventWithClient, parentPort, storyboardId);
    };
    // @ts-ignore
    window.syntheticMouseUp = (parentPort, storyboardId, syntheticEvent) => {
        onPointerUp(syntheticEvent, parentPort, storyboardId);
    };
    // @ts-ignore
    window.clearHoveredOutlines = (parentPort, storyboardId) => {
        if ((0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY)) {
            clearHoveredElements(parentPort, storyboardId);
        }
    };
    // @ts-ignore
    window.setZoomPerc = (parentPort, storyboardId, zoomPerc) => {
        (0, sessionStorageUtils_1.setMemoryStorageItem)('zoomPerc', zoomPerc.toString());
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    // @ts-ignore
    window.setAiContext = (parentPort, storyboardId, aiContext) => {
        (0, sessionStorageUtils_1.setMemoryStorageItem)('aiContext', !!aiContext);
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    // @ts-ignore
    window.tempMoveElement = (parentPort, storyboardId, nodeToMoveElementKey, newIndex) => __awaiter(void 0, void 0, void 0, function* () {
        var _b, _c, _d, _e, _f;
        const elementKeyToNavNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE) || {};
        const navNodeToMove = elementKeyToNavNode[nodeToMoveElementKey];
        if (!navNodeToMove) {
            return;
        }
        const nodeToMoveElement = tempoElement_1.TempoElement.fromKey(nodeToMoveElementKey);
        const domElementsToMove = [];
        // In components, there may be multiple elements that need to be moved, the eleemntKeyToLookupList
        // are all the real DOM elements in a component
        // For non-components, the eleemntKeyToLookupList points to a list of itself
        const elementKeyToLookupList = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_LOOKUP_LIST) || {};
        const lookupList = elementKeyToLookupList[navNodeToMove.tempoElement.getKey()] || [];
        lookupList.forEach((lookupElementKey) => {
            domElementsToMove.push((0, identifierUtils_1.getNodeForElementKey)(lookupElementKey));
        });
        const parentDomElement = (_b = domElementsToMove[0]) === null || _b === void 0 ? void 0 : _b.parentElement;
        const parentNavNode = navNodeToMove.parent;
        if (parentDomElement && parentNavNode) {
            const currentIndex = (_c = parentNavNode === null || parentNavNode === void 0 ? void 0 : parentNavNode.children) === null || _c === void 0 ? void 0 : _c.indexOf(navNodeToMove);
            const numChildren = (_d = parentNavNode === null || parentNavNode === void 0 ? void 0 : parentNavNode.children) === null || _d === void 0 ? void 0 : _d.length;
            if (currentIndex !== newIndex) {
                Array.from(parentDomElement.children).forEach((child) => {
                    (0, jquery_1.default)(child).attr(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true');
                });
                (0, jquery_1.default)(parentDomElement).attr(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true');
                if (newIndex === numChildren - 1) {
                    domElementsToMove.forEach((element) => {
                        element.parentElement.appendChild(element);
                    });
                }
                else {
                    // If the current index is before the new index then we need to adjust by 1 to account for the shift in indices
                    const beforeNode = currentIndex > newIndex
                        ? parentNavNode === null || parentNavNode === void 0 ? void 0 : parentNavNode.children[newIndex]
                        : parentNavNode === null || parentNavNode === void 0 ? void 0 : parentNavNode.children[newIndex + 1];
                    const lookupListForBefore = elementKeyToLookupList[(_e = beforeNode === null || beforeNode === void 0 ? void 0 : beforeNode.tempoElement) === null || _e === void 0 ? void 0 : _e.getKey()] || [];
                    if (!lookupListForBefore.length) {
                        console.log('Cannot find element to insert before in lookup list');
                        return;
                    }
                    const beforeDomElement = (0, identifierUtils_1.getNodeForElementKey)(lookupListForBefore[0]);
                    if (!beforeDomElement) {
                        console.log('Cannot find element to insert before');
                        return;
                    }
                    domElementsToMove.forEach((element) => {
                        element.parentElement.insertBefore(element, beforeDomElement);
                    });
                }
                // Update the selected element key to the new expected one (note if moving there is no hovered element key)
                // This also assumes the nodeToMoveElementKey is the selected element key
                const elementToMoveSegments = nodeToMoveElement.uniquePath.split('-');
                const newSelectedUniquePath = elementToMoveSegments
                    .slice(0, elementToMoveSegments.length - 1)
                    .join('-') + `-${newIndex}`;
                const newSelectedElementKey = new tempoElement_1.TempoElement(nodeToMoveElement.codebaseId, nodeToMoveElement.storyboardId, newSelectedUniquePath).getKey();
                // Update the nav tree which also sets the element key on all the elements, need to do this before
                // updating the selected element key
                yield buildAndSendNavTree(parentPort, storyboardId);
                // Codebase ID doesn't change
                parentPort.postMessage({
                    id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,
                    elementKey: newSelectedElementKey,
                    outerHTML: (_f = (0, identifierUtils_1.getNodeForElementKey)(newSelectedElementKey)) === null || _f === void 0 ? void 0 : _f.outerHTML,
                });
                (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY, newSelectedElementKey);
                (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
            }
        }
    });
    // @ts-ignore
    window.tempAddDiv = (parentPort, storyboardId, parentCodebaseId, indexInParent, width, height) => __awaiter(void 0, void 0, void 0, function* () {
        const element = (0, jquery_1.default)(`.${identifierUtils_1.TEMPO_INSTANT_DIV_DRAW_CLASS}`);
        if (element.length) {
            element.css('width', width);
            element.css('height', height);
        }
        else {
            let parent = (0, jquery_1.default)(`.${parentCodebaseId}`);
            if (!parent.length) {
                parent = (0, jquery_1.default)('body');
            }
            parent.each((index, item) => {
                const newElement = (0, jquery_1.default)(`<div class="${identifierUtils_1.TEMPO_INSTANT_DIV_DRAW_CLASS}" ${identifierUtils_1.TEMPO_DELETE_AFTER_INSTANT_UPDATE}="true" ${identifierUtils_1.TEMPO_DELETE_AFTER_REFRESH}="true" ${identifierUtils_1.TEMPO_INSTANT_UPDATE}="true"></div>`);
                const childAtIndex = (0, jquery_1.default)(item).children().eq(indexInParent);
                if (childAtIndex === null || childAtIndex === void 0 ? void 0 : childAtIndex.length) {
                    childAtIndex.before(newElement);
                }
                else {
                    (0, jquery_1.default)(item).append(newElement);
                }
            });
            // Update the nav tree
            yield buildAndSendNavTree(parentPort, storyboardId);
        }
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    });
    // @ts-ignore
    window.tempMoveToNewParent = (parentPort, storyboardId, indicatorWidth, indicatorHeight, newPositionX, newPositionY, parentElementKey, clear) => {
        (0, jquery_1.default)(`.${identifierUtils_1.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE}`).remove();
        if (clear) {
            return;
        }
        const newElement = document.createElement('div');
        newElement.classList.add(identifierUtils_1.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE);
        newElement.setAttribute(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true'); // Add so it doesn't trigger new nav tree building
        newElement.style.width = indicatorWidth + 'px';
        newElement.style.height = indicatorHeight + 'px';
        newElement.style.left = newPositionX + 'px';
        newElement.style.top = newPositionY + 'px';
        newElement.style.position = 'fixed';
        newElement.style.pointerEvents = 'none';
        newElement.style.zIndex = '2000000004';
        newElement.style.boxSizing = 'border-box';
        newElement.style.cursor = 'default !important';
        newElement.style.backgroundColor = outlineUtils_1.PRIMARY_OUTLINE_COLOUR;
        const body = document.getElementsByTagName('body')[0];
        body.appendChild(newElement);
        const parentDomElement = (0, identifierUtils_1.getNodeForElementKey)(parentElementKey);
        if (parentDomElement) {
            const boundingRect = parentDomElement.getBoundingClientRect();
            const parentOutline = (0, outlineUtils_1.getOutlineElement)(parentPort, outlineUtils_1.OutlineType.PRIMARY, boundingRect.left, boundingRect.top, boundingRect.width, boundingRect.height);
            parentOutline.classList.remove(identifierUtils_1.OUTLINE_CLASS);
            parentOutline.classList.add(identifierUtils_1.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE);
            parentOutline.setAttribute(identifierUtils_1.TEMPO_INSTANT_UPDATE, 'true'); // Add so it doesn't trigger new nav tree building
            body.appendChild(parentOutline);
        }
    };
    // @ts-ignore
    window.checkIfHydrationError = (parentPort, storyboardId) => {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        let errorDescr, errorLabel, errorBody, hasError;
        if (window.location.href.includes('framework=VITE')) {
            // @ts-ignore
            const errorPortal = (_a = document.getElementsByTagName('vite-error-overlay')[0]) === null || _a === void 0 ? void 0 : _a.shadowRoot;
            errorDescr = 'A Vite Error Occurred';
            errorLabel =
                (_d = (_c = (_b = errorPortal === null || errorPortal === void 0 ? void 0 : errorPortal.querySelectorAll) === null || _b === void 0 ? void 0 : _b.call(errorPortal, '.file-link')) === null || _c === void 0 ? void 0 : _c[0]) === null || _d === void 0 ? void 0 : _d.innerHTML;
            errorBody = (_g = (_f = (_e = errorPortal === null || errorPortal === void 0 ? void 0 : errorPortal.querySelectorAll) === null || _e === void 0 ? void 0 : _e.call(errorPortal, '.message')) === null || _f === void 0 ? void 0 : _f[0]) === null || _g === void 0 ? void 0 : _g.innerHTML;
            hasError = Boolean(errorLabel || errorBody);
        }
        else {
            // @ts-ignore
            const errorPortal = (_h = document.getElementsByTagName('nextjs-portal')[0]) === null || _h === void 0 ? void 0 : _h.shadowRoot;
            errorDescr = (_k = (_j = errorPortal === null || errorPortal === void 0 ? void 0 : errorPortal.getElementById) === null || _j === void 0 ? void 0 : _j.call(errorPortal, 'nextjs__container_errors_desc')) === null || _k === void 0 ? void 0 : _k.innerHTML;
            errorLabel = (_m = (_l = errorPortal === null || errorPortal === void 0 ? void 0 : errorPortal.getElementById) === null || _l === void 0 ? void 0 : _l.call(errorPortal, 'nextjs__container_errors_label')) === null || _m === void 0 ? void 0 : _m.innerHTML;
            errorBody = (_q = (_p = (_o = errorPortal === null || errorPortal === void 0 ? void 0 : errorPortal.querySelectorAll) === null || _o === void 0 ? void 0 : _o.call(errorPortal, '.nextjs-container-errors-body')) === null || _p === void 0 ? void 0 : _p[0]) === null || _q === void 0 ? void 0 : _q.innerHTML;
            hasError = Boolean(errorDescr);
        }
        // Check if the contents of the hydration container contain the text "Hydration failed"
        if (hasError) {
            if (errorDescr === null || errorDescr === void 0 ? void 0 : errorDescr.includes('Hydration failed')) {
                parentPort.postMessage({
                    id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.LATEST_HYDRATION_ERROR_STATUS,
                    status: constantsAndTypes_1.STORYBOARD_HYDRATION_STATUS.ERROR,
                    errorDescr,
                    errorLabel,
                    errorBody,
                });
            }
            else {
                parentPort.postMessage({
                    id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.LATEST_HYDRATION_ERROR_STATUS,
                    status: constantsAndTypes_1.STORYBOARD_HYDRATION_STATUS.OTHER_ERROR,
                    errorDescr,
                    errorLabel,
                    errorBody,
                });
            }
        }
        else {
            parentPort.postMessage({
                id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.LATEST_HYDRATION_ERROR_STATUS,
                status: constantsAndTypes_1.STORYBOARD_HYDRATION_STATUS.NO_ERROR,
            });
        }
    };
    // @ts-ignore
    window.triggerDragStart = (parentPort, storyboardId) => {
        const selectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
        const elementKeyToNavNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE) || {};
        // Something has to be selected to trigger a drag start
        if (!selectedElementKey) {
            return;
        }
        const draggedNavNode = elementKeyToNavNode[selectedElementKey];
        const parentDomElement = getParentDomElementForNavNode(draggedNavNode);
        const selectedElement = (0, identifierUtils_1.getNodeForElementKey)(selectedElementKey);
        const mouseDragContext = {
            // Start off screen, this will get updated by onMouseMove
            pageX: -10000,
            pageY: -10000,
            // The difference between where the user clicked and the center of the element
            offsetX: 0,
            offsetY: 0,
            dragging: true,
            selectedParentDisplay: (0, cssFunctions_1.cssEval)(parentDomElement, 'display'),
            selectedParentFlexDirection: (0, cssFunctions_1.cssEval)(parentDomElement, 'flex-direction'),
        };
        (0, sessionStorageUtils_1.setMemoryStorageItem)('mouseDragContext', mouseDragContext);
        // Trigger the drag start event
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.DRAG_START_EVENT,
            event: mouseDragContext,
            outerHTML: selectedElement === null || selectedElement === void 0 ? void 0 : selectedElement.outerHTML,
        });
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    // @ts-ignore
    window.triggerDragCancel = (parentPort, storyboardId) => {
        (0, sessionStorageUtils_1.setMemoryStorageItem)('mouseDragContext', null);
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.DRAG_CANCEL_EVENT,
            event: {},
        });
        (0, outlineUtils_1.updateOutlines)(parentPort, storyboardId);
    };
    // @ts-ignore
    window.setIsFlushing = (parentPort, storyboardId, isFlushing) => {
        const wasFlushing = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.IS_FLUSHING);
        (0, sessionStorageUtils_1.setMemoryStorageItem)(sessionStorageUtils_1.IS_FLUSHING, isFlushing);
        if (isFlushing && !wasFlushing) {
            onFlushStart();
        }
    };
};
exports.initChannelMessagingFunctions = initChannelMessagingFunctions;
//# sourceMappingURL=data:application/json;base64,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