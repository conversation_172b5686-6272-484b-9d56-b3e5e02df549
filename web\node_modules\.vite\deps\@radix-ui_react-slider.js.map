{"version": 3, "sources": ["../../@radix-ui/react-slider/src/Slider.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createCollection } from '@radix-ui/react-collection';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst PAGE_KEYS = ['PageUp', 'PageDown'];\nconst ARROW_KEYS = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];\n\ntype SlideDirection = 'from-left' | 'from-right' | 'from-bottom' | 'from-top';\nconst BACK_KEYS: Record<SlideDirection, string[]> = {\n  'from-left': ['Home', 'PageDown', 'ArrowDown', 'ArrowLeft'],\n  'from-right': ['Home', 'PageDown', 'ArrowDown', 'ArrowRight'],\n  'from-bottom': ['Home', 'PageDown', 'ArrowDown', 'ArrowLeft'],\n  'from-top': ['Home', 'PageDown', 'ArrowUp', 'ArrowLeft'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Slider\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLIDER_NAME = 'Slider';\n\nconst [Collection, useCollection, createCollectionScope] =\n  createCollection<SliderThumbElement>(SLIDER_NAME);\n\ntype ScopedProps<P> = P & { __scopeSlider?: Scope };\nconst [createSliderContext, createSliderScope] = createContextScope(SLIDER_NAME, [\n  createCollectionScope,\n]);\n\ntype SliderContextValue = {\n  name?: string;\n  disabled?: boolean;\n  min: number;\n  max: number;\n  values: number[];\n  valueIndexToChangeRef: React.MutableRefObject<number>;\n  thumbs: Set<SliderThumbElement>;\n  orientation: SliderProps['orientation'];\n};\n\nconst [SliderProvider, useSliderContext] = createSliderContext<SliderContextValue>(SLIDER_NAME);\n\ntype SliderElement = SliderHorizontalElement | SliderVerticalElement;\ninterface SliderProps\n  extends Omit<\n    SliderHorizontalProps | SliderVerticalProps,\n    keyof SliderOrientationPrivateProps | 'defaultValue'\n  > {\n  name?: string;\n  disabled?: boolean;\n  orientation?: React.AriaAttributes['aria-orientation'];\n  dir?: Direction;\n  min?: number;\n  max?: number;\n  step?: number;\n  minStepsBetweenThumbs?: number;\n  value?: number[];\n  defaultValue?: number[];\n  onValueChange?(value: number[]): void;\n  onValueCommit?(value: number[]): void;\n  inverted?: boolean;\n}\n\nconst Slider = React.forwardRef<SliderElement, SliderProps>(\n  (props: ScopedProps<SliderProps>, forwardedRef) => {\n    const {\n      name,\n      min = 0,\n      max = 100,\n      step = 1,\n      orientation = 'horizontal',\n      disabled = false,\n      minStepsBetweenThumbs = 0,\n      defaultValue = [min],\n      value,\n      onValueChange = () => {},\n      onValueCommit = () => {},\n      inverted = false,\n      ...sliderProps\n    } = props;\n    const thumbRefs = React.useRef<SliderContextValue['thumbs']>(new Set());\n    const valueIndexToChangeRef = React.useRef<number>(0);\n    const isHorizontal = orientation === 'horizontal';\n    const SliderOrientation = isHorizontal ? SliderHorizontal : SliderVertical;\n\n    const [values = [], setValues] = useControllableState({\n      prop: value,\n      defaultProp: defaultValue,\n      onChange: (value) => {\n        const thumbs = [...thumbRefs.current];\n        thumbs[valueIndexToChangeRef.current]?.focus();\n        onValueChange(value);\n      },\n    });\n    const valuesBeforeSlideStartRef = React.useRef(values);\n\n    function handleSlideStart(value: number) {\n      const closestIndex = getClosestValueIndex(values, value);\n      updateValues(value, closestIndex);\n    }\n\n    function handleSlideMove(value: number) {\n      updateValues(value, valueIndexToChangeRef.current);\n    }\n\n    function handleSlideEnd() {\n      const prevValue = valuesBeforeSlideStartRef.current[valueIndexToChangeRef.current];\n      const nextValue = values[valueIndexToChangeRef.current];\n      const hasChanged = nextValue !== prevValue;\n      if (hasChanged) onValueCommit(values);\n    }\n\n    function updateValues(value: number, atIndex: number, { commit } = { commit: false }) {\n      const decimalCount = getDecimalCount(step);\n      const snapToStep = roundValue(Math.round((value - min) / step) * step + min, decimalCount);\n      const nextValue = clamp(snapToStep, [min, max]);\n\n      setValues((prevValues = []) => {\n        const nextValues = getNextSortedValues(prevValues, nextValue, atIndex);\n        if (hasMinStepsBetweenValues(nextValues, minStepsBetweenThumbs * step)) {\n          valueIndexToChangeRef.current = nextValues.indexOf(nextValue);\n          const hasChanged = String(nextValues) !== String(prevValues);\n          if (hasChanged && commit) onValueCommit(nextValues);\n          return hasChanged ? nextValues : prevValues;\n        } else {\n          return prevValues;\n        }\n      });\n    }\n\n    return (\n      <SliderProvider\n        scope={props.__scopeSlider}\n        name={name}\n        disabled={disabled}\n        min={min}\n        max={max}\n        valueIndexToChangeRef={valueIndexToChangeRef}\n        thumbs={thumbRefs.current}\n        values={values}\n        orientation={orientation}\n      >\n        <Collection.Provider scope={props.__scopeSlider}>\n          <Collection.Slot scope={props.__scopeSlider}>\n            <SliderOrientation\n              aria-disabled={disabled}\n              data-disabled={disabled ? '' : undefined}\n              {...sliderProps}\n              ref={forwardedRef}\n              onPointerDown={composeEventHandlers(sliderProps.onPointerDown, () => {\n                if (!disabled) valuesBeforeSlideStartRef.current = values;\n              })}\n              min={min}\n              max={max}\n              inverted={inverted}\n              onSlideStart={disabled ? undefined : handleSlideStart}\n              onSlideMove={disabled ? undefined : handleSlideMove}\n              onSlideEnd={disabled ? undefined : handleSlideEnd}\n              onHomeKeyDown={() => !disabled && updateValues(min, 0, { commit: true })}\n              onEndKeyDown={() =>\n                !disabled && updateValues(max, values.length - 1, { commit: true })\n              }\n              onStepKeyDown={({ event, direction: stepDirection }) => {\n                if (!disabled) {\n                  const isPageKey = PAGE_KEYS.includes(event.key);\n                  const isSkipKey = isPageKey || (event.shiftKey && ARROW_KEYS.includes(event.key));\n                  const multiplier = isSkipKey ? 10 : 1;\n                  const atIndex = valueIndexToChangeRef.current;\n                  const value = values[atIndex];\n                  const stepInDirection = step * multiplier * stepDirection;\n                  updateValues(value + stepInDirection, atIndex, { commit: true });\n                }\n              }}\n            />\n          </Collection.Slot>\n        </Collection.Provider>\n      </SliderProvider>\n    );\n  }\n);\n\nSlider.displayName = SLIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderHorizontal\n * -----------------------------------------------------------------------------------------------*/\n\ntype Side = 'top' | 'right' | 'bottom' | 'left';\n\nconst [SliderOrientationProvider, useSliderOrientationContext] = createSliderContext<{\n  startEdge: Side;\n  endEdge: Side;\n  size: keyof NonNullable<ReturnType<typeof useSize>>;\n  direction: number;\n}>(SLIDER_NAME, {\n  startEdge: 'left',\n  endEdge: 'right',\n  size: 'width',\n  direction: 1,\n});\n\ntype SliderOrientationPrivateProps = {\n  min: number;\n  max: number;\n  inverted: boolean;\n  onSlideStart?(value: number): void;\n  onSlideMove?(value: number): void;\n  onSlideEnd?(): void;\n  onHomeKeyDown(event: React.KeyboardEvent): void;\n  onEndKeyDown(event: React.KeyboardEvent): void;\n  onStepKeyDown(step: { event: React.KeyboardEvent; direction: number }): void;\n};\ninterface SliderOrientationProps\n  extends Omit<SliderImplProps, keyof SliderImplPrivateProps>,\n    SliderOrientationPrivateProps {}\n\ntype SliderHorizontalElement = SliderImplElement;\ninterface SliderHorizontalProps extends SliderOrientationProps {\n  dir?: Direction;\n}\n\nconst SliderHorizontal = React.forwardRef<SliderHorizontalElement, SliderHorizontalProps>(\n  (props: ScopedProps<SliderHorizontalProps>, forwardedRef) => {\n    const {\n      min,\n      max,\n      dir,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const [slider, setSlider] = React.useState<SliderImplElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setSlider(node));\n    const rectRef = React.useRef<ClientRect>();\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === 'ltr';\n    const isSlidingFromLeft = (isDirectionLTR && !inverted) || (!isDirectionLTR && inverted);\n\n    function getValueFromPointer(pointerPosition: number) {\n      const rect = rectRef.current || slider!.getBoundingClientRect();\n      const input: [number, number] = [0, rect.width];\n      const output: [number, number] = isSlidingFromLeft ? [min, max] : [max, min];\n      const value = linearScale(input, output);\n\n      rectRef.current = rect;\n      return value(pointerPosition - rect.left);\n    }\n\n    return (\n      <SliderOrientationProvider\n        scope={props.__scopeSlider}\n        startEdge={isSlidingFromLeft ? 'left' : 'right'}\n        endEdge={isSlidingFromLeft ? 'right' : 'left'}\n        direction={isSlidingFromLeft ? 1 : -1}\n        size=\"width\"\n      >\n        <SliderImpl\n          dir={direction}\n          data-orientation=\"horizontal\"\n          {...sliderProps}\n          ref={composedRefs}\n          style={{\n            ...sliderProps.style,\n            ['--radix-slider-thumb-transform' as any]: 'translateX(-50%)',\n          }}\n          onSlideStart={(event) => {\n            const value = getValueFromPointer(event.clientX);\n            onSlideStart?.(value);\n          }}\n          onSlideMove={(event) => {\n            const value = getValueFromPointer(event.clientX);\n            onSlideMove?.(value);\n          }}\n          onSlideEnd={() => {\n            rectRef.current = undefined;\n            onSlideEnd?.();\n          }}\n          onStepKeyDown={(event) => {\n            const slideDirection = isSlidingFromLeft ? 'from-left' : 'from-right';\n            const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n            onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n          }}\n        />\n      </SliderOrientationProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * SliderVertical\n * -----------------------------------------------------------------------------------------------*/\n\ntype SliderVerticalElement = SliderImplElement;\ninterface SliderVerticalProps extends SliderOrientationProps {}\n\nconst SliderVertical = React.forwardRef<SliderVerticalElement, SliderVerticalProps>(\n  (props: ScopedProps<SliderVerticalProps>, forwardedRef) => {\n    const {\n      min,\n      max,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const sliderRef = React.useRef<SliderImplElement>(null);\n    const ref = useComposedRefs(forwardedRef, sliderRef);\n    const rectRef = React.useRef<ClientRect>();\n    const isSlidingFromBottom = !inverted;\n\n    function getValueFromPointer(pointerPosition: number) {\n      const rect = rectRef.current || sliderRef.current!.getBoundingClientRect();\n      const input: [number, number] = [0, rect.height];\n      const output: [number, number] = isSlidingFromBottom ? [max, min] : [min, max];\n      const value = linearScale(input, output);\n\n      rectRef.current = rect;\n      return value(pointerPosition - rect.top);\n    }\n\n    return (\n      <SliderOrientationProvider\n        scope={props.__scopeSlider}\n        startEdge={isSlidingFromBottom ? 'bottom' : 'top'}\n        endEdge={isSlidingFromBottom ? 'top' : 'bottom'}\n        size=\"height\"\n        direction={isSlidingFromBottom ? 1 : -1}\n      >\n        <SliderImpl\n          data-orientation=\"vertical\"\n          {...sliderProps}\n          ref={ref}\n          style={{\n            ...sliderProps.style,\n            ['--radix-slider-thumb-transform' as any]: 'translateY(50%)',\n          }}\n          onSlideStart={(event) => {\n            const value = getValueFromPointer(event.clientY);\n            onSlideStart?.(value);\n          }}\n          onSlideMove={(event) => {\n            const value = getValueFromPointer(event.clientY);\n            onSlideMove?.(value);\n          }}\n          onSlideEnd={() => {\n            rectRef.current = undefined;\n            onSlideEnd?.();\n          }}\n          onStepKeyDown={(event) => {\n            const slideDirection = isSlidingFromBottom ? 'from-bottom' : 'from-top';\n            const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n            onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n          }}\n        />\n      </SliderOrientationProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * SliderImpl\n * -----------------------------------------------------------------------------------------------*/\n\ntype SliderImplElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ntype SliderImplPrivateProps = {\n  onSlideStart(event: React.PointerEvent): void;\n  onSlideMove(event: React.PointerEvent): void;\n  onSlideEnd(event: React.PointerEvent): void;\n  onHomeKeyDown(event: React.KeyboardEvent): void;\n  onEndKeyDown(event: React.KeyboardEvent): void;\n  onStepKeyDown(event: React.KeyboardEvent): void;\n};\ninterface SliderImplProps extends PrimitiveDivProps, SliderImplPrivateProps {}\n\nconst SliderImpl = React.forwardRef<SliderImplElement, SliderImplProps>(\n  (props: ScopedProps<SliderImplProps>, forwardedRef) => {\n    const {\n      __scopeSlider,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onHomeKeyDown,\n      onEndKeyDown,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const context = useSliderContext(SLIDER_NAME, __scopeSlider);\n\n    return (\n      <Primitive.span\n        {...sliderProps}\n        ref={forwardedRef}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          if (event.key === 'Home') {\n            onHomeKeyDown(event);\n            // Prevent scrolling to page start\n            event.preventDefault();\n          } else if (event.key === 'End') {\n            onEndKeyDown(event);\n            // Prevent scrolling to page end\n            event.preventDefault();\n          } else if (PAGE_KEYS.concat(ARROW_KEYS).includes(event.key)) {\n            onStepKeyDown(event);\n            // Prevent scrolling for directional key presses\n            event.preventDefault();\n          }\n        })}\n        onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n          const target = event.target as HTMLElement;\n          target.setPointerCapture(event.pointerId);\n          // Prevent browser focus behaviour because we focus a thumb manually when values change.\n          event.preventDefault();\n          // Touch devices have a delay before focusing so won't focus if touch immediately moves\n          // away from target (sliding). We want thumb to focus regardless.\n          if (context.thumbs.has(target)) {\n            target.focus();\n          } else {\n            onSlideStart(event);\n          }\n        })}\n        onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n          const target = event.target as HTMLElement;\n          if (target.hasPointerCapture(event.pointerId)) onSlideMove(event);\n        })}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          const target = event.target as HTMLElement;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n            onSlideEnd(event);\n          }\n        })}\n      />\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * SliderTrack\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRACK_NAME = 'SliderTrack';\n\ntype SliderTrackElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SliderTrackProps extends PrimitiveSpanProps {}\n\nconst SliderTrack = React.forwardRef<SliderTrackElement, SliderTrackProps>(\n  (props: ScopedProps<SliderTrackProps>, forwardedRef) => {\n    const { __scopeSlider, ...trackProps } = props;\n    const context = useSliderContext(TRACK_NAME, __scopeSlider);\n    return (\n      <Primitive.span\n        data-disabled={context.disabled ? '' : undefined}\n        data-orientation={context.orientation}\n        {...trackProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nSliderTrack.displayName = TRACK_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderRange\n * -----------------------------------------------------------------------------------------------*/\n\nconst RANGE_NAME = 'SliderRange';\n\ntype SliderRangeElement = React.ElementRef<typeof Primitive.span>;\ninterface SliderRangeProps extends PrimitiveSpanProps {}\n\nconst SliderRange = React.forwardRef<SliderRangeElement, SliderRangeProps>(\n  (props: ScopedProps<SliderRangeProps>, forwardedRef) => {\n    const { __scopeSlider, ...rangeProps } = props;\n    const context = useSliderContext(RANGE_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(RANGE_NAME, __scopeSlider);\n    const ref = React.useRef<HTMLSpanElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const valuesCount = context.values.length;\n    const percentages = context.values.map((value) =>\n      convertValueToPercentage(value, context.min, context.max)\n    );\n    const offsetStart = valuesCount > 1 ? Math.min(...percentages) : 0;\n    const offsetEnd = 100 - Math.max(...percentages);\n\n    return (\n      <Primitive.span\n        data-orientation={context.orientation}\n        data-disabled={context.disabled ? '' : undefined}\n        {...rangeProps}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          [orientation.startEdge]: offsetStart + '%',\n          [orientation.endEdge]: offsetEnd + '%',\n        }}\n      />\n    );\n  }\n);\n\nSliderRange.displayName = RANGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'SliderThumb';\n\ntype SliderThumbElement = SliderThumbImplElement;\ninterface SliderThumbProps extends Omit<SliderThumbImplProps, 'index'> {}\n\nconst SliderThumb = React.forwardRef<SliderThumbElement, SliderThumbProps>(\n  (props: ScopedProps<SliderThumbProps>, forwardedRef) => {\n    const getItems = useCollection(props.__scopeSlider);\n    const [thumb, setThumb] = React.useState<SliderThumbImplElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    const index = React.useMemo(\n      () => (thumb ? getItems().findIndex((item) => item.ref.current === thumb) : -1),\n      [getItems, thumb]\n    );\n    return <SliderThumbImpl {...props} ref={composedRefs} index={index} />;\n  }\n);\n\ntype SliderThumbImplElement = React.ElementRef<typeof Primitive.span>;\ninterface SliderThumbImplProps extends PrimitiveSpanProps {\n  index: number;\n  name?: string;\n}\n\nconst SliderThumbImpl = React.forwardRef<SliderThumbImplElement, SliderThumbImplProps>(\n  (props: ScopedProps<SliderThumbImplProps>, forwardedRef) => {\n    const { __scopeSlider, index, name, ...thumbProps } = props;\n    const context = useSliderContext(THUMB_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(THUMB_NAME, __scopeSlider);\n    const [thumb, setThumb] = React.useState<HTMLSpanElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = thumb ? Boolean(thumb.closest('form')) : true;\n    const size = useSize(thumb);\n    // We cast because index could be `-1` which would return undefined\n    const value = context.values[index] as number | undefined;\n    const percent =\n      value === undefined ? 0 : convertValueToPercentage(value, context.min, context.max);\n    const label = getLabel(index, context.values.length);\n    const orientationSize = size?.[orientation.size];\n    const thumbInBoundsOffset = orientationSize\n      ? getThumbInBoundsOffset(orientationSize, percent, orientation.direction)\n      : 0;\n\n    React.useEffect(() => {\n      if (thumb) {\n        context.thumbs.add(thumb);\n        return () => {\n          context.thumbs.delete(thumb);\n        };\n      }\n    }, [thumb, context.thumbs]);\n\n    return (\n      <span\n        style={{\n          transform: 'var(--radix-slider-thumb-transform)',\n          position: 'absolute',\n          [orientation.startEdge]: `calc(${percent}% + ${thumbInBoundsOffset}px)`,\n        }}\n      >\n        <Collection.ItemSlot scope={props.__scopeSlider}>\n          <Primitive.span\n            role=\"slider\"\n            aria-label={props['aria-label'] || label}\n            aria-valuemin={context.min}\n            aria-valuenow={value}\n            aria-valuemax={context.max}\n            aria-orientation={context.orientation}\n            data-orientation={context.orientation}\n            data-disabled={context.disabled ? '' : undefined}\n            tabIndex={context.disabled ? undefined : 0}\n            {...thumbProps}\n            ref={composedRefs}\n            /**\n             * There will be no value on initial render while we work out the index so we hide thumbs\n             * without a value, otherwise SSR will render them in the wrong position before they\n             * snap into the correct position during hydration which would be visually jarring for\n             * slower connections.\n             */\n            style={value === undefined ? { display: 'none' } : props.style}\n            onFocus={composeEventHandlers(props.onFocus, () => {\n              context.valueIndexToChangeRef.current = index;\n            })}\n          />\n        </Collection.ItemSlot>\n\n        {isFormControl && (\n          <BubbleInput\n            key={index}\n            name={\n              name ??\n              (context.name ? context.name + (context.values.length > 1 ? '[]' : '') : undefined)\n            }\n            value={value}\n          />\n        )}\n      </span>\n    );\n  }\n);\n\nSliderThumb.displayName = THUMB_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst BubbleInput = (props: React.ComponentPropsWithoutRef<'input'>) => {\n  const { value, ...inputProps } = props;\n  const ref = React.useRef<HTMLInputElement>(null);\n  const prevValue = usePrevious(value);\n\n  // Bubble value change to parents (e.g form change event)\n  React.useEffect(() => {\n    const input = ref.current!;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'value') as PropertyDescriptor;\n    const setValue = descriptor.set;\n    if (prevValue !== value && setValue) {\n      const event = new Event('input', { bubbles: true });\n      setValue.call(input, value);\n      input.dispatchEvent(event);\n    }\n  }, [prevValue, value]);\n\n  /**\n   * We purposefully do not use `type=\"hidden\"` here otherwise forms that\n   * wrap it will not be able to access its value via the FormData API.\n   *\n   * We purposefully do not add the `value` attribute here to allow the value\n   * to be set programatically and bubble to any parent form `onChange` event.\n   * Adding the `value` will cause React to consider the programatic\n   * dispatch a duplicate and it will get swallowed.\n   */\n  return <input style={{ display: 'none' }} {...inputProps} ref={ref} defaultValue={value} />;\n};\n\nfunction getNextSortedValues(prevValues: number[] = [], nextValue: number, atIndex: number) {\n  const nextValues = [...prevValues];\n  nextValues[atIndex] = nextValue;\n  return nextValues.sort((a, b) => a - b);\n}\n\nfunction convertValueToPercentage(value: number, min: number, max: number) {\n  const maxSteps = max - min;\n  const percentPerStep = 100 / maxSteps;\n  const percentage = percentPerStep * (value - min);\n  return clamp(percentage, [0, 100]);\n}\n\n/**\n * Returns a label for each thumb when there are two or more thumbs\n */\nfunction getLabel(index: number, totalValues: number) {\n  if (totalValues > 2) {\n    return `Value ${index + 1} of ${totalValues}`;\n  } else if (totalValues === 2) {\n    return ['Minimum', 'Maximum'][index];\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Given a `values` array and a `nextValue`, determine which value in\n * the array is closest to `nextValue` and return its index.\n *\n * @example\n * // returns 1\n * getClosestValueIndex([10, 30], 25);\n */\nfunction getClosestValueIndex(values: number[], nextValue: number) {\n  if (values.length === 1) return 0;\n  const distances = values.map((value) => Math.abs(value - nextValue));\n  const closestDistance = Math.min(...distances);\n  return distances.indexOf(closestDistance);\n}\n\n/**\n * Offsets the thumb centre point while sliding to ensure it remains\n * within the bounds of the slider when reaching the edges\n */\nfunction getThumbInBoundsOffset(width: number, left: number, direction: number) {\n  const halfWidth = width / 2;\n  const halfPercent = 50;\n  const offset = linearScale([0, halfPercent], [0, halfWidth]);\n  return (halfWidth - offset(left) * direction) * direction;\n}\n\n/**\n * Gets an array of steps between each value.\n *\n * @example\n * // returns [1, 9]\n * getStepsBetweenValues([10, 11, 20]);\n */\nfunction getStepsBetweenValues(values: number[]) {\n  return values.slice(0, -1).map((value, index) => values[index + 1] - value);\n}\n\n/**\n * Verifies the minimum steps between all values is greater than or equal\n * to the expected minimum steps.\n *\n * @example\n * // returns false\n * hasMinStepsBetweenValues([1,2,3], 2);\n *\n * @example\n * // returns true\n * hasMinStepsBetweenValues([1,2,3], 1);\n */\nfunction hasMinStepsBetweenValues(values: number[], minStepsBetweenValues: number) {\n  if (minStepsBetweenValues > 0) {\n    const stepsBetweenValues = getStepsBetweenValues(values);\n    const actualMinStepsBetweenValues = Math.min(...stepsBetweenValues);\n    return actualMinStepsBetweenValues >= minStepsBetweenValues;\n  }\n  return true;\n}\n\n// https://github.com/tmcw-up-for-adoption/simple-linear-scale/blob/master/index.js\nfunction linearScale(input: readonly [number, number], output: readonly [number, number]) {\n  return (value: number) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\n\nfunction getDecimalCount(value: number) {\n  return (String(value).split('.')[1] || '').length;\n}\n\nfunction roundValue(value: number, decimalCount: number) {\n  const rounder = Math.pow(10, decimalCount);\n  return Math.round(value * rounder) / rounder;\n}\n\nconst Root = Slider;\nconst Track = SliderTrack;\nconst Range = SliderRange;\nconst Thumb = SliderThumb;\n\nexport {\n  createSliderScope,\n  //\n  Slider,\n  SliderTrack,\n  SliderRange,\n  SliderThumb,\n  //\n  Root,\n  Track,\n  Range,\n  Thumb,\n};\nexport type { SliderProps, SliderTrackProps, SliderRangeProps, SliderThumbProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AA4JX,yBAAA;AA5IZ,IAAM,YAAY,CAAC,UAAU,UAAU;AACvC,IAAM,aAAa,CAAC,WAAW,aAAa,aAAa,YAAY;AAGrE,IAAM,YAA8C;EAClD,aAAa,CAAC,QAAQ,YAAY,aAAa,WAAW;EAC1D,cAAc,CAAC,QAAQ,YAAY,aAAa,YAAY;EAC5D,eAAe,CAAC,QAAQ,YAAY,aAAa,WAAW;EAC5D,YAAY,CAAC,QAAQ,YAAY,WAAW,WAAW;AACzD;AAMA,IAAM,cAAc;AAEpB,IAAM,CAAC,YAAY,eAAe,qBAAqB,IACrD,iBAAqC,WAAW;AAGlD,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,aAAa;EAC/E;AACF,CAAC;AAaD,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAuB9F,IAAM,SAAe;EACnB,CAAC,OAAiC,iBAAiB;AACjD,UAAM;MACJ;MACA,MAAM;MACN,MAAM;MACN,OAAO;MACP,cAAc;MACd,WAAW;MACX,wBAAwB;MACxB,eAAe,CAAC,GAAG;MACnB;MACA,gBAAgB,MAAM;MAAC;MACvB,gBAAgB,MAAM;MAAC;MACvB,WAAW;MACX,GAAG;IACL,IAAI;AACJ,UAAM,YAAkB,aAAqC,oBAAI,IAAI,CAAC;AACtE,UAAM,wBAA8B,aAAe,CAAC;AACpD,UAAM,eAAe,gBAAgB;AACrC,UAAM,oBAAoB,eAAe,mBAAmB;AAE5D,UAAM,CAAC,SAAS,CAAC,GAAG,SAAS,IAAI,qBAAqB;MACpD,MAAM;MACN,aAAa;MACb,UAAU,CAACA,WAAU;;AACnB,cAAM,SAAS,CAAC,GAAG,UAAU,OAAO;AACpC,qBAAO,sBAAsB,OAAO,MAApC,mBAAuC;AACvC,sBAAcA,MAAK;MACrB;IACF,CAAC;AACD,UAAM,4BAAkC,aAAO,MAAM;AAErD,aAAS,iBAAiBA,QAAe;AACvC,YAAM,eAAe,qBAAqB,QAAQA,MAAK;AACvD,mBAAaA,QAAO,YAAY;IAClC;AAEA,aAAS,gBAAgBA,QAAe;AACtC,mBAAaA,QAAO,sBAAsB,OAAO;IACnD;AAEA,aAAS,iBAAiB;AACxB,YAAM,YAAY,0BAA0B,QAAQ,sBAAsB,OAAO;AACjF,YAAM,YAAY,OAAO,sBAAsB,OAAO;AACtD,YAAM,aAAa,cAAc;AACjC,UAAI,WAAY,eAAc,MAAM;IACtC;AAEA,aAAS,aAAaA,QAAe,SAAiB,EAAE,OAAO,IAAI,EAAE,QAAQ,MAAM,GAAG;AACpF,YAAM,eAAe,gBAAgB,IAAI;AACzC,YAAM,aAAa,WAAW,KAAK,OAAOA,SAAQ,OAAO,IAAI,IAAI,OAAO,KAAK,YAAY;AACzF,YAAM,YAAY,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC;AAE9C,gBAAU,CAAC,aAAa,CAAC,MAAM;AAC7B,cAAM,aAAa,oBAAoB,YAAY,WAAW,OAAO;AACrE,YAAI,yBAAyB,YAAY,wBAAwB,IAAI,GAAG;AACtE,gCAAsB,UAAU,WAAW,QAAQ,SAAS;AAC5D,gBAAM,aAAa,OAAO,UAAU,MAAM,OAAO,UAAU;AAC3D,cAAI,cAAc,OAAQ,eAAc,UAAU;AAClD,iBAAO,aAAa,aAAa;QACnC,OAAO;AACL,iBAAO;QACT;MACF,CAAC;IACH;AAEA,eACE;MAAC;MAAA;QACC,OAAO,MAAM;QACb;QACA;QACA;QACA;QACA;QACA,QAAQ,UAAU;QAClB;QACA;QAEA,cAAA,wBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,eAChC,cAAA,wBAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,eAC5B,cAAA;UAAC;UAAA;YACC,iBAAe;YACf,iBAAe,WAAW,KAAK;YAC9B,GAAG;YACJ,KAAK;YACL,eAAe,qBAAqB,YAAY,eAAe,MAAM;AACnE,kBAAI,CAAC,SAAU,2BAA0B,UAAU;YACrD,CAAC;YACD;YACA;YACA;YACA,cAAc,WAAW,SAAY;YACrC,aAAa,WAAW,SAAY;YACpC,YAAY,WAAW,SAAY;YACnC,eAAe,MAAM,CAAC,YAAY,aAAa,KAAK,GAAG,EAAE,QAAQ,KAAK,CAAC;YACvE,cAAc,MACZ,CAAC,YAAY,aAAa,KAAK,OAAO,SAAS,GAAG,EAAE,QAAQ,KAAK,CAAC;YAEpE,eAAe,CAAC,EAAE,OAAO,WAAW,cAAc,MAAM;AACtD,kBAAI,CAAC,UAAU;AACb,sBAAM,YAAY,UAAU,SAAS,MAAM,GAAG;AAC9C,sBAAM,YAAY,aAAc,MAAM,YAAY,WAAW,SAAS,MAAM,GAAG;AAC/E,sBAAM,aAAa,YAAY,KAAK;AACpC,sBAAM,UAAU,sBAAsB;AACtC,sBAAMA,SAAQ,OAAO,OAAO;AAC5B,sBAAM,kBAAkB,OAAO,aAAa;AAC5C,6BAAaA,SAAQ,iBAAiB,SAAS,EAAE,QAAQ,KAAK,CAAC;cACjE;YACF;UAAA;QACF,EAAA,CACF,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAEA,OAAO,cAAc;AAQrB,IAAM,CAAC,2BAA2B,2BAA2B,IAAI,oBAK9D,aAAa;EACd,WAAW;EACX,SAAS;EACT,MAAM;EACN,WAAW;AACb,CAAC;AAsBD,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,CAAC,QAAQ,SAAS,IAAU,eAAmC,IAAI;AACzE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,UAAU,IAAI,CAAC;AAC5E,UAAM,UAAgB,aAAmB;AACzC,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,iBAAiB,cAAc;AACrC,UAAM,oBAAqB,kBAAkB,CAAC,YAAc,CAAC,kBAAkB;AAE/E,aAAS,oBAAoB,iBAAyB;AACpD,YAAM,OAAO,QAAQ,WAAW,OAAQ,sBAAsB;AAC9D,YAAM,QAA0B,CAAC,GAAG,KAAK,KAAK;AAC9C,YAAM,SAA2B,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG;AAC3E,YAAM,QAAQ,YAAY,OAAO,MAAM;AAEvC,cAAQ,UAAU;AAClB,aAAO,MAAM,kBAAkB,KAAK,IAAI;IAC1C;AAEA,eACE;MAAC;MAAA;QACC,OAAO,MAAM;QACb,WAAW,oBAAoB,SAAS;QACxC,SAAS,oBAAoB,UAAU;QACvC,WAAW,oBAAoB,IAAI;QACnC,MAAK;QAEL,cAAA;UAAC;UAAA;YACC,KAAK;YACL,oBAAiB;YAChB,GAAG;YACJ,KAAK;YACL,OAAO;cACL,GAAG,YAAY;cACf,CAAC,gCAAuC,GAAG;YAC7C;YACA,cAAc,CAAC,UAAU;AACvB,oBAAM,QAAQ,oBAAoB,MAAM,OAAO;AAC/C,2DAAe;YACjB;YACA,aAAa,CAAC,UAAU;AACtB,oBAAM,QAAQ,oBAAoB,MAAM,OAAO;AAC/C,yDAAc;YAChB;YACA,YAAY,MAAM;AAChB,sBAAQ,UAAU;AAClB;YACF;YACA,eAAe,CAAC,UAAU;AACxB,oBAAM,iBAAiB,oBAAoB,cAAc;AACzD,oBAAM,YAAY,UAAU,cAAc,EAAE,SAAS,MAAM,GAAG;AAC9D,6DAAgB,EAAE,OAAO,WAAW,YAAY,KAAK,EAAE;YACzD;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AASA,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,YAAkB,aAA0B,IAAI;AACtD,UAAM,MAAM,gBAAgB,cAAc,SAAS;AACnD,UAAM,UAAgB,aAAmB;AACzC,UAAM,sBAAsB,CAAC;AAE7B,aAAS,oBAAoB,iBAAyB;AACpD,YAAM,OAAO,QAAQ,WAAW,UAAU,QAAS,sBAAsB;AACzE,YAAM,QAA0B,CAAC,GAAG,KAAK,MAAM;AAC/C,YAAM,SAA2B,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG;AAC7E,YAAM,QAAQ,YAAY,OAAO,MAAM;AAEvC,cAAQ,UAAU;AAClB,aAAO,MAAM,kBAAkB,KAAK,GAAG;IACzC;AAEA,eACE;MAAC;MAAA;QACC,OAAO,MAAM;QACb,WAAW,sBAAsB,WAAW;QAC5C,SAAS,sBAAsB,QAAQ;QACvC,MAAK;QACL,WAAW,sBAAsB,IAAI;QAErC,cAAA;UAAC;UAAA;YACC,oBAAiB;YAChB,GAAG;YACJ;YACA,OAAO;cACL,GAAG,YAAY;cACf,CAAC,gCAAuC,GAAG;YAC7C;YACA,cAAc,CAAC,UAAU;AACvB,oBAAM,QAAQ,oBAAoB,MAAM,OAAO;AAC/C,2DAAe;YACjB;YACA,aAAa,CAAC,UAAU;AACtB,oBAAM,QAAQ,oBAAoB,MAAM,OAAO;AAC/C,yDAAc;YAChB;YACA,YAAY,MAAM;AAChB,sBAAQ,UAAU;AAClB;YACF;YACA,eAAe,CAAC,UAAU;AACxB,oBAAM,iBAAiB,sBAAsB,gBAAgB;AAC7D,oBAAM,YAAY,UAAU,cAAc,EAAE,SAAS,MAAM,GAAG;AAC9D,6DAAgB,EAAE,OAAO,WAAW,YAAY,KAAK,EAAE;YACzD;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAkBA,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,iBAAiB,aAAa,aAAa;AAE3D,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,cAAI,MAAM,QAAQ,QAAQ;AACxB,0BAAc,KAAK;AAEnB,kBAAM,eAAe;UACvB,WAAW,MAAM,QAAQ,OAAO;AAC9B,yBAAa,KAAK;AAElB,kBAAM,eAAe;UACvB,WAAW,UAAU,OAAO,UAAU,EAAE,SAAS,MAAM,GAAG,GAAG;AAC3D,0BAAc,KAAK;AAEnB,kBAAM,eAAe;UACvB;QACF,CAAC;QACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,gBAAM,SAAS,MAAM;AACrB,iBAAO,kBAAkB,MAAM,SAAS;AAExC,gBAAM,eAAe;AAGrB,cAAI,QAAQ,OAAO,IAAI,MAAM,GAAG;AAC9B,mBAAO,MAAM;UACf,OAAO;AACL,yBAAa,KAAK;UACpB;QACF,CAAC;QACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,gBAAM,SAAS,MAAM;AACrB,cAAI,OAAO,kBAAkB,MAAM,SAAS,EAAG,aAAY,KAAK;QAClE,CAAC;QACD,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,gBAAM,SAAS,MAAM;AACrB,cAAI,OAAO,kBAAkB,MAAM,SAAS,GAAG;AAC7C,mBAAO,sBAAsB,MAAM,SAAS;AAC5C,uBAAW,KAAK;UAClB;QACF,CAAC;MAAA;IACH;EAEJ;AACF;AAMA,IAAM,aAAa;AAMnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,eACE;MAAC,UAAU;MAAV;QACC,iBAAe,QAAQ,WAAW,KAAK;QACvC,oBAAkB,QAAQ;QACzB,GAAG;QACJ,KAAK;MAAA;IACP;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,cAAc,4BAA4B,YAAY,aAAa;AACzE,UAAM,MAAY,aAAwB,IAAI;AAC9C,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,cAAc,QAAQ,OAAO;AACnC,UAAM,cAAc,QAAQ,OAAO;MAAI,CAAC,UACtC,yBAAyB,OAAO,QAAQ,KAAK,QAAQ,GAAG;IAC1D;AACA,UAAM,cAAc,cAAc,IAAI,KAAK,IAAI,GAAG,WAAW,IAAI;AACjE,UAAM,YAAY,MAAM,KAAK,IAAI,GAAG,WAAW;AAE/C,eACE;MAAC,UAAU;MAAV;QACC,oBAAkB,QAAQ;QAC1B,iBAAe,QAAQ,WAAW,KAAK;QACtC,GAAG;QACJ,KAAK;QACL,OAAO;UACL,GAAG,MAAM;UACT,CAAC,YAAY,SAAS,GAAG,cAAc;UACvC,CAAC,YAAY,OAAO,GAAG,YAAY;QACrC;MAAA;IACF;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,WAAW,cAAc,MAAM,aAAa;AAClD,UAAM,CAAC,OAAO,QAAQ,IAAU,eAAwC,IAAI;AAC5E,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,SAAS,IAAI,CAAC;AAC3E,UAAM,QAAc;MAClB,MAAO,QAAQ,SAAS,EAAE,UAAU,CAAC,SAAS,KAAK,IAAI,YAAY,KAAK,IAAI;MAC5E,CAAC,UAAU,KAAK;IAClB;AACA,eAAO,wBAAC,iBAAA,EAAiB,GAAG,OAAO,KAAK,cAAc,MAAA,CAAc;EACtE;AACF;AAQA,IAAM,kBAAwB;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,eAAe,OAAO,MAAM,GAAG,WAAW,IAAI;AACtD,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,cAAc,4BAA4B,YAAY,aAAa;AACzE,UAAM,CAAC,OAAO,QAAQ,IAAU,eAAiC,IAAI;AACrE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,SAAS,IAAI,CAAC;AAE3E,UAAM,gBAAgB,QAAQ,QAAQ,MAAM,QAAQ,MAAM,CAAC,IAAI;AAC/D,UAAM,OAAO,QAAQ,KAAK;AAE1B,UAAM,QAAQ,QAAQ,OAAO,KAAK;AAClC,UAAM,UACJ,UAAU,SAAY,IAAI,yBAAyB,OAAO,QAAQ,KAAK,QAAQ,GAAG;AACpF,UAAM,QAAQ,SAAS,OAAO,QAAQ,OAAO,MAAM;AACnD,UAAM,kBAAkB,6BAAO,YAAY;AAC3C,UAAM,sBAAsB,kBACxB,uBAAuB,iBAAiB,SAAS,YAAY,SAAS,IACtE;AAEE,IAAA,gBAAU,MAAM;AACpB,UAAI,OAAO;AACT,gBAAQ,OAAO,IAAI,KAAK;AACxB,eAAO,MAAM;AACX,kBAAQ,OAAO,OAAO,KAAK;QAC7B;MACF;IACF,GAAG,CAAC,OAAO,QAAQ,MAAM,CAAC;AAE1B,eACE;MAAC;MAAA;QACC,OAAO;UACL,WAAW;UACX,UAAU;UACV,CAAC,YAAY,SAAS,GAAG,QAAQ,OAAO,OAAO,mBAAmB;QACpE;QAEA,UAAA;cAAA,wBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,eAChC,cAAA;YAAC,UAAU;YAAV;cACC,MAAK;cACL,cAAY,MAAM,YAAY,KAAK;cACnC,iBAAe,QAAQ;cACvB,iBAAe;cACf,iBAAe,QAAQ;cACvB,oBAAkB,QAAQ;cAC1B,oBAAkB,QAAQ;cAC1B,iBAAe,QAAQ,WAAW,KAAK;cACvC,UAAU,QAAQ,WAAW,SAAY;cACxC,GAAG;cACJ,KAAK;cAOL,OAAO,UAAU,SAAY,EAAE,SAAS,OAAO,IAAI,MAAM;cACzD,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,wBAAQ,sBAAsB,UAAU;cAC1C,CAAC;YAAA;UACH,EAAA,CACF;UAEC,qBACC;YAAC;YAAA;cAEC,MACE,SACC,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,OAAO,SAAS,IAAI,OAAO,MAAM;cAE3E;YAAA;YALK;UAMP;QAAA;MAAA;IAEJ;EAEJ;AACF;AAEA,YAAY,cAAc;AAI1B,IAAM,cAAc,CAAC,UAAmD;AACtE,QAAM,EAAE,OAAO,GAAG,WAAW,IAAI;AACjC,QAAM,MAAY,aAAyB,IAAI;AAC/C,QAAM,YAAY,YAAY,KAAK;AAG7B,EAAA,gBAAU,MAAM;AACpB,UAAM,QAAQ,IAAI;AAClB,UAAM,aAAa,OAAO,iBAAiB;AAC3C,UAAM,aAAa,OAAO,yBAAyB,YAAY,OAAO;AACtE,UAAM,WAAW,WAAW;AAC5B,QAAI,cAAc,SAAS,UAAU;AACnC,YAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC;AAClD,eAAS,KAAK,OAAO,KAAK;AAC1B,YAAM,cAAc,KAAK;IAC3B;EACF,GAAG,CAAC,WAAW,KAAK,CAAC;AAWrB,aAAO,wBAAC,SAAA,EAAM,OAAO,EAAE,SAAS,OAAO,GAAI,GAAG,YAAY,KAAU,cAAc,MAAA,CAAO;AAC3F;AAEA,SAAS,oBAAoB,aAAuB,CAAC,GAAG,WAAmB,SAAiB;AAC1F,QAAM,aAAa,CAAC,GAAG,UAAU;AACjC,aAAW,OAAO,IAAI;AACtB,SAAO,WAAW,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACxC;AAEA,SAAS,yBAAyB,OAAe,KAAa,KAAa;AACzE,QAAM,WAAW,MAAM;AACvB,QAAM,iBAAiB,MAAM;AAC7B,QAAM,aAAa,kBAAkB,QAAQ;AAC7C,SAAO,MAAM,YAAY,CAAC,GAAG,GAAG,CAAC;AACnC;AAKA,SAAS,SAAS,OAAe,aAAqB;AACpD,MAAI,cAAc,GAAG;AACnB,WAAO,SAAS,QAAQ,CAAC,OAAO,WAAW;EAC7C,WAAW,gBAAgB,GAAG;AAC5B,WAAO,CAAC,WAAW,SAAS,EAAE,KAAK;EACrC,OAAO;AACL,WAAO;EACT;AACF;AAUA,SAAS,qBAAqB,QAAkB,WAAmB;AACjE,MAAI,OAAO,WAAW,EAAG,QAAO;AAChC,QAAM,YAAY,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,QAAQ,SAAS,CAAC;AACnE,QAAM,kBAAkB,KAAK,IAAI,GAAG,SAAS;AAC7C,SAAO,UAAU,QAAQ,eAAe;AAC1C;AAMA,SAAS,uBAAuB,OAAe,MAAc,WAAmB;AAC9E,QAAM,YAAY,QAAQ;AAC1B,QAAM,cAAc;AACpB,QAAM,SAAS,YAAY,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,SAAS,CAAC;AAC3D,UAAQ,YAAY,OAAO,IAAI,IAAI,aAAa;AAClD;AASA,SAAS,sBAAsB,QAAkB;AAC/C,SAAO,OAAO,MAAM,GAAG,EAAE,EAAE,IAAI,CAAC,OAAO,UAAU,OAAO,QAAQ,CAAC,IAAI,KAAK;AAC5E;AAcA,SAAS,yBAAyB,QAAkB,uBAA+B;AACjF,MAAI,wBAAwB,GAAG;AAC7B,UAAM,qBAAqB,sBAAsB,MAAM;AACvD,UAAM,8BAA8B,KAAK,IAAI,GAAG,kBAAkB;AAClE,WAAO,+BAA+B;EACxC;AACA,SAAO;AACT;AAGA,SAAS,YAAY,OAAkC,QAAmC;AACxF,SAAO,CAAC,UAAkB;AACxB,QAAI,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,OAAO,CAAC,MAAM,OAAO,CAAC,EAAG,QAAO,OAAO,CAAC;AACrE,UAAM,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,MAAM,CAAC,IAAI,MAAM,CAAC;AAC3D,WAAO,OAAO,CAAC,IAAI,SAAS,QAAQ,MAAM,CAAC;EAC7C;AACF;AAEA,SAAS,gBAAgB,OAAe;AACtC,UAAQ,OAAO,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,IAAI;AAC7C;AAEA,SAAS,WAAW,OAAe,cAAsB;AACvD,QAAM,UAAU,KAAK,IAAI,IAAI,YAAY;AACzC,SAAO,KAAK,MAAM,QAAQ,OAAO,IAAI;AACvC;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ;", "names": ["value"]}