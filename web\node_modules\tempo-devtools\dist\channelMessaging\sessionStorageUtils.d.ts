export declare const TREE_ELEMENT_LOOKUP = "TREE_ELEMENT_LOOKUP";
export declare const SCOPE_LOOKUP = "SCOPE_LOOKUP";
export declare const STORYBOARD_COMPONENT = "STORYBOARD_COMPONENT";
export declare const STORYBOARD_TYPE = "STORYBOARD_TYPE";
export declare const ORIGINAL_STORYBOARD_URL = "ORIGINAL_STORYBOARD_URL";
export declare const SAVED_STORYBOARD_COMPONENT_FILENAME = "SAVED_STORYBOARD_COMPONENT_FILENAME";
export declare const SELECTED_ELEMENT_KEY = "SELECTED_ELEMENT_KEY";
export declare const MULTI_SELECTED_ELEMENT_KEYS = "MULTI_SELECTED_ELEMENT_KEYS";
export declare const HOVERED_ELEMENT_KEY = "HOVERED_ELEMENT_KEY";
export declare const TEXT_EDIT = "TEXT_EDIT";
export declare const HOT_RELOADING = "HOT_RELOADING";
export declare const IS_FLUSHING = "IS_FLUSHING";
export declare const NAV_TREE_CALLBACKS = "NAV_TREE_CALLBACKS";
export declare const ELEMENT_KEY_TO_LOOKUP_LIST = "ELEMENT_KEY_TO_LOOKUP_LIST";
export declare const ELEMENT_KEY_TO_NAV_NODE = "ELEMENT_KEY_TO_NAV_NODE";
export declare const CURRENT_NAV_TREE = "CURRENT_NAV_TREE";
export declare const getMemoryStorageItem: (key: string) => any | null;
export declare const setMemoryStorageItem: (key: string, value: any | null) => void;
export declare const removeMemoryStorageItem: (key: string) => void;
export declare const getSessionStorageItem: (key: string, storyboardId?: string) => string | null;
export declare const setSessionStorageItem: (key: string, value: string | null, storyboardId?: string) => void;
export declare const removeSessionStorageItem: (key: string, storyboardId?: string) => void;
