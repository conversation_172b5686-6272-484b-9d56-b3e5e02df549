apiVersion: v1
kind: Secret
metadata:
  name: streamscale-secrets
type: Opaque
data:
  # These are placeholders and should be replaced with actual base64-encoded values
  redis.password: ${REDIS_PASSWORD_BASE64}
  aws.access_key: ${AWS_ACCESS_KEY_BASE64}
  aws.secret_key: ${AWS_SECRET_KEY_BASE64}
  postgres.user: ${POSTGRES_USER_BASE64}
  postgres.password: ${POSTGRES_PASSWORD_BASE64}
  jwt.secret_key: ${JWT_SECRET_KEY_BASE64}
