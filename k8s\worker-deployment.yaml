apiVersion: apps/v1
kind: Deployment
metadata:
  name: streamscale-worker
  labels:
    app: streamscale
    component: worker
spec:
  replicas: 2  # Start with 2 replicas, will scale based on HPA
  selector:
    matchLabels:
      app: streamscale
      component: worker
  template:
    metadata:
      labels:
        app: streamscale
        component: worker
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: nvidia.com/gpu
                operator: Exists
      containers:
      - name: worker
        image: ${WORKER_IMAGE_NAME}:${WORKER_IMAGE_TAG}
        imagePullPolicy: Always
        resources:
          requests:
            cpu: "1000m"
            memory: "2Gi"
            nvidia.com/gpu: "1"
          limits:
            cpu: "4"
            memory: "4Gi"
            nvidia.com/gpu: "1"
        volumeMounts:
        - name: temp-storage
          mountPath: /app/tmp_segments
        - name: config-volume
          mountPath: /app/config.yml
          subPath: config.yml
        env:
        - name: REDIS_ADDR
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: redis.addr
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: streamscale-secrets
              key: redis.password
        - name: AWS_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: streamscale-secrets
              key: aws.access_key
        - name: AWS_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: streamscale-secrets
              key: aws.secret_key
        - name: AWS_REGION
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: aws.region
        - name: S3_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: s3.endpoint
        - name: S3_INPUT_BUCKET
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: s3.input_bucket
        - name: S3_OUTPUT_BUCKET
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: s3.output_bucket
        - name: S3_CDN_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: s3.cdn_endpoint
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: postgres.host
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: postgres.port
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: streamscale-secrets
              key: postgres.user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: streamscale-secrets
              key: postgres.password
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: postgres.db
        - name: MAX_PARALLEL_JOBS
          value: "4"
        - name: NVIDIA_VISIBLE_DEVICES
          value: "all"
        - name: ENABLE_HARDWARE_ACCELERATION
          value: "true"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: temp-storage
        emptyDir: {}
      - name: config-volume
        configMap:
          name: streamscale-worker-config
