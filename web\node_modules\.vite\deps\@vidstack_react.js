"use client";
import {
  <PERSON><PERSON>eyShortcuts,
  AUDIO_EXTENSIONS,
  AUDIO_TYPES,
  AirPlayButtonInstance,
  AudioGainSliderInstance,
  AudioProviderLoader,
  AudioTrackList,
  CaptionButtonInstance,
  CaptionsInstance,
  ControlsGroupInstance,
  ControlsInstance,
  DASHProviderLoader,
  DASH_VIDEO_EXTENSIONS,
  DASH_VIDEO_TYPES,
  FullscreenButtonInstance,
  FullscreenController,
  GestureInstance,
  GoogleCastButtonInstance,
  HLSProviderLoader,
  HLS_VIDEO_EXTENSIONS,
  HLS_VIDEO_TYPES,
  IS_SERVER,
  List,
  LiveButtonInstance,
  LocalMediaStorage,
  Logger,
  MEDIA_KEY_SHORTCUTS,
  MediaAnnouncerInstance,
  MediaControls,
  MediaPlayerInstance,
  MediaProviderInstance,
  MediaRemoteControl,
  MenuButtonInstance,
  MenuInstance,
  MenuItemInstance,
  MenuItemsInstance,
  MenuPortalInstance,
  MuteButtonInstance,
  PIPButtonInstance,
  PlayButtonInstance,
  PosterInstance,
  Primitive,
  QualitySliderInstance,
  RadioGroupController,
  RadioGroupInstance,
  RadioInstance,
  ScreenOrientationController,
  SeekButtonInstance,
  SliderChaptersInstance,
  SliderInstance,
  SliderPreviewInstance,
  SliderThumbnailInstance,
  SliderValueInstance,
  SliderVideoInstance,
  SpeedSliderInstance,
  TextRenderers,
  TextTrack,
  TextTrackList,
  TextTrackSymbol,
  ThumbnailInstance,
  ThumbnailsLoader,
  TimeInstance,
  TimeRange,
  TimeSliderInstance,
  ToggleButtonInstance,
  TooltipContentInstance,
  TooltipInstance,
  TooltipTriggerInstance,
  VIDEO_EXTENSIONS,
  VIDEO_TYPES,
  VideoProviderLoader,
  VideoQualityList,
  VimeoProviderLoader,
  VolumeSliderInstance,
  YouTubeProviderLoader,
  boundTime,
  canChangeVolume,
  canFullscreen,
  canGoogleCastSrc,
  canOrientScreen,
  canPlayHLSNatively,
  canRotateScreen,
  canUsePictureInPicture,
  canUseVideoPresentation,
  findActiveCue,
  formatSpokenTime,
  formatTime,
  getDownloadFile,
  getTimeRangesEnd,
  getTimeRangesStart,
  isAudioProvider,
  isAudioSrc,
  isCueActive,
  isDASHProvider,
  isDASHSrc,
  isGoogleCastProvider,
  isHLSProvider,
  isHLSSrc,
  isHTMLAudioElement,
  isHTMLIFrameElement,
  isHTMLMediaElement,
  isHTMLVideoElement,
  isMediaStream,
  isRemotionProvider,
  isTrackCaptionKind,
  isVideoProvider,
  isVideoQualitySrc,
  isVideoSrc,
  isVimeoProvider,
  isYouTubeProvider,
  mediaContext,
  mediaState,
  menuContext,
  normalizeTimeIntervals,
  parseJSONCaptionsFile,
  sliderState,
  softResetMediaState,
  sortVideoQualities,
  updateSliderPreviewPlacement,
  updateTimeIntervals,
  useMediaContext,
  useMediaState,
  useMediaStore,
  useSliderState,
  useSliderStore,
  watchActiveTextTrack,
  watchCueTextChange
} from "./chunk-SVRZA3JR.js";
import {
  Component,
  DOMEvent,
  EventsController,
  appendTriggerEvent,
  composeRefs,
  createReactComponent,
  effect,
  findTriggerEvent,
  hasProvidedContext,
  hasTriggerEvent,
  isKeyboardClick,
  isKeyboardEvent,
  isPointerEvent,
  isString,
  listenEvent,
  method,
  noop,
  prop,
  scoped,
  signal,
  useContext,
  useReactContext,
  useReactScope,
  useSignal,
  useSignalRecord,
  useStateContext,
  walkTriggerEventChain
} from "./chunk-DSWAFM5W.js";
import "./chunk-Y4LBKCPT.js";
import {
  require_react_dom
} from "./chunk-F34GCA6J.js";
import {
  require_react
} from "./chunk-ZMLY2J2T.js";
import {
  __privateAdd,
  __privateGet,
  __privateMethod,
  __privateSet,
  __publicField,
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/@vidstack/react/dev/vidstack.js
var React5 = __toESM(require_react());

// node_modules/@vidstack/react/dev/chunks/vidstack-BUd8DcBH.js
var React2 = __toESM(require_react(), 1);

// node_modules/@vidstack/react/dev/chunks/vidstack-Xovmcdt1.js
var React = __toESM(require_react(), 1);
var import_react_dom = __toESM(require_react_dom(), 1);
function useMediaContext2() {
  return useReactContext(mediaContext);
}
var AirPlayButtonBridge = createReactComponent(AirPlayButtonInstance, {
  domEventsRegex: /^onMedia/
});
var AirPlayButton = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(AirPlayButtonBridge, { ...props }, (props2) => React.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
AirPlayButton.displayName = "AirPlayButton";
var PlayButtonBridge = createReactComponent(PlayButtonInstance, {
  domEventsRegex: /^onMedia/
});
var PlayButton = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(PlayButtonBridge, { ...props }, (props2) => React.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
PlayButton.displayName = "PlayButton";
var CaptionButtonBridge = createReactComponent(CaptionButtonInstance, {
  domEventsRegex: /^onMedia/
});
var CaptionButton = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(CaptionButtonBridge, { ...props }, (props2) => React.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
CaptionButton.displayName = "CaptionButton";
var FullscreenButtonBridge = createReactComponent(FullscreenButtonInstance, {
  domEventsRegex: /^onMedia/
});
var FullscreenButton = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(FullscreenButtonBridge, { ...props }, (props2) => React.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
FullscreenButton.displayName = "FullscreenButton";
var MuteButtonBridge = createReactComponent(MuteButtonInstance, {
  domEventsRegex: /^onMedia/
});
var MuteButton = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(MuteButtonBridge, { ...props }, (props2) => React.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
MuteButton.displayName = "MuteButton";
var PIPButtonBridge = createReactComponent(PIPButtonInstance, {
  domEventsRegex: /^onMedia/
});
var PIPButton = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(PIPButtonBridge, { ...props }, (props2) => React.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
PIPButton.displayName = "PIPButton";
var SeekButtonBridge = createReactComponent(SeekButtonInstance, {
  domEventsRegex: /^onMedia/
});
var SeekButton = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(SeekButtonBridge, { ...props }, (props2) => React.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
SeekButton.displayName = "SeekButton";
var LiveButtonBridge = createReactComponent(LiveButtonInstance, {
  domEventsRegex: /^onMedia/
});
var LiveButton = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(LiveButtonBridge, { ...props }, (props2) => React.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
LiveButton.displayName = "LiveButton";
var sliderCallbacks = [
  "onDragStart",
  "onDragEnd",
  "onDragValueChange",
  "onValueChange",
  "onPointerValueChange"
];
var SliderValueBridge = createReactComponent(SliderValueInstance);
var SliderBridge = createReactComponent(SliderInstance, {
  events: sliderCallbacks
});
var Root$5 = React.forwardRef(({ children, ...props }, forwardRef5) => {
  return React.createElement(SliderBridge, { ...props, ref: forwardRef5 }, (props2) => React.createElement(Primitive.div, { ...props2 }, children));
});
Root$5.displayName = "Slider";
var Thumb = React.forwardRef((props, forwardRef5) => React.createElement(Primitive.div, { ...props, ref: forwardRef5 }));
Thumb.displayName = "SliderThumb";
var Track = React.forwardRef((props, forwardRef5) => React.createElement(Primitive.div, { ...props, ref: forwardRef5 }));
Track.displayName = "SliderTrack";
var TrackFill = React.forwardRef((props, forwardRef5) => React.createElement(Primitive.div, { ...props, ref: forwardRef5 }));
TrackFill.displayName = "SliderTrackFill";
var PreviewBridge = createReactComponent(SliderPreviewInstance);
var Preview = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(PreviewBridge, { ...props }, (props2) => React.createElement(
      Primitive.div,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
Preview.displayName = "SliderPreview";
var Value = React.forwardRef(({ children, ...props }, forwardRef5) => {
  return React.createElement(SliderValueBridge, { ...props }, (props2, instance) => {
    const $text = useSignal(() => instance.getValueText(), instance);
    return React.createElement(Primitive.div, { ...props2, ref: forwardRef5 }, $text, children);
  });
});
Value.displayName = "SliderValue";
var Steps = React.forwardRef(({ children, ...props }, forwardRef5) => {
  const $min = useSliderState("min"), $max = useSliderState("max"), $step = useSliderState("step"), steps = ($max - $min) / $step;
  return React.createElement(Primitive.div, { ...props, ref: forwardRef5 }, Array.from({ length: Math.floor(steps) + 1 }).map((_, step) => children(step)));
});
Steps.displayName = "SliderSteps";
var slider = Object.freeze({
  __proto__: null,
  Preview,
  Root: Root$5,
  Steps,
  Thumb,
  Track,
  TrackFill,
  Value
});
var VolumeSliderBridge = createReactComponent(VolumeSliderInstance, {
  events: sliderCallbacks,
  domEventsRegex: /^onMedia/
});
var Root$4 = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(VolumeSliderBridge, { ...props, ref: forwardRef5 }, (props2) => React.createElement(Primitive.div, { ...props2 }, children));
  }
);
Root$4.displayName = "VolumeSlider";
var volumeSlider = Object.freeze({
  __proto__: null,
  Preview,
  Root: Root$4,
  Steps,
  Thumb,
  Track,
  TrackFill,
  Value
});
function createVTTCue(startTime = 0, endTime = 0, text = "") {
  if (IS_SERVER) {
    return {
      startTime,
      endTime,
      text,
      addEventListener: noop,
      removeEventListener: noop,
      dispatchEvent: noop
    };
  }
  return new window.VTTCue(startTime, endTime, text);
}
var ThumbnailBridge = createReactComponent(ThumbnailInstance);
var Root$3 = React.forwardRef(({ children, ...props }, forwardRef5) => {
  return React.createElement(ThumbnailBridge, { ...props }, (props2) => React.createElement(
    Primitive.div,
    {
      ...props2,
      ref: composeRefs(props2.ref, forwardRef5)
    },
    children
  ));
});
Root$3.displayName = "Thumbnail";
var Img = React.forwardRef(({ children, ...props }, forwardRef5) => {
  const { src, img, crossOrigin } = useStateContext(ThumbnailInstance.state), $src = useSignal(src), $crossOrigin = useSignal(crossOrigin);
  return React.createElement(
    Primitive.img,
    {
      crossOrigin: $crossOrigin,
      ...props,
      src: $src || void 0,
      ref: composeRefs(img.set, forwardRef5)
    },
    children
  );
});
Img.displayName = "ThumbnailImg";
var thumbnail = Object.freeze({
  __proto__: null,
  Img,
  Root: Root$3
});
var TimeSliderContext = React.createContext({
  $chapters: signal(null)
});
TimeSliderContext.displayName = "TimeSliderContext";
var TimeSliderBridge = createReactComponent(TimeSliderInstance, {
  events: sliderCallbacks,
  domEventsRegex: /^onMedia/
});
var Root$2 = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    const $chapters = React.useMemo(() => signal(null), []);
    return React.createElement(TimeSliderContext.Provider, { value: { $chapters } }, React.createElement(TimeSliderBridge, { ...props, ref: forwardRef5 }, (props2) => React.createElement(Primitive.div, { ...props2 }, children)));
  }
);
Root$2.displayName = "TimeSlider";
var SliderChaptersBridge = createReactComponent(SliderChaptersInstance);
var Chapters = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(SliderChaptersBridge, { ...props }, (props2, instance) => React.createElement(
      Primitive.div,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      React.createElement(ChapterTracks, { instance }, children)
    ));
  }
);
Chapters.displayName = "SliderChapters";
function ChapterTracks({ instance, children }) {
  const $cues = useSignal(() => instance.cues, instance), refs = React.useRef([]), emptyCue = React.useRef(), { $chapters } = React.useContext(TimeSliderContext);
  if (!emptyCue.current) {
    emptyCue.current = createVTTCue();
  }
  React.useEffect(() => {
    $chapters.set(instance);
    return () => void $chapters.set(null);
  }, [instance]);
  React.useEffect(() => {
    instance.setRefs(refs.current);
  }, [$cues]);
  return children($cues.length ? $cues : [emptyCue.current], (el) => {
    if (!el) {
      refs.current.length = 0;
      return;
    }
    refs.current.push(el);
  });
}
ChapterTracks.displayName = "SliderChapterTracks";
var ChapterTitle = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    const { $chapters } = React.useContext(TimeSliderContext), [title, setTitle] = React.useState();
    React.useEffect(() => {
      return effect(() => {
        const chapters = $chapters(), cue = (chapters == null ? void 0 : chapters.activePointerCue) || (chapters == null ? void 0 : chapters.activeCue);
        setTitle((cue == null ? void 0 : cue.text) || "");
      });
    }, []);
    return React.createElement(Primitive.div, { ...props, ref: forwardRef5 }, title, children);
  }
);
ChapterTitle.displayName = "SliderChapterTitle";
var Progress = React.forwardRef((props, forwardRef5) => React.createElement(Primitive.div, { ...props, ref: forwardRef5 }));
Progress.displayName = "SliderProgress";
var SliderThumbnailBridge = createReactComponent(SliderThumbnailInstance);
var ThumbnailRoot = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(SliderThumbnailBridge, { ...props }, (props2) => React.createElement(Primitive.div, { ...props2, ref: composeRefs(props2.ref, forwardRef5) }, children));
  }
);
ThumbnailRoot.displayName = "SliderThumbnail";
var Thumbnail = {
  Root: ThumbnailRoot,
  Img
};
var VideoBridge = createReactComponent(SliderVideoInstance, {
  events: ["onCanPlay", "onError"]
});
var Video = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(VideoBridge, { ...props }, (props2, instance) => React.createElement(
      VideoProvider,
      {
        ...props2,
        instance,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
Video.displayName = "SliderVideo";
var VideoProvider = React.forwardRef(
  ({ instance, children, ...props }, forwardRef5) => {
    const { canLoad } = useStateContext(mediaState), { src, video, crossOrigin } = instance.$state, $src = useSignal(src), $canLoad = useSignal(canLoad), $crossOrigin = useSignal(crossOrigin);
    return React.createElement(
      Primitive.video,
      {
        style: { maxWidth: "unset" },
        ...props,
        src: $src || void 0,
        muted: true,
        playsInline: true,
        preload: $canLoad ? "auto" : "none",
        crossOrigin: $crossOrigin || void 0,
        ref: composeRefs(video.set, forwardRef5)
      },
      children
    );
  }
);
VideoProvider.displayName = "SliderVideoProvider";
var timeSlider = Object.freeze({
  __proto__: null,
  ChapterTitle,
  Chapters,
  Preview,
  Progress,
  Root: Root$2,
  Steps,
  Thumb,
  Thumbnail,
  Track,
  TrackFill,
  Value,
  Video
});
var RadioGroupBridge = createReactComponent(RadioGroupInstance, {
  events: ["onChange"]
});
var Root$1 = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(RadioGroupBridge, { ...props, ref: forwardRef5 }, (props2) => React.createElement(Primitive.div, { ...props2 }, children));
  }
);
Root$1.displayName = "RadioGroup";
var ItemBridge$1 = createReactComponent(RadioInstance, {
  events: ["onChange", "onSelect"]
});
var Item$1 = React.forwardRef(({ children, ...props }, forwardRef5) => {
  return React.createElement(ItemBridge$1, { ...props }, (props2) => React.createElement(
    Primitive.div,
    {
      ...props2,
      ref: composeRefs(props2.ref, forwardRef5)
    },
    children
  ));
});
Item$1.displayName = "RadioItem";
var radioGroup = Object.freeze({
  __proto__: null,
  Item: Item$1,
  Root: Root$1
});
var MenuBridge = createReactComponent(MenuInstance, {
  events: ["onOpen", "onClose"],
  domEventsRegex: /^onMedia/
});
var Root = React.forwardRef(({ children, ...props }, forwardRef5) => {
  return React.createElement(MenuBridge, { ...props, ref: forwardRef5 }, (props2, instance) => React.createElement(
    Primitive.div,
    {
      ...props2,
      style: { display: !instance.isSubmenu ? "contents" : void 0, ...props2.style }
    },
    children
  ));
});
Root.displayName = "Menu";
var ButtonBridge = createReactComponent(MenuButtonInstance, {
  events: ["onSelect"]
});
var Button = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(ButtonBridge, { ...props }, (props2) => React.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
Button.displayName = "MenuButton";
var Portal = React.forwardRef(
  ({ container = null, disabled = false, children, ...props }, forwardRef5) => {
    let fullscreen = useMediaState("fullscreen"), shouldPortal = disabled === "fullscreen" ? !fullscreen : !disabled;
    const target = React.useMemo(() => {
      if (IS_SERVER) return null;
      const node = isString(container) ? document.querySelector(container) : container;
      return node ?? document.body;
    }, [container]);
    return !target || !shouldPortal ? children : (0, import_react_dom.createPortal)(
      React.createElement(
        Primitive.div,
        {
          ...props,
          style: { display: "contents", ...props.style },
          ref: forwardRef5
        },
        children
      ),
      target
    );
  }
);
Portal.displayName = "MenuPortal";
var ItemsBridge = createReactComponent(MenuItemsInstance);
var Items = React.forwardRef(({ children, ...props }, forwardRef5) => {
  return React.createElement(ItemsBridge, { ...props }, (props2) => React.createElement(
    Primitive.div,
    {
      ...props2,
      ref: composeRefs(props2.ref, forwardRef5)
    },
    children
  ));
});
Items.displayName = "MenuItems";
var ItemBridge = createReactComponent(MenuItemInstance);
var Item = React.forwardRef(({ children, ...props }, forwardRef5) => {
  return React.createElement(ItemBridge, { ...props }, (props2) => React.createElement(
    Primitive.div,
    {
      ...props2,
      ref: composeRefs(props2.ref, forwardRef5)
    },
    children
  ));
});
Item.displayName = "MenuItem";
var menu = Object.freeze({
  __proto__: null,
  Button,
  Content: Items,
  Item,
  Items,
  Portal,
  Radio: Item$1,
  RadioGroup: Root$1,
  Root
});
var GestureBridge = createReactComponent(GestureInstance, {
  events: ["onWillTrigger", "onTrigger"]
});
var Gesture = React.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React.createElement(GestureBridge, { ...props, ref: forwardRef5 }, (props2) => React.createElement(Primitive.div, { ...props2 }, children));
  }
);
Gesture.displayName = "Gesture";
var TimeBridge = createReactComponent(TimeInstance);
var Time = React.forwardRef(({ children, ...props }, forwardRef5) => {
  return React.createElement(TimeBridge, { ...props }, (props2, instance) => React.createElement(
    TimeText,
    {
      ...props2,
      instance,
      ref: composeRefs(props2.ref, forwardRef5)
    },
    children
  ));
});
Time.displayName = "Time";
var TimeText = React.forwardRef(
  ({ instance, children, ...props }, forwardRef5) => {
    const { timeText } = instance.$state, $timeText = useSignal(timeText);
    return React.createElement(Primitive.div, { ...props, ref: forwardRef5 }, $timeText, children);
  }
);
TimeText.displayName = "TimeText";
function useMediaPlayer() {
  const context = useMediaContext2();
  if (!context) {
    throw Error(
      "[vidstack] no media context was found - was this called outside of `<MediaPlayer>`?"
    );
  }
  return (context == null ? void 0 : context.player) || null;
}
function useAudioOptions() {
  const media = useMediaContext2(), { audioTracks, audioTrack } = media.$state, $audioTracks = useSignal(audioTracks);
  useSignal(audioTrack);
  return React.useMemo(() => {
    const options = $audioTracks.map((track) => ({
      track,
      label: track.label,
      value: getTrackValue$1(track),
      get selected() {
        return audioTrack() === track;
      },
      select(trigger) {
        const index = audioTracks().indexOf(track);
        if (index >= 0) media.remote.changeAudioTrack(index, trigger);
      }
    }));
    Object.defineProperty(options, "disabled", {
      get() {
        return options.length <= 1;
      }
    });
    Object.defineProperty(options, "selectedTrack", {
      get() {
        return audioTrack();
      }
    });
    Object.defineProperty(options, "selectedValue", {
      get() {
        const track = audioTrack();
        return track ? getTrackValue$1(track) : void 0;
      }
    });
    return options;
  }, [$audioTracks]);
}
function getTrackValue$1(track) {
  return track.label.toLowerCase();
}
function useCaptionOptions({ off = true } = {}) {
  const media = useMediaContext2(), { textTracks, textTrack } = media.$state, $textTracks = useSignal(textTracks);
  useSignal(textTrack);
  return React.useMemo(() => {
    const captionTracks = $textTracks.filter(isTrackCaptionKind), options = captionTracks.map((track) => ({
      track,
      label: track.label,
      value: getTrackValue(track),
      get selected() {
        return textTrack() === track;
      },
      select(trigger) {
        const index = textTracks().indexOf(track);
        if (index >= 0) media.remote.changeTextTrackMode(index, "showing", trigger);
      }
    }));
    if (off) {
      options.unshift({
        track: null,
        label: isString(off) ? off : "Off",
        value: "off",
        get selected() {
          return !textTrack();
        },
        select(trigger) {
          media.remote.toggleCaptions(trigger);
        }
      });
    }
    Object.defineProperty(options, "disabled", {
      get() {
        return !captionTracks.length;
      }
    });
    Object.defineProperty(options, "selectedTrack", {
      get() {
        return textTrack();
      }
    });
    Object.defineProperty(options, "selectedValue", {
      get() {
        const track = textTrack();
        return track ? getTrackValue(track) : "off";
      }
    });
    return options;
  }, [$textTracks]);
}
function getTrackValue(track) {
  return track.id + ":" + track.kind + "-" + track.label.toLowerCase();
}

// node_modules/@vidstack/react/dev/chunks/vidstack-BUd8DcBH.js
var MediaAnnouncerBridge = createReactComponent(MediaAnnouncerInstance, {
  events: ["onChange"]
});
var MediaAnnouncer = React2.forwardRef(
  ({ style, children, ...props }, forwardRef5) => {
    return React2.createElement(MediaAnnouncerBridge, { ...props }, (props2) => React2.createElement(
      Primitive.div,
      {
        ...props2,
        style: { display: "contents", ...style },
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
MediaAnnouncer.displayName = "MediaAnnouncer";
var ControlsBridge = createReactComponent(ControlsInstance);
var Root$52 = React2.forwardRef(({ children, ...props }, forwardRef5) => {
  return React2.createElement(ControlsBridge, { ...props }, (props2) => React2.createElement(
    Primitive.div,
    {
      ...props2,
      ref: composeRefs(props2.ref, forwardRef5)
    },
    children
  ));
});
Root$52.displayName = "Controls";
var ControlsGroupBridge = createReactComponent(ControlsGroupInstance);
var Group = React2.forwardRef(({ children, ...props }, forwardRef5) => {
  return React2.createElement(ControlsGroupBridge, { ...props }, (props2) => React2.createElement(
    Primitive.div,
    {
      ...props2,
      ref: composeRefs(props2.ref, forwardRef5)
    },
    children
  ));
});
Group.displayName = "ControlsGroup";
var controls = Object.freeze({
  __proto__: null,
  Group,
  Root: Root$52
});
var TooltipBridge = createReactComponent(TooltipInstance);
function Root$42({ children, ...props }) {
  return React2.createElement(TooltipBridge, { ...props }, children);
}
Root$42.displayName = "Tooltip";
var TriggerBridge = createReactComponent(TooltipTriggerInstance);
var Trigger = React2.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React2.createElement(TriggerBridge, { ...props }, (props2) => React2.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
Trigger.displayName = "TooltipTrigger";
var ContentBridge = createReactComponent(TooltipContentInstance);
var Content = React2.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React2.createElement(ContentBridge, { ...props }, (props2) => React2.createElement(
      Primitive.div,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
Content.displayName = "TooltipContent";
var tooltip = Object.freeze({
  __proto__: null,
  Content,
  Root: Root$42,
  Trigger
});
var GoogleCastButtonBridge = createReactComponent(GoogleCastButtonInstance, {
  domEventsRegex: /^onMedia/
});
var GoogleCastButton = React2.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React2.createElement(GoogleCastButtonBridge, { ...props }, (props2) => React2.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
GoogleCastButton.displayName = "GoogleCastButton";
var QualitySliderBridge = createReactComponent(QualitySliderInstance, {
  events: sliderCallbacks,
  domEventsRegex: /^onMedia/
});
var Root$32 = React2.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React2.createElement(QualitySliderBridge, { ...props, ref: forwardRef5 }, (props2) => React2.createElement(Primitive.div, { ...props2 }, children));
  }
);
Root$32.displayName = "QualitySlider";
var qualitySlider = Object.freeze({
  __proto__: null,
  Preview,
  Root: Root$32,
  Steps,
  Thumb,
  Track,
  TrackFill,
  Value
});
var AudioGainSliderBridge = createReactComponent(AudioGainSliderInstance, {
  events: sliderCallbacks,
  domEventsRegex: /^onMedia/
});
var Root$22 = React2.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React2.createElement(AudioGainSliderBridge, { ...props, ref: forwardRef5 }, (props2) => React2.createElement(Primitive.div, { ...props2 }, children));
  }
);
Root$22.displayName = "AudioGainSlider";
var audioGainSlider = Object.freeze({
  __proto__: null,
  Preview,
  Root: Root$22,
  Steps,
  Thumb,
  Track,
  TrackFill,
  Value
});
var SpeedSliderBridge = createReactComponent(SpeedSliderInstance, {
  events: sliderCallbacks,
  domEventsRegex: /^onMedia/
});
var Root$12 = React2.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React2.createElement(SpeedSliderBridge, { ...props, ref: forwardRef5 }, (props2) => React2.createElement(Primitive.div, { ...props2 }, children));
  }
);
Root$12.displayName = "SpeedSlider";
var speedSlider = Object.freeze({
  __proto__: null,
  Preview,
  Root: Root$12,
  Steps,
  Thumb,
  Track,
  TrackFill,
  Value
});
var Title = React2.forwardRef(({ children, ...props }, forwardRef5) => {
  const $title = useMediaState("title");
  return React2.createElement(Primitive.span, { ...props, ref: forwardRef5 }, $title, children);
});
Title.displayName = "Title";
function useActiveTextCues(track) {
  const [activeCues, setActiveCues] = React2.useState([]);
  React2.useEffect(() => {
    if (!track) {
      setActiveCues([]);
      return;
    }
    function onCuesChange() {
      if (track) setActiveCues(track.activeCues);
    }
    onCuesChange();
    return listenEvent(track, "cue-change", onCuesChange);
  }, [track]);
  return activeCues;
}
function useActiveTextTrack(kind) {
  const media = useMediaContext2(), [track, setTrack] = React2.useState(null);
  React2.useEffect(() => {
    return watchActiveTextTrack(media.textTracks, kind, setTrack);
  }, [kind]);
  return track;
}
function useChapterTitle() {
  var _a;
  const $track = useActiveTextTrack("chapters"), $cues = useActiveTextCues($track);
  return ((_a = $cues[0]) == null ? void 0 : _a.text) || "";
}
var ChapterTitle2 = React2.forwardRef(
  ({ defaultText = "", children, ...props }, forwardRef5) => {
    const $chapterTitle = useChapterTitle();
    return React2.createElement(Primitive.span, { ...props, ref: forwardRef5 }, $chapterTitle || defaultText, children);
  }
);
ChapterTitle2.displayName = "ChapterTitle";
var CaptionsBridge = createReactComponent(CaptionsInstance);
var Captions = React2.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React2.createElement(CaptionsBridge, { ...props, ref: forwardRef5 }, (props2) => React2.createElement(Primitive.div, { ...props2 }, children));
  }
);
Captions.displayName = "Captions";
var Root2 = React2.forwardRef(
  ({ size = 96, children, ...props }, forwardRef5) => {
    return React2.createElement(
      "svg",
      {
        width: size,
        height: size,
        fill: "none",
        viewBox: "0 0 120 120",
        "aria-hidden": "true",
        "data-part": "root",
        ...props,
        ref: forwardRef5
      },
      children
    );
  }
);
var Track2 = React2.forwardRef(
  ({ width = 8, children, ...props }, ref) => React2.createElement(
    "circle",
    {
      cx: "60",
      cy: "60",
      r: "54",
      stroke: "currentColor",
      strokeWidth: width,
      "data-part": "track",
      ...props,
      ref
    },
    children
  )
);
var TrackFill2 = React2.forwardRef(
  ({ width = 8, fillPercent = 50, children, ...props }, ref) => React2.createElement(
    "circle",
    {
      cx: "60",
      cy: "60",
      r: "54",
      stroke: "currentColor",
      pathLength: "100",
      strokeWidth: width,
      strokeDasharray: 100,
      strokeDashoffset: 100 - fillPercent,
      "data-part": "track-fill",
      ...props,
      ref
    },
    children
  )
);
var spinner = Object.freeze({
  __proto__: null,
  Root: Root2,
  Track: Track2,
  TrackFill: TrackFill2
});
function createSignal(initialValue, deps = []) {
  const scope = useReactScope();
  return React2.useMemo(() => scoped(() => signal(initialValue), scope), [scope, ...deps]);
}
function useScoped(compute) {
  const scope = useReactScope();
  return React2.useMemo(() => scoped(compute, scope), [scope]);
}
function useTextCues(track) {
  const [cues, setCues] = React2.useState([]);
  React2.useEffect(() => {
    if (!track) return;
    function onCuesChange() {
      if (track) setCues([...track.cues]);
    }
    const events = new EventsController(track).add("add-cue", onCuesChange).add("remove-cue", onCuesChange);
    onCuesChange();
    return () => {
      setCues([]);
      events.abort();
    };
  }, [track]);
  return cues;
}
function useChapterOptions() {
  const media = useMediaContext2(), track = useActiveTextTrack("chapters"), cues = useTextCues(track), $startTime = useSignal(media.$state.seekableStart), $endTime = useSignal(media.$state.seekableEnd);
  useActiveTextCues(track);
  return React2.useMemo(() => {
    const options = track ? cues.filter((cue) => cue.startTime <= $endTime && cue.endTime >= $startTime).map((cue, i) => {
      let currentRef = null, stopProgressEffect;
      return {
        cue,
        label: cue.text,
        value: i.toString(),
        startTimeText: formatTime(Math.max(0, cue.startTime - $startTime)),
        durationText: formatSpokenTime(
          Math.min($endTime, cue.endTime) - Math.max($startTime, cue.startTime)
        ),
        get selected() {
          return cue === track.activeCues[0];
        },
        setProgressVar(ref) {
          if (!ref || cue !== track.activeCues[0]) {
            stopProgressEffect == null ? void 0 : stopProgressEffect();
            stopProgressEffect = void 0;
            ref == null ? void 0 : ref.style.setProperty("--progress", "0%");
            currentRef = null;
            return;
          }
          if (currentRef === ref) return;
          currentRef = ref;
          stopProgressEffect == null ? void 0 : stopProgressEffect();
          stopProgressEffect = effect(() => {
            const { realCurrentTime } = media.$state, time = realCurrentTime(), cueStartTime = Math.max($startTime, cue.startTime), duration = Math.min($endTime, cue.endTime) - cueStartTime, progress = Math.max(0, time - cueStartTime) / duration * 100;
            ref.style.setProperty("--progress", progress.toFixed(3) + "%");
          });
        },
        select(trigger) {
          media.remote.seek(cue.startTime - $startTime, trigger);
        }
      };
    }) : [];
    Object.defineProperty(options, "selectedValue", {
      get() {
        const index = options.findIndex((option) => option.selected);
        return (index >= 0 ? index : 0).toString();
      }
    });
    return options;
  }, [cues, $startTime, $endTime]);
}

// node_modules/@vidstack/react/dev/chunks/vidstack-CBF7iUqu.js
var React3 = __toESM(require_react(), 1);
var Icon = React3.forwardRef((props, ref) => {
  const { width, height, size = null, paths, ...restProps } = props;
  return React3.createElement("svg", {
    viewBox: "0 0 32 32",
    ...restProps,
    width: width ?? size,
    height: height ?? size,
    fill: "none",
    "aria-hidden": "true",
    focusable: "false",
    xmlns: "http://www.w3.org/2000/svg",
    ref,
    dangerouslySetInnerHTML: { __html: paths }
  });
});
Icon.displayName = "VidstackIcon";

// node_modules/@vidstack/react/dev/chunks/vidstack-C4TlCb-3.js
var React4 = __toESM(require_react(), 1);
var DEFAULT_PLAYBACK_RATES = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];
var _media, _menu, _controller, _SpeedRadioGroup_instances, watchValue_fn, watchHintText_fn, watchControllerDisabled_fn, getValue_fn, onValueChange_fn;
var SpeedRadioGroup = class extends Component {
  constructor() {
    super();
    __privateAdd(this, _SpeedRadioGroup_instances);
    __privateAdd(this, _media);
    __privateAdd(this, _menu);
    __privateAdd(this, _controller);
    __privateSet(this, _controller, new RadioGroupController());
    __privateGet(this, _controller).onValueChange = __privateMethod(this, _SpeedRadioGroup_instances, onValueChange_fn).bind(this);
  }
  get value() {
    return __privateGet(this, _controller).value;
  }
  get disabled() {
    const { rates } = this.$props, { canSetPlaybackRate } = __privateGet(this, _media).$state;
    return !canSetPlaybackRate() || rates().length === 0;
  }
  onSetup() {
    __privateSet(this, _media, useMediaContext());
    if (hasProvidedContext(menuContext)) {
      __privateSet(this, _menu, useContext(menuContext));
    }
  }
  onConnect(el) {
    effect(__privateMethod(this, _SpeedRadioGroup_instances, watchValue_fn).bind(this));
    effect(__privateMethod(this, _SpeedRadioGroup_instances, watchHintText_fn).bind(this));
    effect(__privateMethod(this, _SpeedRadioGroup_instances, watchControllerDisabled_fn).bind(this));
  }
  getOptions() {
    const { rates, normalLabel } = this.$props;
    return rates().map((rate) => ({
      label: rate === 1 ? normalLabel : rate + "×",
      value: rate.toString()
    }));
  }
};
_media = new WeakMap();
_menu = new WeakMap();
_controller = new WeakMap();
_SpeedRadioGroup_instances = new WeakSet();
watchValue_fn = function() {
  __privateGet(this, _controller).value = __privateMethod(this, _SpeedRadioGroup_instances, getValue_fn).call(this);
};
watchHintText_fn = function() {
  var _a;
  const { normalLabel } = this.$props, { playbackRate } = __privateGet(this, _media).$state, rate = playbackRate();
  (_a = __privateGet(this, _menu)) == null ? void 0 : _a.hint.set(rate === 1 ? normalLabel() : rate + "×");
};
watchControllerDisabled_fn = function() {
  var _a;
  (_a = __privateGet(this, _menu)) == null ? void 0 : _a.disable(this.disabled);
};
getValue_fn = function() {
  const { playbackRate } = __privateGet(this, _media).$state;
  return playbackRate().toString();
};
onValueChange_fn = function(value, trigger) {
  if (this.disabled) return;
  const rate = +value;
  __privateGet(this, _media).remote.changePlaybackRate(rate, trigger);
  this.dispatch("change", { detail: rate, trigger });
};
__publicField(SpeedRadioGroup, "props", {
  normalLabel: "Normal",
  rates: DEFAULT_PLAYBACK_RATES
});
var speedradiogroup__proto = SpeedRadioGroup.prototype;
prop(speedradiogroup__proto, "value");
prop(speedradiogroup__proto, "disabled");
method(speedradiogroup__proto, "getOptions");
function useMediaRemote(target) {
  const media = useMediaContext2(), remote = React4.useRef();
  if (!remote.current) {
    remote.current = new MediaRemoteControl();
  }
  React4.useEffect(() => {
    const ref = target && "current" in target ? target.current : target, isPlayerRef = ref instanceof MediaPlayerInstance, player = isPlayerRef ? ref : media == null ? void 0 : media.player;
    remote.current.setPlayer(player ?? null);
    remote.current.setTarget(ref ?? null);
  }, [media, target && "current" in target ? target.current : target]);
  return remote.current;
}
function useVideoQualityOptions({
  auto = true,
  sort = "descending"
} = {}) {
  const media = useMediaContext2(), { qualities, quality, autoQuality, canSetQuality } = media.$state, $qualities = useSignal(qualities);
  useSignal(quality);
  useSignal(autoQuality);
  useSignal(canSetQuality);
  return React4.useMemo(() => {
    const sortedQualities = sortVideoQualities($qualities, sort === "descending"), options = sortedQualities.map((q) => {
      return {
        quality: q,
        label: q.height + "p",
        value: getQualityValue(q),
        bitrateText: q.bitrate && q.bitrate > 0 ? `${(q.bitrate / 1e6).toFixed(2)} Mbps` : null,
        get selected() {
          return q === quality();
        },
        get autoSelected() {
          return autoQuality();
        },
        select(trigger) {
          const index = qualities().indexOf(q);
          if (index >= 0) media.remote.changeQuality(index, trigger);
        }
      };
    });
    if (auto) {
      options.unshift({
        quality: null,
        label: isString(auto) ? auto : "Auto",
        value: "auto",
        bitrateText: null,
        get selected() {
          return autoQuality();
        },
        get autoSelected() {
          return autoQuality();
        },
        select(trigger) {
          media.remote.requestAutoQuality(trigger);
        }
      });
    }
    Object.defineProperty(options, "disabled", {
      get() {
        return !canSetQuality() || $qualities.length <= 1;
      }
    });
    Object.defineProperty(options, "selectedQuality", {
      get() {
        return quality();
      }
    });
    Object.defineProperty(options, "selectedValue", {
      get() {
        const $quality = quality();
        return !autoQuality() && $quality ? getQualityValue($quality) : "auto";
      }
    });
    return options;
  }, [$qualities, sort]);
}
function getQualityValue(quality) {
  return quality.height + "_" + quality.bitrate;
}
function usePlaybackRateOptions({
  rates = DEFAULT_PLAYBACK_RATES,
  normalLabel = "Normal"
} = {}) {
  const media = useMediaContext2(), { playbackRate, canSetPlaybackRate } = media.$state;
  useSignal(playbackRate);
  useSignal(canSetPlaybackRate);
  return React4.useMemo(() => {
    const options = rates.map((opt) => {
      const label = typeof opt === "number" ? opt === 1 && normalLabel ? normalLabel : opt + "x" : opt.label, rate = typeof opt === "number" ? opt : opt.rate;
      return {
        label,
        value: rate.toString(),
        rate,
        get selected() {
          return playbackRate() === rate;
        },
        select(trigger) {
          media.remote.changePlaybackRate(rate, trigger);
        }
      };
    });
    Object.defineProperty(options, "disabled", {
      get() {
        return !canSetPlaybackRate() || !options.length;
      }
    });
    Object.defineProperty(options, "selectedValue", {
      get() {
        return playbackRate().toString();
      }
    });
    return options;
  }, [rates]);
}

// node_modules/@vidstack/react/dev/vidstack.js
var import_react_dom2 = __toESM(require_react_dom());
var _instance, _track, _typeRE, _LibASSTextRenderer_instances, freeTrack_fn;
var LibASSTextRenderer = class {
  constructor(loader, config) {
    __privateAdd(this, _LibASSTextRenderer_instances);
    __publicField(this, "priority", 1);
    __privateAdd(this, _instance, null);
    __privateAdd(this, _track, null);
    __privateAdd(this, _typeRE, /(ssa|ass)$/);
    this.loader = loader;
    this.config = config;
  }
  canRender(track, video) {
    return !!video && !!track.src && (isString(track.type) && __privateGet(this, _typeRE).test(track.type) || __privateGet(this, _typeRE).test(track.src));
  }
  attach(video) {
    if (!video) return;
    this.loader().then(async (mod) => {
      var _a;
      __privateSet(this, _instance, new mod.default({
        ...this.config,
        video,
        subUrl: ((_a = __privateGet(this, _track)) == null ? void 0 : _a.src) || ""
      }));
      new EventsController(__privateGet(this, _instance)).add("ready", () => {
        var _a2;
        const canvas = (_a2 = __privateGet(this, _instance)) == null ? void 0 : _a2._canvas;
        if (canvas) canvas.style.pointerEvents = "none";
      }).add("error", (event) => {
        if (!__privateGet(this, _track)) return;
        __privateGet(this, _track)[TextTrackSymbol.readyState] = 3;
        __privateGet(this, _track).dispatchEvent(
          new DOMEvent("error", {
            trigger: event,
            detail: event.error
          })
        );
      });
    });
  }
  changeTrack(track) {
    var _a;
    if (!track || track.readyState === 3) {
      __privateMethod(this, _LibASSTextRenderer_instances, freeTrack_fn).call(this);
    } else if (__privateGet(this, _track) !== track) {
      (_a = __privateGet(this, _instance)) == null ? void 0 : _a.setTrackByUrl(track.src);
      __privateSet(this, _track, track);
    }
  }
  detach() {
    __privateMethod(this, _LibASSTextRenderer_instances, freeTrack_fn).call(this);
  }
};
_instance = new WeakMap();
_track = new WeakMap();
_typeRE = new WeakMap();
_LibASSTextRenderer_instances = new WeakSet();
freeTrack_fn = function() {
  var _a;
  (_a = __privateGet(this, _instance)) == null ? void 0 : _a.freeTrack();
  __privateSet(this, _track, null);
};
var DEFAULT_AUDIO_GAINS = [1, 1.25, 1.5, 1.75, 2, 2.5, 3, 4];
var _media2, _menu2, _controller2, _AudioGainRadioGroup_instances, watchValue_fn2, watchHintText_fn2, watchControllerDisabled_fn2, getValue_fn2, onValueChange_fn2;
var AudioGainRadioGroup = class extends Component {
  constructor() {
    super();
    __privateAdd(this, _AudioGainRadioGroup_instances);
    __privateAdd(this, _media2);
    __privateAdd(this, _menu2);
    __privateAdd(this, _controller2);
    __privateSet(this, _controller2, new RadioGroupController());
    __privateGet(this, _controller2).onValueChange = __privateMethod(this, _AudioGainRadioGroup_instances, onValueChange_fn2).bind(this);
  }
  get value() {
    return __privateGet(this, _controller2).value;
  }
  get disabled() {
    const { gains } = this.$props, { canSetAudioGain } = __privateGet(this, _media2).$state;
    return !canSetAudioGain() || gains().length === 0;
  }
  onSetup() {
    __privateSet(this, _media2, useMediaContext());
    if (hasProvidedContext(menuContext)) {
      __privateSet(this, _menu2, useContext(menuContext));
    }
  }
  onConnect(el) {
    effect(__privateMethod(this, _AudioGainRadioGroup_instances, watchValue_fn2).bind(this));
    effect(__privateMethod(this, _AudioGainRadioGroup_instances, watchHintText_fn2).bind(this));
    effect(__privateMethod(this, _AudioGainRadioGroup_instances, watchControllerDisabled_fn2).bind(this));
  }
  getOptions() {
    const { gains, normalLabel } = this.$props;
    return gains().map((gain) => ({
      label: gain === 1 || gain === null ? normalLabel : String(gain * 100) + "%",
      value: gain.toString()
    }));
  }
};
_media2 = new WeakMap();
_menu2 = new WeakMap();
_controller2 = new WeakMap();
_AudioGainRadioGroup_instances = new WeakSet();
watchValue_fn2 = function() {
  __privateGet(this, _controller2).value = __privateMethod(this, _AudioGainRadioGroup_instances, getValue_fn2).call(this);
};
watchHintText_fn2 = function() {
  var _a;
  const { normalLabel } = this.$props, { audioGain } = __privateGet(this, _media2).$state, gain = audioGain();
  (_a = __privateGet(this, _menu2)) == null ? void 0 : _a.hint.set(gain === 1 || gain == null ? normalLabel() : String(gain * 100) + "%");
};
watchControllerDisabled_fn2 = function() {
  var _a;
  (_a = __privateGet(this, _menu2)) == null ? void 0 : _a.disable(this.disabled);
};
getValue_fn2 = function() {
  var _a;
  const { audioGain } = __privateGet(this, _media2).$state;
  return ((_a = audioGain()) == null ? void 0 : _a.toString()) ?? "1";
};
onValueChange_fn2 = function(value, trigger) {
  if (this.disabled) return;
  const gain = +value;
  __privateGet(this, _media2).remote.changeAudioGain(gain, trigger);
  this.dispatch("change", { detail: gain, trigger });
};
__publicField(AudioGainRadioGroup, "props", {
  normalLabel: "Disabled",
  gains: DEFAULT_AUDIO_GAINS
});
var audiogainradiogroup__proto = AudioGainRadioGroup.prototype;
prop(audiogainradiogroup__proto, "value");
prop(audiogainradiogroup__proto, "disabled");
method(audiogainradiogroup__proto, "getOptions");
var playerCallbacks = [
  "onAbort",
  "onAudioTrackChange",
  "onAudioTracksChange",
  "onAutoPlay",
  "onAutoPlayChange",
  "onAutoPlayFail",
  "onCanLoad",
  "onCanPlay",
  "onCanPlayThrough",
  "onControlsChange",
  "onDestroy",
  "onDurationChange",
  "onEmptied",
  "onEnd",
  "onEnded",
  "onError",
  "onFindMediaPlayer",
  "onFullscreenChange",
  "onFullscreenError",
  "onLiveChange",
  "onLiveEdgeChange",
  "onLoadedData",
  "onLoadedMetadata",
  "onLoadStart",
  "onLoopChange",
  "onOrientationChange",
  "onPause",
  "onPictureInPictureChange",
  "onPictureInPictureError",
  "onPlay",
  "onPlayFail",
  "onPlaying",
  "onPlaysInlineChange",
  "onPosterChange",
  "onProgress",
  "onProviderChange",
  "onProviderLoaderChange",
  "onProviderSetup",
  "onQualitiesChange",
  "onQualityChange",
  "onRateChange",
  "onReplay",
  "onSeeked",
  "onSeeking",
  "onSourceChange",
  "onSourceChange",
  "onStalled",
  "onStarted",
  "onStreamTypeChange",
  "onSuspend",
  "onTextTrackChange",
  "onTextTracksChange",
  "onTimeUpdate",
  "onTitleChange",
  "onVdsLog",
  "onVideoPresentationChange",
  "onVolumeChange",
  "onWaiting"
];
var MediaPlayerBridge = createReactComponent(MediaPlayerInstance, {
  events: playerCallbacks,
  eventsRegex: /^onHls/,
  domEventsRegex: /^onMedia/
});
var MediaPlayer = React5.forwardRef(
  ({ aspectRatio, children, ...props }, forwardRef5) => {
    return React5.createElement(
      MediaPlayerBridge,
      {
        ...props,
        src: props.src,
        ref: forwardRef5,
        style: {
          aspectRatio,
          ...props.style
        }
      },
      (props2) => React5.createElement(Primitive.div, { ...props2 }, children)
    );
  }
);
MediaPlayer.displayName = "MediaPlayer";
var MediaProviderBridge = createReactComponent(MediaProviderInstance);
var MediaProvider = React5.forwardRef(
  ({ loaders = [], children, iframeProps, mediaProps, ...props }, forwardRef5) => {
    const reactLoaders = React5.useMemo(() => loaders.map((Loader) => new Loader()), loaders);
    return React5.createElement(MediaProviderBridge, { ...props, loaders: reactLoaders, ref: forwardRef5 }, (props2, instance) => React5.createElement("div", { ...props2 }, React5.createElement(MediaOutlet, { provider: instance, mediaProps, iframeProps }), children));
  }
);
MediaProvider.displayName = "MediaProvider";
function MediaOutlet({ provider, mediaProps, iframeProps }) {
  const { sources, crossOrigin, poster, remotePlaybackInfo, nativeControls, viewType } = useStateContext(mediaState), { loader } = provider.$state, { $provider: $$provider, $providerSetup: $$providerSetup } = useMediaContext2(), $sources = useSignal(sources), $nativeControls = useSignal(nativeControls), $crossOrigin = useSignal(crossOrigin), $poster = useSignal(poster), $loader = useSignal(loader), $provider = useSignal($$provider), $providerSetup = useSignal($$providerSetup), $remoteInfo = useSignal(remotePlaybackInfo), $mediaType = $loader == null ? void 0 : $loader.mediaType(), $viewType = useSignal(viewType), isAudioView = $viewType === "audio", isYouTubeEmbed = ($loader == null ? void 0 : $loader.name) === "youtube", isVimeoEmbed = ($loader == null ? void 0 : $loader.name) === "vimeo", isEmbed = isYouTubeEmbed || isVimeoEmbed, isRemotion = ($loader == null ? void 0 : $loader.name) === "remotion", isGoogleCast = ($loader == null ? void 0 : $loader.name) === "google-cast", [googleCastIconPaths, setGoogleCastIconPaths] = React5.useState(""), [hasMounted, setHasMounted] = React5.useState(false);
  React5.useEffect(() => {
    if (!isGoogleCast || googleCastIconPaths) return;
    import("./vidstack-CH225ns1-M354VZIU.js").then(function(n) {
      return n.chromecast;
    }).then((mod) => {
      setGoogleCastIconPaths(mod.default);
    });
  }, [isGoogleCast]);
  React5.useEffect(() => {
    setHasMounted(true);
  }, []);
  if (isGoogleCast) {
    return React5.createElement(
      "div",
      {
        className: "vds-google-cast",
        ref: (el) => {
          provider.load(el);
        }
      },
      React5.createElement(Icon, { paths: googleCastIconPaths }),
      ($remoteInfo == null ? void 0 : $remoteInfo.deviceName) ? React5.createElement("span", { className: "vds-google-cast-info" }, "Google Cast on", " ", React5.createElement("span", { className: "vds-google-cast-device-name" }, $remoteInfo.deviceName)) : null
    );
  }
  if (isRemotion) {
    return React5.createElement("div", { "data-remotion-canvas": true }, React5.createElement(
      "div",
      {
        "data-remotion-container": true,
        ref: (el) => {
          provider.load(el);
        }
      },
      isRemotionProvider($provider) && $providerSetup ? React5.createElement($provider.render) : null
    ));
  }
  return isEmbed ? React5.createElement(
    React5.Fragment,
    null,
    React5.createElement("iframe", {
      ...iframeProps,
      className: ((iframeProps == null ? void 0 : iframeProps.className) ? `${iframeProps.className} ` : "") + isYouTubeEmbed ? "vds-youtube" : "vds-vimeo",
      suppressHydrationWarning: true,
      tabIndex: !$nativeControls ? -1 : void 0,
      "aria-hidden": "true",
      "data-no-controls": !$nativeControls ? "" : void 0,
      ref(el) {
        provider.load(el);
      }
    }),
    !$nativeControls && !isAudioView ? React5.createElement("div", { className: "vds-blocker" }) : null
  ) : $mediaType ? React5.createElement($mediaType === "audio" ? "audio" : "video", {
    ...mediaProps,
    controls: $nativeControls ? true : null,
    crossOrigin: typeof $crossOrigin === "boolean" ? "" : $crossOrigin,
    poster: $mediaType === "video" && $nativeControls && $poster ? $poster : null,
    suppressHydrationWarning: true,
    children: !hasMounted ? $sources.map(
      ({ src, type }) => isString(src) ? React5.createElement("source", { src, type: type !== "?" ? type : void 0, key: src }) : null
    ) : null,
    ref(el) {
      provider.load(el);
    }
  }) : null;
}
MediaOutlet.displayName = "MediaOutlet";
function createTextTrack(init) {
  const media = useMediaContext2(), track = React5.useMemo(() => new TextTrack(init), Object.values(init));
  React5.useEffect(() => {
    media.textTracks.add(track);
    return () => void media.textTracks.remove(track);
  }, [track]);
  return track;
}
function Track3({ lang, ...props }) {
  createTextTrack({ language: lang, ...props });
  return null;
}
Track3.displayName = "Track";
var ToggleButtonBridge = createReactComponent(ToggleButtonInstance);
var ToggleButton = React5.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React5.createElement(ToggleButtonBridge, { ...props }, (props2) => React5.createElement(
      Primitive.button,
      {
        ...props2,
        ref: composeRefs(props2.ref, forwardRef5)
      },
      children
    ));
  }
);
ToggleButton.displayName = "ToggleButton";
var PosterBridge = createReactComponent(PosterInstance);
var Poster = React5.forwardRef(
  ({ children, ...props }, forwardRef5) => {
    return React5.createElement(
      PosterBridge,
      {
        src: props.asChild && React5.isValidElement(children) ? children.props.src : void 0,
        ...props
      },
      (props2, instance) => React5.createElement(
        PosterImg,
        {
          ...props2,
          instance,
          ref: composeRefs(props2.ref, forwardRef5)
        },
        children
      )
    );
  }
);
Poster.displayName = "Poster";
var PosterImg = React5.forwardRef(
  ({ instance, children, ...props }, forwardRef5) => {
    const { src, img, alt, crossOrigin, hidden } = instance.$state, $src = useSignal(src), $alt = useSignal(alt), $crossOrigin = useSignal(crossOrigin), $hidden = useSignal(hidden);
    return React5.createElement(
      Primitive.img,
      {
        ...props,
        src: $src || void 0,
        alt: $alt || void 0,
        crossOrigin: $crossOrigin || void 0,
        ref: composeRefs(img.set, forwardRef5),
        style: { display: $hidden ? "none" : void 0 }
      },
      children
    );
  }
);
PosterImg.displayName = "PosterImg";
var Root3 = React5.forwardRef(({ children, ...props }, forwardRef5) => {
  return React5.createElement(
    Primitive.div,
    {
      translate: "yes",
      "aria-live": "off",
      "aria-atomic": "true",
      ...props,
      ref: forwardRef5
    },
    children
  );
});
Root3.displayName = "Caption";
var Text = React5.forwardRef((props, forwardRef5) => {
  const textTrack = useMediaState("textTrack"), [activeCue, setActiveCue] = React5.useState();
  React5.useEffect(() => {
    if (!textTrack) return;
    function onCueChange() {
      setActiveCue(textTrack == null ? void 0 : textTrack.activeCues[0]);
    }
    textTrack.addEventListener("cue-change", onCueChange);
    return () => {
      textTrack.removeEventListener("cue-change", onCueChange);
      setActiveCue(void 0);
    };
  }, [textTrack]);
  return React5.createElement(
    Primitive.span,
    {
      ...props,
      "data-part": "cue",
      dangerouslySetInnerHTML: {
        __html: (activeCue == null ? void 0 : activeCue.text) || ""
      },
      ref: forwardRef5
    }
  );
});
Text.displayName = "CaptionText";
var caption = Object.freeze({
  __proto__: null,
  Root: Root3,
  Text
});
function useState4(ctor, prop2, ref) {
  const initialValue = React5.useMemo(() => ctor.state.record[prop2], [ctor, prop2]);
  return useSignal(ref.current ? ref.current.$state[prop2] : initialValue);
}
var storesCache = /* @__PURE__ */ new Map();
function useStore(ctor, ref) {
  const initialStore = React5.useMemo(() => {
    let store = storesCache.get(ctor);
    if (!store) {
      store = new Proxy(ctor.state.record, {
        get: (_, prop2) => () => ctor.state.record[prop2]
      });
      storesCache.set(ctor, store);
    }
    return store;
  }, [ctor]);
  return useSignalRecord(ref.current ? ref.current.$state : initialStore);
}
function useMediaProvider() {
  const [provider, setProvider] = React5.useState(null), context = useMediaContext2();
  if (!context) {
    throw Error(
      "[vidstack] no media context was found - was this called outside of `<MediaPlayer>`?"
    );
  }
  React5.useEffect(() => {
    if (!context) return;
    return effect(() => {
      setProvider(context.$provider());
    });
  }, []);
  return provider;
}
function useThumbnails(src, crossOrigin = null) {
  const scope = useReactScope(), $src = createSignal(src), $crossOrigin = createSignal(crossOrigin), loader = useScoped(() => ThumbnailsLoader.create($src, $crossOrigin));
  if (!scope) {
    console.warn(
      `[vidstack] \`useThumbnails\` must be called inside a child component of \`<MediaPlayer>\``
    );
  }
  React5.useEffect(() => {
    $src.set(src);
  }, [src]);
  React5.useEffect(() => {
    $crossOrigin.set(crossOrigin);
  }, [crossOrigin]);
  return useSignal(loader.$images);
}
function useActiveThumbnail(thumbnails, time) {
  return React5.useMemo(() => {
    let activeIndex = -1;
    for (let i = thumbnails.length - 1; i >= 0; i--) {
      const image = thumbnails[i];
      if (time >= image.startTime && (!image.endTime || time < image.endTime)) {
        activeIndex = i;
        break;
      }
    }
    return thumbnails[activeIndex] || null;
  }, [thumbnails, time]);
}
function useSliderPreview({
  clamp = false,
  offset = 0,
  orientation = "horizontal"
} = {}) {
  const [rootRef, setRootRef] = React5.useState(null), [previewRef, setPreviewRef] = React5.useState(null), [pointerValue, setPointerValue] = React5.useState(0), [isVisible, setIsVisible] = React5.useState(false);
  React5.useEffect(() => {
    if (!rootRef) return;
    const dragging = signal(false);
    function updatePointerValue(event) {
      if (!rootRef) return;
      setPointerValue(getPointerValue(rootRef, event, orientation));
    }
    return effect(() => {
      if (!dragging()) {
        new EventsController(rootRef).add("pointerenter", () => {
          setIsVisible(true);
          previewRef == null ? void 0 : previewRef.setAttribute("data-visible", "");
        }).add("pointerdown", (event) => {
          dragging.set(true);
          updatePointerValue(event);
        }).add("pointerleave", () => {
          setIsVisible(false);
          previewRef == null ? void 0 : previewRef.removeAttribute("data-visible");
        }).add("pointermove", updatePointerValue);
      }
      previewRef == null ? void 0 : previewRef.setAttribute("data-dragging", "");
      new EventsController(document).add("pointerup", (event) => {
        dragging.set(false);
        previewRef == null ? void 0 : previewRef.removeAttribute("data-dragging");
        updatePointerValue(event);
      }).add("pointermove", updatePointerValue).add("touchmove", (e) => e.preventDefault(), { passive: false });
    });
  }, [rootRef]);
  React5.useEffect(() => {
    if (previewRef) {
      previewRef.style.setProperty("--slider-pointer", pointerValue + "%");
    }
  }, [previewRef, pointerValue]);
  React5.useEffect(() => {
    if (!previewRef) return;
    const update = () => {
      updateSliderPreviewPlacement(previewRef, {
        offset,
        clamp,
        orientation
      });
    };
    update();
    const resize = new ResizeObserver(update);
    resize.observe(previewRef);
    return () => resize.disconnect();
  }, [previewRef, clamp, offset, orientation]);
  return {
    previewRootRef: setRootRef,
    previewRef: setPreviewRef,
    previewValue: pointerValue,
    isPreviewVisible: isVisible
  };
}
function getPointerValue(root, event, orientation) {
  let thumbPositionRate, rect = root.getBoundingClientRect();
  if (orientation === "vertical") {
    const { bottom: trackBottom, height: trackHeight } = rect;
    thumbPositionRate = (trackBottom - event.clientY) / trackHeight;
  } else {
    const { left: trackLeft, width: trackWidth } = rect;
    thumbPositionRate = (event.clientX - trackLeft) / trackWidth;
  }
  return round(Math.max(0, Math.min(100, 100 * thumbPositionRate)));
}
function round(num) {
  return Number(num.toFixed(3));
}
function useAudioGainOptions({
  gains = DEFAULT_AUDIO_GAINS,
  disabledLabel = "disabled"
} = {}) {
  const media = useMediaContext2(), { audioGain, canSetAudioGain } = media.$state;
  useSignal(audioGain);
  useSignal(canSetAudioGain);
  return React5.useMemo(() => {
    const options = gains.map((opt) => {
      const label = typeof opt === "number" ? opt === 1 && disabledLabel ? disabledLabel : opt * 100 + "%" : opt.label, gain = typeof opt === "number" ? opt : opt.gain;
      return {
        label,
        value: gain.toString(),
        gain,
        get selected() {
          return audioGain() === gain;
        },
        select(trigger) {
          media.remote.changeAudioGain(gain, trigger);
        }
      };
    });
    Object.defineProperty(options, "disabled", {
      get() {
        return !canSetAudioGain() || !options.length;
      }
    });
    Object.defineProperty(options, "selectedValue", {
      get() {
        var _a;
        return (_a = audioGain()) == null ? void 0 : _a.toString();
      }
    });
    return options;
  }, [gains]);
}
export {
  ARIAKeyShortcuts,
  AUDIO_EXTENSIONS,
  AUDIO_TYPES,
  AirPlayButton,
  AirPlayButtonInstance,
  audioGainSlider as AudioGainSlider,
  AudioGainSliderInstance,
  AudioProviderLoader,
  AudioTrackList,
  caption as Caption,
  CaptionButton,
  CaptionButtonInstance,
  Captions,
  CaptionsInstance,
  ChapterTitle2 as ChapterTitle,
  controls as Controls,
  ControlsGroupInstance,
  ControlsInstance,
  DASHProviderLoader,
  DASH_VIDEO_EXTENSIONS,
  DASH_VIDEO_TYPES,
  DEFAULT_AUDIO_GAINS,
  DEFAULT_PLAYBACK_RATES,
  FullscreenButton,
  FullscreenButtonInstance,
  FullscreenController,
  Gesture,
  GestureInstance,
  GoogleCastButton,
  GoogleCastButtonInstance,
  HLSProviderLoader,
  HLS_VIDEO_EXTENSIONS,
  HLS_VIDEO_TYPES,
  Icon,
  LibASSTextRenderer,
  List,
  LiveButton,
  LiveButtonInstance,
  LocalMediaStorage,
  Logger,
  MEDIA_KEY_SHORTCUTS,
  MediaAnnouncer,
  MediaAnnouncerInstance,
  MediaControls,
  MediaPlayer,
  MediaPlayerInstance,
  MediaProvider,
  MediaProviderInstance,
  MediaRemoteControl,
  menu as Menu,
  MenuButtonInstance,
  MenuInstance,
  MenuItemInstance,
  MenuItemsInstance,
  MenuPortalInstance,
  MuteButton,
  MuteButtonInstance,
  PIPButton,
  PIPButtonInstance,
  PlayButton,
  PlayButtonInstance,
  Poster,
  PosterInstance,
  qualitySlider as QualitySlider,
  QualitySliderInstance,
  radioGroup as RadioGroup,
  RadioGroupInstance,
  RadioInstance,
  ScreenOrientationController,
  SeekButton,
  SeekButtonInstance,
  slider as Slider,
  SliderChaptersInstance,
  SliderInstance,
  SliderPreviewInstance,
  SliderThumbnailInstance,
  SliderValueInstance,
  SliderVideoInstance,
  speedSlider as SpeedSlider,
  SpeedSliderInstance,
  spinner as Spinner,
  TextRenderers,
  TextTrack,
  TextTrackList,
  thumbnail as Thumbnail,
  ThumbnailInstance,
  Time,
  TimeInstance,
  TimeRange,
  timeSlider as TimeSlider,
  TimeSliderInstance,
  Title,
  ToggleButton,
  ToggleButtonInstance,
  tooltip as Tooltip,
  TooltipContentInstance,
  TooltipInstance,
  TooltipTriggerInstance,
  Track3 as Track,
  VIDEO_EXTENSIONS,
  VIDEO_TYPES,
  VideoProviderLoader,
  VideoQualityList,
  VimeoProviderLoader,
  volumeSlider as VolumeSlider,
  VolumeSliderInstance,
  YouTubeProviderLoader,
  appendTriggerEvent,
  boundTime,
  canChangeVolume,
  canFullscreen,
  canGoogleCastSrc,
  canOrientScreen,
  canPlayHLSNatively,
  canRotateScreen,
  canUsePictureInPicture,
  canUseVideoPresentation,
  createTextTrack,
  findActiveCue,
  findTriggerEvent,
  formatSpokenTime,
  formatTime,
  getDownloadFile,
  getTimeRangesEnd,
  getTimeRangesStart,
  hasTriggerEvent,
  isAudioProvider,
  isAudioSrc,
  isCueActive,
  isDASHProvider,
  isDASHSrc,
  isGoogleCastProvider,
  isHLSProvider,
  isHLSSrc,
  isHTMLAudioElement,
  isHTMLIFrameElement,
  isHTMLMediaElement,
  isHTMLVideoElement,
  isKeyboardClick,
  isKeyboardEvent,
  isMediaStream,
  isPointerEvent,
  isTrackCaptionKind,
  isVideoProvider,
  isVideoQualitySrc,
  isVideoSrc,
  isVimeoProvider,
  isYouTubeProvider,
  mediaContext,
  mediaState,
  normalizeTimeIntervals,
  parseJSONCaptionsFile,
  sliderState,
  softResetMediaState,
  sortVideoQualities,
  updateTimeIntervals,
  useActiveTextCues,
  useActiveTextTrack,
  useActiveThumbnail,
  useAudioGainOptions,
  useAudioOptions,
  useCaptionOptions,
  useChapterOptions,
  useChapterTitle,
  useMediaContext2 as useMediaContext,
  useMediaPlayer,
  useMediaProvider,
  useMediaRemote,
  useMediaState,
  useMediaStore,
  usePlaybackRateOptions,
  useSliderPreview,
  useSliderState,
  useSliderStore,
  useState4 as useState,
  useStore,
  useTextCues,
  useThumbnails,
  useVideoQualityOptions,
  walkTriggerEventChain,
  watchActiveTextTrack,
  watchCueTextChange
};
//# sourceMappingURL=@vidstack_react.js.map
