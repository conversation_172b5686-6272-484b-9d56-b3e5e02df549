{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "noEmitOnError": false, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": false, "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "exclude": ["src/tempobook", "src/stories"], "references": [{"path": "./tsconfig.node.json"}]}