"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isElementInSvg = exports.isSkipNavTreeNode = exports.isMovingElement = exports.getAllUnknownClasses = exports.getAllUnknownClasesFromList = exports.isOutline = exports.hasClass = exports.getElementKeyFromNode = exports.getUniquePathFromNode = exports.getCodebaseIdFromNode = exports.getCodebaseIdFromClassName = exports.validateUuid = exports.clearElementKeyForUniquePath = exports.setElementKeyForUniquePath = exports.getElementKeyForUniquePath = exports.clearNodeForElementKey = exports.setNodeForElementKey = exports.getNodeForElementKey = exports.KNOWN_ATTRIBUTES = exports.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD = exports.TEMPO_TEST_ID = exports.TEMPO_ELEMENT_ID = exports.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH = exports.TEMPO_OUTLINE_UNTIL_REFESH = exports.TEMPO_DELETE_AFTER_REFRESH = exports.TEMPO_INSTANT_UPDATE = exports.TEMPO_DELETE_AFTER_INSTANT_UPDATE = exports.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS = exports.TEMPO_INSTANT_UPDATE_STYLING_PREFIX = exports.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE = exports.TEMPO_INSTANT_DIV_DRAW_CLASS = exports.EDIT_TEXT_BUTTON = exports.OUTLINE_CLASS = void 0;
const changeItemFunctions_1 = require("./changeItemFunctions");
// Must match identifier utils on the frontend
exports.OUTLINE_CLASS = 'arb89-outline';
exports.EDIT_TEXT_BUTTON = 'arb89-edit-text-button';
exports.TEMPO_INSTANT_DIV_DRAW_CLASS = 'arb89-instant-div-draw';
exports.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE = 'arb89-move-between-parents-outline';
exports.TEMPO_INSTANT_UPDATE_STYLING_PREFIX = 'arb89-styling-';
exports.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS = 'arb89-display-none-until-refresh';
exports.TEMPO_DELETE_AFTER_INSTANT_UPDATE = 'arb89-delete-after-instant-update';
const KNOWN_CLASSES = new Set([
    exports.OUTLINE_CLASS,
    exports.TEMPO_INSTANT_DIV_DRAW_CLASS,
    exports.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE,
    exports.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS,
    changeItemFunctions_1.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID,
    changeItemFunctions_1.TEMPORARY_STYLING_CLASS_NAME,
    exports.EDIT_TEXT_BUTTON,
]);
const KNOWN_CLASS_PREFIXES = [
    exports.TEMPO_INSTANT_UPDATE_STYLING_PREFIX,
    changeItemFunctions_1.DUPLICATE_PLACEHOLDER_PREFIX,
    changeItemFunctions_1.ADD_JSX_PREFIX,
];
// Attributes that are set until the next Nav Tree Refresh
exports.TEMPO_INSTANT_UPDATE = 'arb89-instant-update';
exports.TEMPO_DELETE_AFTER_REFRESH = 'arb89-delete-after-refresh';
exports.TEMPO_OUTLINE_UNTIL_REFESH = 'arb89-outline-until-refresh';
exports.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH = 'arb89-do-not-show-in-nav';
exports.TEMPO_ELEMENT_ID = 'tempoelementid';
exports.TEMPO_TEST_ID = 'data-testid';
exports.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD = 'arb89-queue-delete-after-hot-reload';
exports.KNOWN_ATTRIBUTES = new Set([
    exports.TEMPO_INSTANT_UPDATE,
    exports.TEMPO_DELETE_AFTER_REFRESH,
    exports.TEMPO_DELETE_AFTER_INSTANT_UPDATE,
    exports.TEMPO_OUTLINE_UNTIL_REFESH,
    exports.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD,
    exports.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH,
    exports.TEMPO_ELEMENT_ID,
    exports.TEMPO_TEST_ID,
]);
let elementKeyToNodeMap = {};
const getNodeForElementKey = (elementKey) => {
    if (!elementKey)
        return null;
    return elementKeyToNodeMap[elementKey];
};
exports.getNodeForElementKey = getNodeForElementKey;
const setNodeForElementKey = (elementKey, node) => {
    elementKeyToNodeMap[elementKey] = node;
};
exports.setNodeForElementKey = setNodeForElementKey;
const clearNodeForElementKey = () => {
    elementKeyToNodeMap = {};
};
exports.clearNodeForElementKey = clearNodeForElementKey;
let uniquePathToElementKeyMap = {};
const getElementKeyForUniquePath = (uniquePath) => {
    return uniquePathToElementKeyMap[uniquePath] || null;
};
exports.getElementKeyForUniquePath = getElementKeyForUniquePath;
const setElementKeyForUniquePath = (uniquePath, elementKey) => {
    uniquePathToElementKeyMap[uniquePath] = elementKey;
};
exports.setElementKeyForUniquePath = setElementKeyForUniquePath;
const clearElementKeyForUniquePath = () => {
    uniquePathToElementKeyMap = {};
};
exports.clearElementKeyForUniquePath = clearElementKeyForUniquePath;
const validateUuid = (uuid) => {
    return new RegExp('^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$', 'i').test(uuid);
};
exports.validateUuid = validateUuid;
const getCodebaseIdFromClassName = (className) => {
    if (className &&
        className.startsWith('tempo-') &&
        ((0, exports.validateUuid)(className.substring('tempo-'.length)) ||
            className === changeItemFunctions_1.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID ||
            className.startsWith(changeItemFunctions_1.DUPLICATE_PLACEHOLDER_PREFIX))) {
        return className;
    }
    return null;
};
exports.getCodebaseIdFromClassName = getCodebaseIdFromClassName;
const getCodebaseIdFromNode = (node) => {
    var _a;
    if (!(node === null || node === void 0 ? void 0 : node.classList)) {
        return null;
    }
    let clsFound = null;
    node.classList.forEach((cls) => {
        // A bit of a hack -> in the case when there are multiple tempo codebase IDs on an element
        // we want to use the first one defined. This happens in the case of forward refs, and we
        // always add the bottom-most className to the end of the classes list, so we want to
        // show the top-most element in this case
        if (clsFound) {
            return;
        }
        if (!cls) {
            return;
        }
        const extractedId = (0, exports.getCodebaseIdFromClassName)(cls);
        if (extractedId) {
            clsFound = extractedId;
        }
    });
    if (clsFound) {
        return clsFound;
    }
    if (((_a = node === null || node === void 0 ? void 0 : node.tagName) === null || _a === void 0 ? void 0 : _a.toLowerCase()) == 'body') {
        return 'body';
    }
    if ((node === null || node === void 0 ? void 0 : node.id) == 'root') {
        return 'root';
    }
    if ((node === null || node === void 0 ? void 0 : node.id) == '__next') {
        return '__next';
    }
    return null;
};
exports.getCodebaseIdFromNode = getCodebaseIdFromNode;
const getUniquePathFromNode = (node) => {
    var _a, _b;
    // The unique path is the index of the node in the parent overall chain
    // E.g. 0-1-2-1-2-4-1-0
    const path = [];
    let currentNode = node;
    while (currentNode && ((_a = currentNode.tagName) === null || _a === void 0 ? void 0 : _a.toLowerCase()) !== 'body') {
        const index = Array.from(((_b = currentNode.parentElement) === null || _b === void 0 ? void 0 : _b.children) || []).indexOf(currentNode);
        if (index === -1) {
            path.push('0');
        }
        else {
            path.push(index.toString());
        }
        currentNode = currentNode.parentElement;
    }
    return '0-' + path.reverse().join('-');
};
exports.getUniquePathFromNode = getUniquePathFromNode;
const getElementKeyFromNode = (node) => {
    const uniquePath = (0, exports.getUniquePathFromNode)(node);
    return (0, exports.getElementKeyForUniquePath)(uniquePath);
};
exports.getElementKeyFromNode = getElementKeyFromNode;
const hasClass = (node, klass) => {
    if (!(node === null || node === void 0 ? void 0 : node.classList)) {
        return false;
    }
    let hasClass = false;
    node.classList.forEach((cls) => {
        if (cls == klass) {
            hasClass = true;
        }
    });
    return hasClass;
};
exports.hasClass = hasClass;
const isOutline = (node) => {
    return (0, exports.hasClass)(node, exports.OUTLINE_CLASS);
};
exports.isOutline = isOutline;
const getAllUnknownClasesFromList = (classes) => {
    return classes.filter((cls) => {
        if (!cls) {
            return false;
        }
        const isCodebaseId = (0, exports.getCodebaseIdFromClassName)(cls) !== null;
        const clsStartsWithKnownPrefix = KNOWN_CLASS_PREFIXES.some((prefix) => cls.startsWith(prefix));
        if (!clsStartsWithKnownPrefix && !KNOWN_CLASSES.has(cls) && !isCodebaseId) {
            return true;
        }
        return false;
    });
};
exports.getAllUnknownClasesFromList = getAllUnknownClasesFromList;
const getAllUnknownClasses = (node) => {
    if (!(node === null || node === void 0 ? void 0 : node.classList)) {
        return [];
    }
    return (0, exports.getAllUnknownClasesFromList)(Array.from(node.classList));
};
exports.getAllUnknownClasses = getAllUnknownClasses;
/**
 * Accepts a node from mutation observer and determines if it's a moving element.
 * Returns moving if and only if the node has the attribute `arb89-instant-update` set to true.
 * @param node
 * @returns
 */
const isMovingElement = (node) => {
    if (!node) {
        return false;
    }
    // Check if the node responds to getAttribute method.
    if (typeof node.getAttribute !== 'function') {
        return false;
    }
    return node.getAttribute(exports.TEMPO_INSTANT_UPDATE) === 'true';
};
exports.isMovingElement = isMovingElement;
const isSkipNavTreeNode = (node) => {
    if (!node) {
        return;
    }
    return node.getAttribute(exports.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH) === 'true';
};
exports.isSkipNavTreeNode = isSkipNavTreeNode;
/**
 * Check if the node has any parent that is an svg tag
 */
const isElementInSvg = (node, parent) => {
    var _a;
    if (!node) {
        return false;
    }
    if (parent && ((_a = node.tagName) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === 'svg') {
        return true;
    }
    if (node.parentNode) {
        return (0, exports.isElementInSvg)(node.parentNode, true);
    }
    return false;
};
exports.isElementInSvg = isElementInSvg;
//# sourceMappingURL=data:application/json;base64,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