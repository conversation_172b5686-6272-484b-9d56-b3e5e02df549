#!/bin/bash

# Test script for StreamScale Analytics API
# This script tests all the analytics endpoints

# Configuration
API_URL="http://localhost:8080"
API_VERSION="v1"
BASE_URL="$API_URL/api/$API_VERSION"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
    echo -e "\n${YELLOW}==== $1 ====${NC}\n"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# Function to get a JWT token
get_token() {
    print_header "Getting authentication token"
    
    # Replace with your actual login credentials
    response=$(curl -s -X POST \
        "$BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "password": "your_password"
        }')
    
    # Extract token from response
    token=$(echo $response | grep -o '"token":"[^"]*' | sed 's/"token":"//')
    
    if [ -z "$token" ]; then
        print_error "Failed to get authentication token"
        echo "Response: $response"
        exit 1
    else
        print_success "Got authentication token"
        echo "Token: $token"
    fi
    
    echo $token
}

# Function to get a video ID
get_video_id() {
    print_header "Getting a video ID for testing"
    
    # Get a list of videos and extract the first video ID
    response=$(curl -s -X GET \
        "$BASE_URL/video/list" \
        -H "Authorization: Bearer $TOKEN")
    
    # Extract the first video ID from the response
    video_id=$(echo $response | grep -o '"video_id":"[^"]*' | head -1 | sed 's/"video_id":"//')
    
    if [ -z "$video_id" ]; then
        print_error "Failed to get a video ID"
        echo "Response: $response"
        # Use a default video ID for testing
        video_id="550e8400-e29b-41d4-a716-446655440000"
        echo "Using default video ID: $video_id"
    else
        print_success "Got video ID: $video_id"
    fi
    
    echo $video_id
}

# Get authentication token
TOKEN=$(get_token)

# Get a video ID for testing
VIDEO_ID=$(get_video_id)

# Test 1: Get Analytics Summary
print_header "Test 1: Get Analytics Summary"
curl -s -X GET \
    "$BASE_URL/analytics/summary" \
    -H "Authorization: Bearer $TOKEN" | jq .

# Test 2: Record a Video View
print_header "Test 2: Record a Video View"
view_response=$(curl -s -X POST \
    "$BASE_URL/analytics/views" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "{
        \"video_id\": \"$VIDEO_ID\",
        \"duration\": 180
    }")
echo $view_response | jq .

# Test 3: Get Video Views
print_header "Test 3: Get Video Views"
curl -s -X GET \
    "$BASE_URL/analytics/videos/$VIDEO_ID/views?limit=10&offset=0" \
    -H "Authorization: Bearer $TOKEN" | jq .

# Test 4: Start a Watch Session
print_header "Test 4: Start a Watch Session"
session_response=$(curl -s -X POST \
    "$BASE_URL/analytics/sessions/start" \
    -H "Content-Type: application/json" \
    -d "{
        \"video_id\": \"$VIDEO_ID\"
    }")
echo $session_response | jq .

# Extract session ID
SESSION_ID=$(echo $session_response | grep -o '"session_id":"[^"]*' | sed 's/"session_id":"//')

# Test 5: End a Watch Session
print_header "Test 5: End a Watch Session"
curl -s -X POST \
    "$BASE_URL/analytics/sessions/end" \
    -H "Content-Type: application/json" \
    -d "{
        \"session_id\": \"$SESSION_ID\",
        \"watch_duration\": 175,
        \"completed\": true
    }" | jq .

# Test 6: Get Video Performance Metrics
print_header "Test 6: Get Video Performance Metrics"
curl -s -X GET \
    "$BASE_URL/analytics/videos/$VIDEO_ID/performance" \
    -H "Authorization: Bearer $TOKEN" | jq .

# Test 7: Get Top Performing Videos
print_header "Test 7: Get Top Performing Videos"
curl -s -X GET \
    "$BASE_URL/analytics/videos/top?limit=5" \
    -H "Authorization: Bearer $TOKEN" | jq .

# Test 8: Get Recent Videos
print_header "Test 8: Get Recent Videos"
curl -s -X GET \
    "$BASE_URL/analytics/videos/recent?limit=5" \
    -H "Authorization: Bearer $TOKEN" | jq .

print_header "All tests completed"
