import {
  Icon$100
} from "./chunk-DYDB6ZB4.js";
import {
  Icon$101
} from "./chunk-RHDLL2WF.js";
import {
  Icon$102
} from "./chunk-QNDPMTXM.js";
import {
  Icon$103
} from "./chunk-GQVT46KT.js";
import {
  Icon$104
} from "./chunk-2CCJNW3N.js";
import {
  Icon$105
} from "./chunk-P2JPSGG7.js";
import {
  Icon$106
} from "./chunk-MIDHVH5S.js";
import {
  Icon$92
} from "./chunk-R7LOQXCN.js";
import {
  Icon$93
} from "./chunk-RIV7NOQA.js";
import {
  Icon$94
} from "./chunk-2CW6KKFT.js";
import {
  Icon$95
} from "./chunk-BFXMRCJI.js";
import {
  Icon$96
} from "./chunk-4BNKX5ZR.js";
import {
  Icon$97
} from "./chunk-GUKHY5EJ.js";
import {
  Icon$98
} from "./chunk-BS6IQS63.js";
import {
  Icon$99
} from "./chunk-TZV64SYI.js";
import {
  Icon$84
} from "./chunk-BE4P4C6E.js";
import {
  Icon$85
} from "./chunk-SVTJNTH3.js";
import {
  Icon$86
} from "./chunk-ZNZW4E3A.js";
import {
  Icon$87
} from "./chunk-FT3MCX3A.js";
import {
  Icon$88
} from "./chunk-GYTHXPHW.js";
import {
  Icon$89
} from "./chunk-DJNRPYUR.js";
import {
  Icon$90
} from "./chunk-6EWIMXBV.js";
import {
  Icon$91
} from "./chunk-T6RYYTG3.js";
import {
  Icon$76
} from "./chunk-2IXK24RF.js";
import {
  Icon$77
} from "./chunk-Z6BHXZRY.js";
import {
  Icon$78
} from "./chunk-BTO7EKR5.js";
import {
  Icon$79
} from "./chunk-4NBEMRPD.js";
import {
  Icon$80
} from "./chunk-54WQRXO2.js";
import {
  Icon$81
} from "./chunk-X4UPMIBL.js";
import {
  Icon$82
} from "./chunk-KF3Q5L4J.js";
import {
  Icon$83
} from "./chunk-ZXC3V6N4.js";
import {
  Icon$68
} from "./chunk-NTYWXPSD.js";
import {
  Icon$69
} from "./chunk-XP26UYRI.js";
import {
  Icon$70
} from "./chunk-GJZQ627U.js";
import {
  Icon$71
} from "./chunk-N2PPSVK6.js";
import {
  Icon$72
} from "./chunk-43WTYDKZ.js";
import {
  Icon$73
} from "./chunk-UIIQZPOQ.js";
import {
  Icon$74
} from "./chunk-2UIC5Y22.js";
import {
  Icon$75
} from "./chunk-J6K5F6Z4.js";
import {
  Icon$60
} from "./chunk-KBBDLIAE.js";
import {
  Icon$61
} from "./chunk-FZ3JMRQB.js";
import {
  Icon$62
} from "./chunk-HME67SJJ.js";
import {
  Icon$63
} from "./chunk-YMAQ2BIE.js";
import {
  Icon$64
} from "./chunk-JC7PTRZQ.js";
import {
  Icon$65
} from "./chunk-Z3SLNVLS.js";
import {
  Icon$66
} from "./chunk-5RV2PPIX.js";
import {
  Icon$67
} from "./chunk-6Y3KT4XK.js";
import {
  Icon$52
} from "./chunk-L4PG3VUI.js";
import {
  Icon$53
} from "./chunk-G435YTUN.js";
import {
  Icon$54
} from "./chunk-QNURTYHR.js";
import {
  Icon$55
} from "./chunk-M4CXIY5J.js";
import {
  Icon$56
} from "./chunk-3BKP2UXO.js";
import {
  Icon$57
} from "./chunk-UGDOBDLW.js";
import {
  Icon$58
} from "./chunk-NCQPKZX7.js";
import {
  Icon$59
} from "./chunk-4EYJKHNB.js";
import {
  Icon$44
} from "./chunk-GTFL6RIB.js";
import {
  Icon$45
} from "./chunk-XVCESUTX.js";
import {
  Icon$46
} from "./chunk-KAXDVHYY.js";
import {
  Icon$47
} from "./chunk-YEYQWDZX.js";
import {
  Icon$48
} from "./chunk-JBUXQ7QV.js";
import {
  Icon$49
} from "./chunk-FKHAF6ZU.js";
import {
  Icon$50
} from "./chunk-YWSGXPNW.js";
import {
  Icon$51
} from "./chunk-FEWCAF4K.js";
import {
  Icon$36
} from "./chunk-OZ24NBVU.js";
import {
  Icon$37
} from "./chunk-LQJ4BHMF.js";
import {
  Icon$38
} from "./chunk-YRYN4PXT.js";
import {
  Icon$39
} from "./chunk-5U4CIZ5T.js";
import {
  Icon$40
} from "./chunk-IMWS5IHL.js";
import {
  Icon$41
} from "./chunk-54GJLYTR.js";
import {
  Icon$42
} from "./chunk-QYSXLIJG.js";
import {
  Icon$43
} from "./chunk-XPHJDMBT.js";
import {
  Icon$28
} from "./chunk-KQ7HDHFW.js";
import {
  Icon$29
} from "./chunk-2QM3TPMD.js";
import {
  Icon$30
} from "./chunk-O6DZ3FQB.js";
import {
  Icon$31
} from "./chunk-OR3N33Y3.js";
import {
  Icon$32
} from "./chunk-WGCLY7A5.js";
import {
  Icon$33
} from "./chunk-JRRIHYLD.js";
import {
  Icon$34
} from "./chunk-UVIHE3JM.js";
import {
  Icon$35
} from "./chunk-UZ67Q5GR.js";
import {
  Icon$20
} from "./chunk-CN3BFWDX.js";
import {
  Icon$21
} from "./chunk-WPFBQ5CE.js";
import {
  Icon$22
} from "./chunk-PZIUHYKO.js";
import {
  Icon$23
} from "./chunk-YNKRI5FI.js";
import {
  Icon$24
} from "./chunk-JOBXUYQF.js";
import {
  Icon$25
} from "./chunk-U5X5A2QK.js";
import {
  Icon$26
} from "./chunk-I3VPT36G.js";
import {
  Icon$27
} from "./chunk-O3GTPUMX.js";
import {
  Icon$12
} from "./chunk-QJFZLXIJ.js";
import {
  Icon$13
} from "./chunk-XXIOL66V.js";
import {
  Icon$14
} from "./chunk-5BNUKOPX.js";
import {
  Icon$15
} from "./chunk-HS6GMUCE.js";
import {
  Icon$16
} from "./chunk-ZIGXT2LA.js";
import {
  Icon$17
} from "./chunk-WKPMDYVQ.js";
import {
  Icon$18
} from "./chunk-CA2C2QFS.js";
import {
  Icon$19
} from "./chunk-YKLUJJYF.js";
import {
  Icon$4
} from "./chunk-ECISRJ5D.js";
import {
  Icon$5
} from "./chunk-M2H2SCIS.js";
import {
  Icon$6
} from "./chunk-5EQSY475.js";
import {
  Icon$7
} from "./chunk-PAX2JXKD.js";
import {
  Icon$8
} from "./chunk-CSNODQHC.js";
import {
  Icon$9
} from "./chunk-JAL7LJ64.js";
import {
  Icon$10
} from "./chunk-CDX456MW.js";
import {
  Icon$11
} from "./chunk-X5RFPUTF.js";
import {
  Icon$0
} from "./chunk-JY4YBBMQ.js";
import {
  Icon$1
} from "./chunk-6PMTSZ7M.js";
import {
  Icon$2
} from "./chunk-CTLMFZ7W.js";
import {
  Icon$3
} from "./chunk-RI6MV3BJ.js";
import {
  require_react
} from "./chunk-ZMLY2J2T.js";
import {
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/@vidstack/react/prod/vidstack-icons.js
var import_react = __toESM(require_react());

// node_modules/@vidstack/react/prod/chunks/vidstack-CBF7iUqu.js
var React = __toESM(require_react(), 1);
var Icon = React.forwardRef((props, ref) => {
  const { width, height, size = null, paths, ...restProps } = props;
  return React.createElement("svg", {
    viewBox: "0 0 32 32",
    ...restProps,
    width: width ?? size,
    height: height ?? size,
    fill: "none",
    "aria-hidden": "true",
    focusable: "false",
    xmlns: "http://www.w3.org/2000/svg",
    ref,
    dangerouslySetInnerHTML: { __html: paths }
  });
});
Icon.displayName = "VidstackIcon";

// node_modules/@vidstack/react/prod/vidstack-icons.js
var cn = (className) => className ? `${className} vds-icon` : "vds-icon";
var AccessibilityIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$0 });
});
AccessibilityIcon.displayName = "VidstackAccessibilityIcon";
var AddNoteIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$1 });
});
AddNoteIcon.displayName = "VidstackAddNoteIcon";
var AddPlaylistIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$2 });
});
AddPlaylistIcon.displayName = "VidstackAddPlaylistIcon";
var AddUserIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$3 });
});
AddUserIcon.displayName = "VidstackAddUserIcon";
var AddIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$4 });
});
AddIcon.displayName = "VidstackAddIcon";
var AirPlayIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$5 });
});
AirPlayIcon.displayName = "VidstackAirPlayIcon";
var ArrowCollapseInIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$6 });
});
ArrowCollapseInIcon.displayName = "VidstackArrowCollapseInIcon";
var ArrowCollapseIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$7 });
});
ArrowCollapseIcon.displayName = "VidstackArrowCollapseIcon";
var ArrowDownIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$8 });
});
ArrowDownIcon.displayName = "VidstackArrowDownIcon";
var ArrowExpandOutIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$9 });
});
ArrowExpandOutIcon.displayName = "VidstackArrowExpandOutIcon";
var ArrowExpandIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$10 });
});
ArrowExpandIcon.displayName = "VidstackArrowExpandIcon";
var ArrowLeftIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$11 });
});
ArrowLeftIcon.displayName = "VidstackArrowLeftIcon";
var ArrowRightIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$12 });
});
ArrowRightIcon.displayName = "VidstackArrowRightIcon";
var ArrowUpIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$13 });
});
ArrowUpIcon.displayName = "VidstackArrowUpIcon";
var BookmarkIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$14 });
});
BookmarkIcon.displayName = "VidstackBookmarkIcon";
var CameraIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$15 });
});
CameraIcon.displayName = "VidstackCameraIcon";
var ChaptersIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$16 });
});
ChaptersIcon.displayName = "VidstackChaptersIcon";
var ChatCollapseIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$17 });
});
ChatCollapseIcon.displayName = "VidstackChatCollapseIcon";
var ChatIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$18 });
});
ChatIcon.displayName = "VidstackChatIcon";
var CheckIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$19 });
});
CheckIcon.displayName = "VidstackCheckIcon";
var ChevronDownIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$20 });
});
ChevronDownIcon.displayName = "VidstackChevronDownIcon";
var ChevronLeftIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$21 });
});
ChevronLeftIcon.displayName = "VidstackChevronLeftIcon";
var ChevronRightIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$22 });
});
ChevronRightIcon.displayName = "VidstackChevronRightIcon";
var ChevronUpIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$23 });
});
ChevronUpIcon.displayName = "VidstackChevronUpIcon";
var ChromecastIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$24 });
});
ChromecastIcon.displayName = "VidstackChromecastIcon";
var ClipIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$25 });
});
ClipIcon.displayName = "VidstackClipIcon";
var ClosedCaptionsOnIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$26 });
});
ClosedCaptionsOnIcon.displayName = "VidstackClosedCaptionsOnIcon";
var ClosedCaptionsIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$27 });
});
ClosedCaptionsIcon.displayName = "VidstackClosedCaptionsIcon";
var CommentIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$28 });
});
CommentIcon.displayName = "VidstackCommentIcon";
var ComputerIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$29 });
});
ComputerIcon.displayName = "VidstackComputerIcon";
var DeviceIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$30 });
});
DeviceIcon.displayName = "VidstackDeviceIcon";
var DownloadIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$31 });
});
DownloadIcon.displayName = "VidstackDownloadIcon";
var EpisodesIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$32 });
});
EpisodesIcon.displayName = "VidstackEpisodesIcon";
var EyeIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$33 });
});
EyeIcon.displayName = "VidstackEyeIcon";
var FastBackwardIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$34 });
});
FastBackwardIcon.displayName = "VidstackFastBackwardIcon";
var FastForwardIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$35 });
});
FastForwardIcon.displayName = "VidstackFastForwardIcon";
var FlagIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$36 });
});
FlagIcon.displayName = "VidstackFlagIcon";
var FullscreenArrowExitIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$37 });
});
FullscreenArrowExitIcon.displayName = "VidstackFullscreenArrowExitIcon";
var FullscreenArrowIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$38 });
});
FullscreenArrowIcon.displayName = "VidstackFullscreenArrowIcon";
var FullscreenExitIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$39 });
});
FullscreenExitIcon.displayName = "VidstackFullscreenExitIcon";
var FullscreenIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$40 });
});
FullscreenIcon.displayName = "VidstackFullscreenIcon";
var HeartIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$41 });
});
HeartIcon.displayName = "VidstackHeartIcon";
var InfoIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$42 });
});
InfoIcon.displayName = "VidstackInfoIcon";
var LanguageIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$43 });
});
LanguageIcon.displayName = "VidstackLanguageIcon";
var LinkIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$44 });
});
LinkIcon.displayName = "VidstackLinkIcon";
var LockClosedIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$45 });
});
LockClosedIcon.displayName = "VidstackLockClosedIcon";
var LockOpenIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$46 });
});
LockOpenIcon.displayName = "VidstackLockOpenIcon";
var MenuHorizontalIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$47 });
});
MenuHorizontalIcon.displayName = "VidstackMenuHorizontalIcon";
var MenuVerticalIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$48 });
});
MenuVerticalIcon.displayName = "VidstackMenuVerticalIcon";
var MicrophoneIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$49 });
});
MicrophoneIcon.displayName = "VidstackMicrophoneIcon";
var MobileIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$50 });
});
MobileIcon.displayName = "VidstackMobileIcon";
var MoonIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$51 });
});
MoonIcon.displayName = "VidstackMoonIcon";
var MusicOffIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$52 });
});
MusicOffIcon.displayName = "VidstackMusicOffIcon";
var MusicIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$53 });
});
MusicIcon.displayName = "VidstackMusicIcon";
var MuteIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$54 });
});
MuteIcon.displayName = "VidstackMuteIcon";
var NextIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$55 });
});
NextIcon.displayName = "VidstackNextIcon";
var NoEyeIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$56 });
});
NoEyeIcon.displayName = "VidstackNoEyeIcon";
var NotificationIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$57 });
});
NotificationIcon.displayName = "VidstackNotificationIcon";
var OdometerIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$58 });
});
OdometerIcon.displayName = "VidstackOdometerIcon";
var PauseIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$59 });
});
PauseIcon.displayName = "VidstackPauseIcon";
var PictureInPictureExitIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$60 });
});
PictureInPictureExitIcon.displayName = "VidstackPictureInPictureExitIcon";
var PictureInPictureIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$61 });
});
PictureInPictureIcon.displayName = "VidstackPictureInPictureIcon";
var PlayIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$62 });
});
PlayIcon.displayName = "VidstackPlayIcon";
var PlaybackSpeedCircleIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$63 });
});
PlaybackSpeedCircleIcon.displayName = "VidstackPlaybackSpeedCircleIcon";
var PlaylistIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$64 });
});
PlaylistIcon.displayName = "VidstackPlaylistIcon";
var PreviousIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$65 });
});
PreviousIcon.displayName = "VidstackPreviousIcon";
var QuestionMarkIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$66 });
});
QuestionMarkIcon.displayName = "VidstackQuestionMarkIcon";
var QueueListIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$67 });
});
QueueListIcon.displayName = "VidstackQueueListIcon";
var RadioButtonSelectedIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$68 });
});
RadioButtonSelectedIcon.displayName = "VidstackRadioButtonSelectedIcon";
var RadioButtonIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$69 });
});
RadioButtonIcon.displayName = "VidstackRadioButtonIcon";
var RepeatOnIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$70 });
});
RepeatOnIcon.displayName = "VidstackRepeatOnIcon";
var RepeatSquareOnIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$71 });
});
RepeatSquareOnIcon.displayName = "VidstackRepeatSquareOnIcon";
var RepeatSquareIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$72 });
});
RepeatSquareIcon.displayName = "VidstackRepeatSquareIcon";
var RepeatIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$73 });
});
RepeatIcon.displayName = "VidstackRepeatIcon";
var ReplayIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$74 });
});
ReplayIcon.displayName = "VidstackReplayIcon";
var RotateIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$75 });
});
RotateIcon.displayName = "VidstackRotateIcon";
var SearchIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$76 });
});
SearchIcon.displayName = "VidstackSearchIcon";
var SeekBackward10Icon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$77 });
});
SeekBackward10Icon.displayName = "VidstackSeekBackward10Icon";
var SeekBackward15Icon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$78 });
});
SeekBackward15Icon.displayName = "VidstackSeekBackward15Icon";
var SeekBackward30Icon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$79 });
});
SeekBackward30Icon.displayName = "VidstackSeekBackward30Icon";
var SeekBackwardIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$80 });
});
SeekBackwardIcon.displayName = "VidstackSeekBackwardIcon";
var SeekForward10Icon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$81 });
});
SeekForward10Icon.displayName = "VidstackSeekForward10Icon";
var SeekForward15Icon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$82 });
});
SeekForward15Icon.displayName = "VidstackSeekForward15Icon";
var SeekForward30Icon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$83 });
});
SeekForward30Icon.displayName = "VidstackSeekForward30Icon";
var SeekForwardIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$84 });
});
SeekForwardIcon.displayName = "VidstackSeekForwardIcon";
var SendIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$85 });
});
SendIcon.displayName = "VidstackSendIcon";
var SettingsMenuIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$86 });
});
SettingsMenuIcon.displayName = "VidstackSettingsMenuIcon";
var SettingsSwitchIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$87 });
});
SettingsSwitchIcon.displayName = "VidstackSettingsSwitchIcon";
var SettingsIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$88 });
});
SettingsIcon.displayName = "VidstackSettingsIcon";
var ShareArrowIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$89 });
});
ShareArrowIcon.displayName = "VidstackShareArrowIcon";
var ShareIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$90 });
});
ShareIcon.displayName = "VidstackShareIcon";
var ShuffleOnIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$91 });
});
ShuffleOnIcon.displayName = "VidstackShuffleOnIcon";
var ShuffleIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$92 });
});
ShuffleIcon.displayName = "VidstackShuffleIcon";
var StopIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$93 });
});
StopIcon.displayName = "VidstackStopIcon";
var SubtitlesIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$94 });
});
SubtitlesIcon.displayName = "VidstackSubtitlesIcon";
var SunIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$95 });
});
SunIcon.displayName = "VidstackSunIcon";
var TheatreModeExitIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$96 });
});
TheatreModeExitIcon.displayName = "VidstackTheatreModeExitIcon";
var TheatreModeIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$97 });
});
TheatreModeIcon.displayName = "VidstackTheatreModeIcon";
var ThumbsDownIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$98 });
});
ThumbsDownIcon.displayName = "VidstackThumbsDownIcon";
var ThumbsUpIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$99 });
});
ThumbsUpIcon.displayName = "VidstackThumbsUpIcon";
var TimerIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$100 });
});
TimerIcon.displayName = "VidstackTimerIcon";
var TranscriptIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$101 });
});
TranscriptIcon.displayName = "VidstackTranscriptIcon";
var TvIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$102 });
});
TvIcon.displayName = "VidstackTvIcon";
var UserIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$103 });
});
UserIcon.displayName = "VidstackUserIcon";
var VolumeHighIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$104 });
});
VolumeHighIcon.displayName = "VidstackVolumeHighIcon";
var VolumeLowIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$105 });
});
VolumeLowIcon.displayName = "VidstackVolumeLowIcon";
var XMarkIcon = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_react.createElement)(Icon, { ...props, className: cn(props.className), ref, paths: Icon$106 });
});
XMarkIcon.displayName = "VidstackXMarkIcon";
export {
  AccessibilityIcon,
  AddIcon,
  AddNoteIcon,
  AddPlaylistIcon,
  AddUserIcon,
  AirPlayIcon,
  ArrowCollapseIcon,
  ArrowCollapseInIcon,
  ArrowDownIcon,
  ArrowExpandIcon,
  ArrowExpandOutIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  ArrowUpIcon,
  BookmarkIcon,
  CameraIcon,
  ChaptersIcon,
  ChatCollapseIcon,
  ChatIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  ChromecastIcon,
  ClipIcon,
  ClosedCaptionsIcon,
  ClosedCaptionsOnIcon,
  CommentIcon,
  ComputerIcon,
  DeviceIcon,
  DownloadIcon,
  EpisodesIcon,
  EyeIcon,
  FastBackwardIcon,
  FastForwardIcon,
  FlagIcon,
  FullscreenArrowExitIcon,
  FullscreenArrowIcon,
  FullscreenExitIcon,
  FullscreenIcon,
  HeartIcon,
  InfoIcon,
  LanguageIcon,
  LinkIcon,
  LockClosedIcon,
  LockOpenIcon,
  MenuHorizontalIcon,
  MenuVerticalIcon,
  MicrophoneIcon,
  MobileIcon,
  MoonIcon,
  MusicIcon,
  MusicOffIcon,
  MuteIcon,
  NextIcon,
  NoEyeIcon,
  NotificationIcon,
  OdometerIcon,
  PauseIcon,
  PictureInPictureExitIcon,
  PictureInPictureIcon,
  PlayIcon,
  PlaybackSpeedCircleIcon,
  PlaylistIcon,
  PreviousIcon,
  QuestionMarkIcon,
  QueueListIcon,
  RadioButtonIcon,
  RadioButtonSelectedIcon,
  RepeatIcon,
  RepeatOnIcon,
  RepeatSquareIcon,
  RepeatSquareOnIcon,
  ReplayIcon,
  RotateIcon,
  SearchIcon,
  SeekBackward10Icon,
  SeekBackward15Icon,
  SeekBackward30Icon,
  SeekBackwardIcon,
  SeekForward10Icon,
  SeekForward15Icon,
  SeekForward30Icon,
  SeekForwardIcon,
  SendIcon,
  SettingsIcon,
  SettingsMenuIcon,
  SettingsSwitchIcon,
  ShareArrowIcon,
  ShareIcon,
  ShuffleIcon,
  ShuffleOnIcon,
  StopIcon,
  SubtitlesIcon,
  SunIcon,
  TheatreModeExitIcon,
  TheatreModeIcon,
  ThumbsDownIcon,
  ThumbsUpIcon,
  TimerIcon,
  TranscriptIcon,
  TvIcon,
  UserIcon,
  VolumeHighIcon,
  VolumeLowIcon,
  XMarkIcon
};
//# sourceMappingURL=@vidstack_react_icons.js.map
