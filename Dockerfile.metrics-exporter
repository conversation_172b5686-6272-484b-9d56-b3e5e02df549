FROM golang:1.21-alpine AS builder

WORKDIR /app

# Cache Go modules
COPY go.mod go.sum ./
RUN go mod download

# Install Prometheus client
RUN go get github.com/prometheus/client_golang/prometheus
RUN go get github.com/prometheus/client_golang/prometheus/promhttp

# Copy source and build
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o metrics-exporter ./cmd/metrics-exporter/main.go

# Final image
FROM alpine:latest

WORKDIR /app

# Copy built binary
COPY --from=builder /app/metrics-exporter .

# Expose metrics port
EXPOSE 9090

# Run the exporter
CMD ["./metrics-exporter"]
