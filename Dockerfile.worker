# --- Stage 1: Build Go binary ---
    FROM golang:1.21-alpine AS builder

    WORKDIR /app
    
    # Cache Go modules
    COPY go.mod go.sum ./
    RUN go mod download
    
    # Copy source and build
    COPY . .
    RUN CGO_ENABLED=0 GOOS=linux go build -o worker ./cmd/worker.go
    
    
    # --- Stage 2: Final image ---
    FROM alfg/bento4:latest
    
    # Install additional dependencies using Alpine's package manager
    RUN apk add --no-cache \
        curl \
        ca-certificates \
        python3 \
        py3-pip \
        make \
        g++ \
        cmake \
        git \
        bash \
        unzip \
        ffmpeg
    
    # Set workdir
    WORKDIR /app
    
    # Copy built binary and config
    COPY --from=builder /app/worker .
    COPY config.yml .
    
    # Create temp directory for video segments
    RUN mkdir -p /app/tmp_segments && chmod 777 /app/tmp_segments
    
    # Default command
    CMD ["./worker"]
    