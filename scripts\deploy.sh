#!/bin/bash
set -e

# Default values
ENVIRONMENT=${ENVIRONMENT:-"dev"}
WORKER_IMAGE_NAME=${WORKER_IMAGE_NAME:-"streamscale/worker"}
WORKER_IMAGE_TAG=${WORKER_IMAGE_TAG:-"latest"}
METRICS_EXPORTER_IMAGE=${METRICS_EXPORTER_IMAGE:-"streamscale/metrics-exporter"}
METRICS_EXPORTER_TAG=${METRICS_EXPORTER_TAG:-"latest"}

# Load environment-specific variables
if [ -f ".env.${ENVIRONMENT}" ]; then
  echo "Loading environment variables from .env.${ENVIRONMENT}"
  source ".env.${ENVIRONMENT}"
else
  echo "Environment file .env.${ENVIRONMENT} not found"
  exit 1
fi

# Ensure required variables are set
required_vars=(
  "REDIS_PASSWORD"
  "AWS_ACCESS_KEY"
  "AWS_SECRET_KEY"
  "POSTGRES_USER"
  "POSTGRES_PASSWORD"
  "JWT_SECRET_KEY"
)

for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    echo "Error: Required variable $var is not set"
    exit 1
  fi
done

# Create base64 encoded secrets
REDIS_PASSWORD_BASE64=$(echo -n "$REDIS_PASSWORD" | base64)
AWS_ACCESS_KEY_BASE64=$(echo -n "$AWS_ACCESS_KEY" | base64)
AWS_SECRET_KEY_BASE64=$(echo -n "$AWS_SECRET_KEY" | base64)
POSTGRES_USER_BASE64=$(echo -n "$POSTGRES_USER" | base64)
POSTGRES_PASSWORD_BASE64=$(echo -n "$POSTGRES_PASSWORD" | base64)
JWT_SECRET_KEY_BASE64=$(echo -n "$JWT_SECRET_KEY" | base64)

# Create namespace if it doesn't exist
kubectl get namespace streamscale-${ENVIRONMENT} || kubectl create namespace streamscale-${ENVIRONMENT}
kubectl config set-context --current --namespace=streamscale-${ENVIRONMENT}

# Apply ConfigMaps
echo "Applying ConfigMaps..."
sed -e "s/\${REDIS_ADDR}/${REDIS_ADDR}/g" \
    -e "s/\${AWS_REGION}/${AWS_REGION}/g" \
    -e "s/\${S3_ENDPOINT}/${S3_ENDPOINT}/g" \
    -e "s/\${S3_INPUT_BUCKET}/${S3_INPUT_BUCKET}/g" \
    -e "s/\${S3_OUTPUT_BUCKET}/${S3_OUTPUT_BUCKET}/g" \
    -e "s/\${S3_CDN_ENDPOINT}/${S3_CDN_ENDPOINT}/g" \
    -e "s/\${POSTGRES_HOST}/${POSTGRES_HOST}/g" \
    -e "s/\${POSTGRES_PORT}/${POSTGRES_PORT}/g" \
    -e "s/\${POSTGRES_DB}/${POSTGRES_DB}/g" \
    k8s/configmap.yaml | kubectl apply -f -

# Apply Secrets
echo "Applying Secrets..."
sed -e "s/\${REDIS_PASSWORD_BASE64}/${REDIS_PASSWORD_BASE64}/g" \
    -e "s/\${AWS_ACCESS_KEY_BASE64}/${AWS_ACCESS_KEY_BASE64}/g" \
    -e "s/\${AWS_SECRET_KEY_BASE64}/${AWS_SECRET_KEY_BASE64}/g" \
    -e "s/\${POSTGRES_USER_BASE64}/${POSTGRES_USER_BASE64}/g" \
    -e "s/\${POSTGRES_PASSWORD_BASE64}/${POSTGRES_PASSWORD_BASE64}/g" \
    -e "s/\${JWT_SECRET_KEY_BASE64}/${JWT_SECRET_KEY_BASE64}/g" \
    k8s/secrets.yaml | kubectl apply -f -

# Apply Metrics Exporter
echo "Deploying Metrics Exporter..."
sed -e "s/\${METRICS_EXPORTER_IMAGE}/${METRICS_EXPORTER_IMAGE}/g" \
    -e "s/\${METRICS_EXPORTER_TAG}/${METRICS_EXPORTER_TAG}/g" \
    k8s/metrics-exporter.yaml | kubectl apply -f -

# Apply Prometheus Adapter Config
echo "Applying Prometheus Adapter Config..."
kubectl apply -f k8s/prometheus-adapter-config.yaml

# Apply Worker Deployment
echo "Deploying Worker..."
sed -e "s/\${WORKER_IMAGE_NAME}/${WORKER_IMAGE_NAME}/g" \
    -e "s/\${WORKER_IMAGE_TAG}/${WORKER_IMAGE_TAG}/g" \
    k8s/worker-deployment.yaml | kubectl apply -f -

# Apply HPA
echo "Applying Horizontal Pod Autoscaler..."
kubectl apply -f k8s/worker-hpa.yaml

echo "Deployment completed successfully!"
