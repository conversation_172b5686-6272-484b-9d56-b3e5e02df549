"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isNodeOutline = exports.updateOutlines = exports.clearAllOutlines = exports.getOutlineElement = exports.OutlineType = exports.PRIMARY_COMPONENT_OUTLINE_COLOR = exports.SECONDARY_OUTLINE_COLOUR = exports.PRIMARY_OUTLINE_COLOUR = void 0;
const identifierUtils_1 = require("./identifierUtils");
const sessionStorageUtils_1 = require("./sessionStorageUtils");
// @ts-ignore
const jquery_1 = __importDefault(require("jquery"));
const tempoElement_1 = require("./tempoElement");
const editTextUtils_1 = require("./editTextUtils");
const constantsAndTypes_1 = require("./constantsAndTypes");
exports.PRIMARY_OUTLINE_COLOUR = '#4597F7';
exports.SECONDARY_OUTLINE_COLOUR = '#4597F7';
exports.PRIMARY_COMPONENT_OUTLINE_COLOR = '#6183e4';
var OutlineType;
(function (OutlineType) {
    OutlineType[OutlineType["PRIMARY"] = 0] = "PRIMARY";
    OutlineType[OutlineType["SECONDARY"] = 1] = "SECONDARY";
    OutlineType[OutlineType["CHILD"] = 2] = "CHILD";
    OutlineType[OutlineType["MOVE"] = 3] = "MOVE";
})(OutlineType || (exports.OutlineType = OutlineType = {}));
/**
 * Returns a context-based palette of colours to use for the outlines.
 */
const colours = () => {
    const aiContextSelection = (0, sessionStorageUtils_1.getMemoryStorageItem)('aiContext');
    if (aiContextSelection) {
        return {
            primary: '#6858f5',
            secondary: '#6858f5',
            component: '#5246C2',
        };
    }
    return {
        primary: exports.PRIMARY_OUTLINE_COLOUR,
        secondary: exports.SECONDARY_OUTLINE_COLOUR,
        component: exports.PRIMARY_COMPONENT_OUTLINE_COLOR,
    };
};
const getDashedBackgroundImage = (strokeColor, dashWidth, dashGap) => {
    return `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='${strokeColor.replace('#', '%23')}' stroke-width='${dashWidth}' stroke-dasharray='1%2c ${dashGap}' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e")`;
};
const capitalizeFirstLetter = (str) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
};
const getPencilSVG = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>`;
};
const getEditTextButtonNode = (parentPort, bgColor, elementKey) => {
    const el = document.createElement('div');
    const textEl = document.createElement('div');
    textEl.innerText = 'Edit Dynamic Text';
    textEl.classList.add(identifierUtils_1.EDIT_TEXT_BUTTON);
    textEl.classList.add(identifierUtils_1.OUTLINE_CLASS);
    // First append the pencil SVG
    const pencilSVG = document.createElement('div');
    pencilSVG.innerHTML = getPencilSVG();
    pencilSVG.style.width = '22px';
    pencilSVG.style.height = '22px';
    pencilSVG.classList.add(identifierUtils_1.EDIT_TEXT_BUTTON);
    pencilSVG.classList.add(identifierUtils_1.OUTLINE_CLASS);
    el.appendChild(pencilSVG);
    el.appendChild(textEl);
    el.classList.add(identifierUtils_1.OUTLINE_CLASS);
    el.classList.add(identifierUtils_1.EDIT_TEXT_BUTTON);
    el.style.color = 'white';
    el.style.cursor = 'pointer';
    el.style.backgroundColor = bgColor;
    el.style.padding = '4px 12px 4px 12px';
    el.style.borderRadius = '8px';
    el.style.fontSize = '20px';
    el.style.pointerEvents = 'auto';
    el.style.display = 'flex';
    el.style.flexDirection = 'row';
    el.style.alignItems = 'center';
    el.style.justifyContent = 'center';
    el.style.gap = '8px';
    // When clicking, trigger an open in editor action
    el.addEventListener('pointerdown', (e) => {
        e.preventDefault();
        e.stopPropagation();
        parentPort.postMessage({
            id: constantsAndTypes_1.FIXED_IFRAME_MESSAGE_IDS.EDIT_DYNAMIC_TEXT,
            elementKey,
        });
    });
    el.addEventListener('pointerup', (e) => {
        e.preventDefault();
        e.stopPropagation();
    });
    return el;
};
const getOutlineElement = (parentPort, type, pageLeft, pageTop, width, height, selected, tagName, isComponent, elementKey) => {
    const palette = colours();
    const left = pageLeft;
    const top = pageTop;
    const zoomPerc = (0, sessionStorageUtils_1.getMemoryStorageItem)('zoomPerc');
    const zoomMultiplier = zoomPerc ? 1 / Number(zoomPerc) : 1;
    const newElement = document.createElement('div');
    newElement.classList.add(identifierUtils_1.OUTLINE_CLASS);
    if (type === OutlineType.CHILD || type === OutlineType.MOVE) {
        const dashThickness = 5 * zoomMultiplier;
        newElement.style.backgroundImage = getDashedBackgroundImage(isComponent ? palette.component : palette.primary, Math.max(1, Math.round(dashThickness)), Math.max(3, Math.round(dashThickness * 3)));
    }
    else {
        const thickness = type === OutlineType.SECONDARY
            ? 0.5 * zoomMultiplier
            : 1 * zoomMultiplier;
        if (thickness >= 0.5) {
            newElement.style.outline = `${thickness}px solid ${type === OutlineType.SECONDARY
                ? palette.secondary
                : isComponent
                    ? palette.component
                    : palette.primary}`;
        }
        newElement.style.border = `${thickness >= 0.5 ? thickness : thickness * 2}px solid ${type === OutlineType.SECONDARY
            ? palette.secondary
            : isComponent
                ? palette.component
                : palette.primary}`;
    }
    newElement.style.position = 'fixed';
    newElement.style.pointerEvents = 'none';
    switch (type) {
        case OutlineType.PRIMARY:
            newElement.style.zIndex = '2000000002';
            break;
        case OutlineType.SECONDARY:
            newElement.style.zIndex = '2000000001';
            break;
        case OutlineType.CHILD:
            newElement.style.zIndex = '2000000000';
            break;
        case OutlineType.MOVE:
            newElement.style.zIndex = '2000000003';
            break;
    }
    newElement.style.boxSizing = 'border-box';
    newElement.style.left = left + 'px';
    newElement.style.top = top + 'px';
    newElement.style.width = width + 'px';
    newElement.style.height = height + 'px';
    newElement.style.cursor = 'default !important';
    const limitedZoomMultiplier = Math.min(2, zoomMultiplier);
    if (type === OutlineType.PRIMARY && selected) {
        // Draw the size of the element underneath
        const sizeElement = document.createElement('div');
        newElement.appendChild(sizeElement);
        sizeElement.classList.add(identifierUtils_1.OUTLINE_CLASS);
        sizeElement.innerHTML = `${Math.round(width)} x ${Math.round(height)}`;
        sizeElement.style.color = 'white';
        sizeElement.style.backgroundColor = isComponent
            ? palette.component
            : palette.primary;
        sizeElement.style.padding = '4px 12px 4px 12px';
        sizeElement.style.height = '38px';
        sizeElement.style.borderRadius = '8px';
        sizeElement.style.position = 'absolute';
        sizeElement.style.left = `calc(${width}px / 2)`;
        sizeElement.style.fontSize = '20px';
        sizeElement.style.whiteSpace = 'nowrap';
        // After 22 it starts to merge into the border
        // 52 is the size of the element (38px) + double the size of the gap between the border and the element (7px)
        const bottomValue = -Math.max(22, 45 + (52 * limitedZoomMultiplier - 52) / 2);
        sizeElement.style.bottom = `${bottomValue}px`;
        sizeElement.style.transform = `scale(${limitedZoomMultiplier}) translateX(${-50 / limitedZoomMultiplier}%)`;
    }
    if (selected && tagName) {
        const topControlsWrapper = document.createElement('div');
        newElement.appendChild(topControlsWrapper);
        topControlsWrapper.style.display = 'flex';
        topControlsWrapper.style.width = width / limitedZoomMultiplier + 'px';
        topControlsWrapper.style.justifyContent = 'space-between';
        topControlsWrapper.style.flexDirection = 'row';
        topControlsWrapper.style.gap = '4px';
        topControlsWrapper.style.position = 'absolute';
        topControlsWrapper.style.left = `0px`;
        topControlsWrapper.style.transform = `scale(${limitedZoomMultiplier}) translateX(${50 - 50 / limitedZoomMultiplier}%) translateY(${-70 - 50 / limitedZoomMultiplier}%)`;
        // Draw the tagname above
        const tagNameElement = document.createElement('div');
        topControlsWrapper.appendChild(tagNameElement);
        tagNameElement.classList.add(identifierUtils_1.OUTLINE_CLASS);
        tagNameElement.innerHTML = tagName
            ? isComponent
                ? capitalizeFirstLetter(tagName)
                : tagName.toLowerCase()
            : '';
        tagNameElement.style.color = 'white';
        tagNameElement.style.backgroundColor = isComponent
            ? palette.component
            : palette.primary;
        tagNameElement.style.padding = '4px 12px 4px 12px';
        tagNameElement.style.height = '38px';
        tagNameElement.style.borderRadius = '8px';
        tagNameElement.style.fontSize = '20px';
        // If this node has direct static text inside of it, but is not editable, show the edit text
        // dynamically button
        if (type === OutlineType.PRIMARY) {
            const matchingNode = (0, identifierUtils_1.getNodeForElementKey)(elementKey);
            const tempoElement = tempoElement_1.TempoElement.fromKey(elementKey || '');
            if (matchingNode &&
                (0, editTextUtils_1.hasTextContents)(matchingNode) &&
                !(0, editTextUtils_1.canEditText)(tempoElement)) {
                const newNode = getEditTextButtonNode(parentPort, isComponent ? palette.component : palette.primary, elementKey);
                topControlsWrapper.appendChild(newNode);
            }
        }
    }
    // TODO: Add in when we add resizing in the canvas
    // if (primary && selected) {
    //   for (let top = 1; top >= 0; top -= 1) {
    //     for (let left = 1; left >= 0; left -= 1) {
    //       const cornerElement = document.createElement("div");
    //       newElement.appendChild(cornerElement);
    //       cornerElement.classList.add(OUTLINE_CLASS);
    //       cornerElement.style.position = "absolute";
    //       cornerElement.style.width = Math.max(14 * zoomMultiplier, 1) + "px";
    //       cornerElement.style.height = Math.max(14 * zoomMultiplier, 1) + "px";
    //       cornerElement.style.backgroundColor = "white";
    //       cornerElement.style.cursor = "pointer";
    //       cornerElement.style.zIndex = "2000000002";
    //       if (top) {
    //         cornerElement.style.top = Math.min(-7 * zoomMultiplier, -0.5) + "px";
    //       } else {
    //         cornerElement.style.bottom = Math.min(-7 * zoomMultiplier, -0.5) + "px";
    //       }
    //       if (left) {
    //         cornerElement.style.left = Math.min(-8 * zoomMultiplier, -0.5) + "px";
    //       } else {
    //         cornerElement.style.right = Math.min(-8 * zoomMultiplier, -0.5) + "px";
    //       }
    //       cornerElement.style.outline = 2 * zoomMultiplier + "px solid " + PRIMARY_OUTLINE_COLOUR;
    //       cornerElement.style.pointerEvents = "auto";
    //     }
    //   }
    // }
    return newElement;
};
exports.getOutlineElement = getOutlineElement;
const clearAllOutlines = () => {
    (0, jquery_1.default)(`.${identifierUtils_1.OUTLINE_CLASS}`).remove();
};
exports.clearAllOutlines = clearAllOutlines;
/**
 * Creates all the necessary outlines for the hovered and selected elements
 * @returns
 */
const updateOutlines = (parentPort, storyboardId) => {
    // Use requestAnimationFrame to batch visual updates for better performance
    (0, exports.clearAllOutlines)();
    const driveModeEnabled = !!(0, sessionStorageUtils_1.getSessionStorageItem)('driveModeEnabled', storyboardId);
    if (driveModeEnabled) {
        return;
    }
    const hoveredElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.HOVERED_ELEMENT_KEY);
    const selectedElementKey = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.SELECTED_ELEMENT_KEY);
    const multiselectedElementKeys = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.MULTI_SELECTED_ELEMENT_KEYS);
    const selectedElement = tempoElement_1.TempoElement.fromKey(selectedElementKey);
    const body = document.getElementsByTagName('body')[0];
    // Create a document fragment to batch DOM operations
    const fragment = document.createDocumentFragment();
    const elementKeyToNavNode = (0, sessionStorageUtils_1.getMemoryStorageItem)(sessionStorageUtils_1.ELEMENT_KEY_TO_NAV_NODE) || {};
    // Cache for bounding boxes to avoid recalculating for the same elements
    const boundingBoxCache = {};
    // Cache jQuery selectors
    const $body = (0, jquery_1.default)('body');
    const $instantDivDrawElements = (0, jquery_1.default)(`.${identifierUtils_1.TEMPO_INSTANT_DIV_DRAW_CLASS}`);
    const $outlineUntilRefreshElements = (0, jquery_1.default)(`*[${identifierUtils_1.TEMPO_OUTLINE_UNTIL_REFESH}=true]`);
    // Cache element selector results for reuse
    const elementKeySelectors = {};
    const getElementSelector = (elementKey) => {
        if (!elementKeySelectors[elementKey]) {
            const node = (0, identifierUtils_1.getNodeForElementKey)(elementKey);
            if (node) {
                elementKeySelectors[elementKey] = (0, jquery_1.default)(node);
            }
        }
        return elementKeySelectors[elementKey];
    };
    const getBoundingBoxForElementKey = (elementKey) => {
        var _a;
        // Return cached result if available
        if (boundingBoxCache[elementKey] !== undefined) {
            return boundingBoxCache[elementKey];
        }
        const navNode = elementKeyToNavNode[elementKey];
        let result = null;
        if (navNode === null || navNode === void 0 ? void 0 : navNode.pageBoundingBox) {
            result = {
                left: navNode.pageBoundingBox.pageX,
                top: navNode.pageBoundingBox.pageY,
                width: navNode.pageBoundingBox.width,
                height: navNode.pageBoundingBox.height,
            };
        }
        else {
            // Try to get the bounding box directly from the DOM as a fallback
            // at Nav Tree build time
            const element = getElementSelector(elementKey).get(0);
            const boundingBoxToUse = (_a = element === null || element === void 0 ? void 0 : element.getBoundingClientRect) === null || _a === void 0 ? void 0 : _a.call(element);
            if (boundingBoxToUse) {
                result = {
                    left: boundingBoxToUse.left,
                    top: boundingBoxToUse.top,
                    width: boundingBoxToUse.width,
                    height: boundingBoxToUse.height,
                };
            }
        }
        // Cache the result
        boundingBoxCache[elementKey] = result;
        return result;
    };
    const createOutlinesForElementKey = (elementKey, selected, isChild, outlineChildren) => {
        var _a, _b;
        const navNode = elementKeyToNavNode[elementKey];
        if (!navNode) {
            return;
        }
        const tagNameToUse = navNode === null || navNode === void 0 ? void 0 : navNode.name;
        const boundingBox = getBoundingBoxForElementKey(elementKey);
        if (boundingBox) {
            fragment.appendChild((0, exports.getOutlineElement)(parentPort, isChild ? OutlineType.CHILD : OutlineType.PRIMARY, boundingBox.left, boundingBox.top, boundingBox.width, boundingBox.height, selected, tagNameToUse, navNode === null || navNode === void 0 ? void 0 : navNode.isComponent, elementKey));
            const mouseDragData = (0, sessionStorageUtils_1.getMemoryStorageItem)('mouseDragContext');
            const mousePosData = (0, sessionStorageUtils_1.getMemoryStorageItem)('mousePos');
            if (selected && (mouseDragData === null || mouseDragData === void 0 ? void 0 : mouseDragData.dragging) && mousePosData) {
                fragment.appendChild((0, exports.getOutlineElement)(parentPort, OutlineType.MOVE, mousePosData.pageX - boundingBox.width / 2 + mouseDragData.offsetX, mousePosData.pageY - boundingBox.height / 2 + mouseDragData.offsetY, boundingBox.width, boundingBox.height, undefined, undefined, navNode === null || navNode === void 0 ? void 0 : navNode.isComponent, elementKey));
            }
        }
        if (outlineChildren) {
            (_b = (_a = navNode === null || navNode === void 0 ? void 0 : navNode.children) === null || _a === void 0 ? void 0 : _a.forEach) === null || _b === void 0 ? void 0 : _b.call(_a, (child) => {
                createOutlinesForElementKey(child.tempoElement.getKey(), false, true, false);
            });
        }
    };
    if (hoveredElementKey) {
        createOutlinesForElementKey(hoveredElementKey, false, false, true);
    }
    if (multiselectedElementKeys === null || multiselectedElementKeys === void 0 ? void 0 : multiselectedElementKeys.length) {
        // More efficient bounding box calculation for multiple elements
        let minLeft = Infinity;
        let minTop = Infinity;
        let maxRight = -Infinity;
        let maxBottom = -Infinity;
        let validBoundingBoxFound = false;
        // Process all elements in a single loop
        for (const elementKey of multiselectedElementKeys) {
            const boundingBox = getBoundingBoxForElementKey(elementKey);
            if (boundingBox) {
                validBoundingBoxFound = true;
                const right = boundingBox.left + boundingBox.width;
                const bottom = boundingBox.top + boundingBox.height;
                minLeft = Math.min(minLeft, boundingBox.left);
                minTop = Math.min(minTop, boundingBox.top);
                maxRight = Math.max(maxRight, right);
                maxBottom = Math.max(maxBottom, bottom);
            }
        }
        // Create the full bounding box if we found at least one valid box
        if (validBoundingBoxFound) {
            const fullBoundingBox = {
                left: minLeft,
                top: minTop,
                width: maxRight - minLeft,
                height: maxBottom - minTop,
            };
            fragment.appendChild((0, exports.getOutlineElement)(parentPort, OutlineType.PRIMARY, fullBoundingBox.left, fullBoundingBox.top, fullBoundingBox.width, fullBoundingBox.height, true, `${multiselectedElementKeys.length} Elements`, false));
        }
        multiselectedElementKeys.forEach((elementKey) => {
            createOutlinesForElementKey(elementKey, false, false, false);
        });
    }
    else if (selectedElementKey) {
        createOutlinesForElementKey(selectedElementKey, true, false, false);
    }
    // Create outlines
    $instantDivDrawElements.each((index, item) => {
        const boundingRect = item.getBoundingClientRect();
        fragment.appendChild((0, exports.getOutlineElement)(parentPort, OutlineType.PRIMARY, boundingRect.left, boundingRect.top, boundingRect.width, boundingRect.height));
    });
    $outlineUntilRefreshElements.each((index, item) => {
        const boundingRect = item.getBoundingClientRect();
        fragment.appendChild((0, exports.getOutlineElement)(parentPort, OutlineType.PRIMARY, boundingRect.left, boundingRect.top, boundingRect.width, boundingRect.height));
    });
    // Create secondary outlines for all matching IDs in the codebase for the clicked element
    if (selectedElement === null || selectedElement === void 0 ? void 0 : selectedElement.codebaseId) {
        let $selectedCodebaseElements = $body.find(`.${selectedElement === null || selectedElement === void 0 ? void 0 : selectedElement.codebaseId}`);
        if (selectedElementKey) {
            const node = (0, identifierUtils_1.getNodeForElementKey)(selectedElementKey);
            if (node) {
                $selectedCodebaseElements = $selectedCodebaseElements.not(node);
            }
        }
        if (hoveredElementKey) {
            const node = (0, identifierUtils_1.getNodeForElementKey)(hoveredElementKey);
            if (node) {
                $selectedCodebaseElements = $selectedCodebaseElements.not(node);
            }
        }
        $selectedCodebaseElements.each((index, item) => {
            const boundingRect = item.getBoundingClientRect();
            fragment.appendChild((0, exports.getOutlineElement)(parentPort, OutlineType.SECONDARY, boundingRect.left, boundingRect.top, boundingRect.width, boundingRect.height));
        });
    }
    // Append the fragment to the body in one operation
    body.appendChild(fragment);
};
exports.updateOutlines = updateOutlines;
const isNodeOutline = (node) => {
    if (!(node === null || node === void 0 ? void 0 : node.classList)) {
        return false;
    }
    let isOutline = false;
    node.classList.forEach((cls) => {
        if (cls === identifierUtils_1.OUTLINE_CLASS) {
            isOutline = true;
        }
    });
    return isOutline;
};
exports.isNodeOutline = isNodeOutline;
//# sourceMappingURL=data:application/json;base64,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