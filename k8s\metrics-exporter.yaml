apiVersion: apps/v1
kind: Deployment
metadata:
  name: streamscale-metrics-exporter
  labels:
    app: streamscale
    component: metrics-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: streamscale
      component: metrics-exporter
  template:
    metadata:
      labels:
        app: streamscale
        component: metrics-exporter
    spec:
      containers:
      - name: metrics-exporter
        image: ${METRICS_EXPORTER_IMAGE}:${METRICS_EXPORTER_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 9090
          name: metrics
        env:
        - name: REDIS_ADDR
          valueFrom:
            configMapKeyRef:
              name: streamscale-config
              key: redis.addr
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: streamscale-secrets
              key: redis.password
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "200m"
            memory: "256Mi"
---
apiVersion: v1
kind: Service
metadata:
  name: streamscale-metrics-exporter
  labels:
    app: streamscale
    component: metrics-exporter
spec:
  selector:
    app: streamscale
    component: metrics-exporter
  ports:
  - port: 9090
    targetPort: 9090
    name: metrics
  type: ClusterIP
