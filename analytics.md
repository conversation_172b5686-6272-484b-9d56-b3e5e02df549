# StreamScale Analytics API Documentation

This document provides examples of how to interact with the StreamScale Analytics API using curl commands.

## Prerequisites

Before using these API endpoints, you need:

1. A valid authentication token (for authenticated endpoints)
2. The base URL of your API (replace `http://localhost:8080` with your actual API URL)
3. Valid video IDs from your system

## Authentication

For endpoints that require authentication, include the JWT token in the Authorization header:

```
-H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Replace `YOUR_JWT_TOKEN` with an actual token obtained from the login endpoint.

## API Endpoints

### 1. Get Analytics Summary

Retrieves a summary of analytics for the authenticated user, including total videos, views, watch time, and top-performing videos.

```bash
curl -X GET \
  http://localhost:8080/api/v1/analytics/summary \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Example response:
```json
{
  "total_videos": 15,
  "total_views": 1250,
  "total_watch_time": 45600,
  "avg_engagement_score": 72.5,
  "recent_videos": [
    {
      "video_id": "550e8400-e29b-41d4-a716-446655440000",
      "title": "Introduction to StreamScale",
      "duration": 180,
      "total_views": 320,
      "unique_views": 280,
      "total_watch_time": 15600,
      "avg_watch_time": 48.75,
      "completion_rate": 85.5,
      "engagement_score": 78.2,
      "views_last_7_days": 120,
      "views_last_30_days": 320,
      "watch_time_last_7_days": 5400,
      "watch_time_last_30_days": 15600,
      "thumbnail_url": "https://example.com/thumbnails/intro.jpg",
      "created_at": "2023-05-15T10:30:00Z"
    }
  ],
  "top_videos": [
    {
      "video_id": "550e8400-e29b-41d4-a716-446655440001",
      "title": "Advanced StreamScale Features",
      "duration": 240,
      "total_views": 450,
      "unique_views": 380,
      "total_watch_time": 21600,
      "avg_watch_time": 48.0,
      "completion_rate": 92.0,
      "engagement_score": 85.5,
      "views_last_7_days": 150,
      "views_last_30_days": 450,
      "watch_time_last_7_days": 7200,
      "watch_time_last_30_days": 21600,
      "thumbnail_url": "https://example.com/thumbnails/advanced.jpg",
      "created_at": "2023-04-20T14:15:00Z"
    }
  ]
}
```

### 2. Record a Video View

Records a new view for a video. Authentication is optional - if provided, the view will be associated with the user.

```bash
curl -X POST \
  http://localhost:8080/api/v1/analytics/views \
  -H "Content-Type: application/json" \
  -d '{
    "video_id": "550e8400-e29b-41d4-a716-446655440000",
    "duration": 180
  }'
```

With authentication:

```bash
curl -X POST \
  http://localhost:8080/api/v1/analytics/views \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "video_id": "550e8400-e29b-41d4-a716-446655440000",
    "duration": 180
  }'
```

Example response:
```json
{
  "id": 12345,
  "video_id": "550e8400-e29b-41d4-a716-446655440000",
  "user_id": "7f8d9e10-a1b2-c3d4-e5f6-123456789012",
  "ip": "***********",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "timestamp": "2023-06-10T15:30:45Z",
  "duration": 180
}
```

### 3. Get Video Views

Retrieves views for a specific video. Requires authentication.

```bash
curl -X GET \
  "http://localhost:8080/api/v1/analytics/videos/550e8400-e29b-41d4-a716-446655440000/views?start_date=2023-01-01&end_date=2023-06-30&limit=10&offset=0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Example response:
```json
[
  {
    "id": 12345,
    "video_id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "7f8d9e10-a1b2-c3d4-e5f6-123456789012",
    "ip": "***********",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "timestamp": "2023-06-10T15:30:45Z",
    "duration": 180
  },
  {
    "id": 12344,
    "video_id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "7f8d9e10-a1b2-c3d4-e5f6-123456789013",
    "ip": "***********",
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    "timestamp": "2023-06-09T12:15:30Z",
    "duration": 165
  }
]
```

### 4. Start a Watch Session

Starts a new watch session for a video. Authentication is optional.

```bash
curl -X POST \
  http://localhost:8080/api/v1/analytics/sessions/start \
  -H "Content-Type: application/json" \
  -d '{
    "video_id": "550e8400-e29b-41d4-a716-446655440000"
  }'
```

Example response:
```json
{
  "id": 5678,
  "video_id": "550e8400-e29b-41d4-a716-446655440000",
  "user_id": "7f8d9e10-a1b2-c3d4-e5f6-123456789012",
  "session_id": "session-9876-5432-1098",
  "start_time": "2023-06-10T16:00:00Z",
  "end_time": "2023-06-10T16:00:00Z",
  "watch_duration": 0,
  "completed": false
}
```

### 5. End a Watch Session

Ends a watch session and records metrics. The session_id must match the one returned from the start session endpoint.

```bash
curl -X POST \
  http://localhost:8080/api/v1/analytics/sessions/end \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "session-9876-5432-1098",
    "watch_duration": 175,
    "completed": true
  }'
```

Example response:
```json
{
  "message": "Session ended successfully"
}
```

### 6. Get Video Performance Metrics

Retrieves performance metrics for a specific video. Requires authentication.

```bash
curl -X GET \
  http://localhost:8080/api/v1/analytics/videos/550e8400-e29b-41d4-a716-446655440000/performance \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Example response:
```json
{
  "video_id": "550e8400-e29b-41d4-a716-446655440000",
  "title": "Introduction to StreamScale",
  "duration": 180,
  "total_views": 320,
  "unique_views": 280,
  "total_watch_time": 15600,
  "avg_watch_time": 48.75,
  "completion_rate": 85.5,
  "engagement_score": 78.2,
  "views_last_7_days": 120,
  "views_last_30_days": 320,
  "watch_time_last_7_days": 5400,
  "watch_time_last_30_days": 15600,
  "thumbnail_url": "https://example.com/thumbnails/intro.jpg",
  "created_at": "2023-05-15T10:30:00Z"
}
```

### 7. Get Top Performing Videos

Retrieves top performing videos for the authenticated user.

```bash
curl -X GET \
  "http://localhost:8080/api/v1/analytics/videos/top?limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Example response:
```json
[
  {
    "video_id": "550e8400-e29b-41d4-a716-446655440001",
    "title": "Advanced StreamScale Features",
    "duration": 240,
    "total_views": 450,
    "unique_views": 380,
    "total_watch_time": 21600,
    "avg_watch_time": 48.0,
    "completion_rate": 92.0,
    "engagement_score": 85.5,
    "views_last_7_days": 150,
    "views_last_30_days": 450,
    "watch_time_last_7_days": 7200,
    "watch_time_last_30_days": 21600,
    "thumbnail_url": "https://example.com/thumbnails/advanced.jpg",
    "created_at": "2023-04-20T14:15:00Z"
  },
  {
    "video_id": "550e8400-e29b-41d4-a716-446655440000",
    "title": "Introduction to StreamScale",
    "duration": 180,
    "total_views": 320,
    "unique_views": 280,
    "total_watch_time": 15600,
    "avg_watch_time": 48.75,
    "completion_rate": 85.5,
    "engagement_score": 78.2,
    "views_last_7_days": 120,
    "views_last_30_days": 320,
    "watch_time_last_7_days": 5400,
    "watch_time_last_30_days": 15600,
    "thumbnail_url": "https://example.com/thumbnails/intro.jpg",
    "created_at": "2023-05-15T10:30:00Z"
  }
]
```

### 8. Get Recent Videos

Retrieves recent videos for the authenticated user.

```bash
curl -X GET \
  "http://localhost:8080/api/v1/analytics/videos/recent?limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Example response:
```json
[
  {
    "video_id": "550e8400-e29b-41d4-a716-446655440000",
    "title": "Introduction to StreamScale",
    "duration": 180,
    "total_views": 320,
    "unique_views": 280,
    "total_watch_time": 15600,
    "avg_watch_time": 48.75,
    "completion_rate": 85.5,
    "engagement_score": 78.2,
    "views_last_7_days": 120,
    "views_last_30_days": 320,
    "watch_time_last_7_days": 5400,
    "watch_time_last_30_days": 15600,
    "thumbnail_url": "https://example.com/thumbnails/intro.jpg",
    "created_at": "2023-05-15T10:30:00Z"
  },
  {
    "video_id": "550e8400-e29b-41d4-a716-************",
    "title": "StreamScale Integration Guide",
    "duration": 210,
    "total_views": 180,
    "unique_views": 150,
    "total_watch_time": 8400,
    "avg_watch_time": 46.67,
    "completion_rate": 80.0,
    "engagement_score": 72.3,
    "views_last_7_days": 80,
    "views_last_30_days": 180,
    "watch_time_last_7_days": 3600,
    "watch_time_last_30_days": 8400,
    "thumbnail_url": "https://example.com/thumbnails/integration.jpg",
    "created_at": "2023-05-10T09:45:00Z"
  }
]
```

## Error Responses

All endpoints may return the following error responses:

- 400 Bad Request: Invalid input parameters
- 401 Unauthorized: Missing or invalid authentication token
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Resource not found
- 500 Internal Server Error: Server-side error

Example error response:
```json
{
  "error": "Bad Request",
  "message": "Invalid video ID format",
  "status": 400
}
```

## Notes

- All timestamps are in ISO 8601 format (UTC)
- Duration values are in seconds
- For date range filters, use the format YYYY-MM-DD
