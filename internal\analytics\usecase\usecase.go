package usecase

import (
	"context"
	"math"
	"time"

	"github.com/amankumarsingh77/cloud-video-encoder/internal/analytics"
	"github.com/amankumarsingh77/cloud-video-encoder/internal/models"
	"github.com/amankumarsingh77/cloud-video-encoder/pkg/logger"
	"github.com/google/uuid"
)

// AnalyticsUseCase implements the analytics.UseCase interface
type AnalyticsUseCase struct {
	repo   analytics.Repository
	logger logger.Logger
}

// NewAnalyticsUseCase creates a new AnalyticsUseCase
func NewAnalyticsUseCase(repo analytics.Repository, logger logger.Logger) analytics.UseCase {
	return &AnalyticsUseCase{
		repo:   repo,
		logger: logger,
	}
}

// RecordVideoView records a new video view
func (u *AnalyticsUseCase) RecordVideoView(ctx context.Context, view *models.VideoView) error {
	// Set timestamp if not provided
	if view.Timestamp.IsZero() {
		view.Timestamp = time.Now()
	}

	// Record the view
	err := u.repo.CreateVideoView(ctx, view)
	if err != nil {
		return err
	}

	// Recalculate engagement metrics asynchronously
	go func() {
		// Create a new context since the original one might be canceled
		newCtx := context.Background()
		_, err := u.CalculateEngagement(newCtx, view.VideoID)
		if err != nil {
			u.logger.Errorf("Error calculating engagement after view: %v", err)
		}
	}()

	return nil
}

// GetVideoViews retrieves video views based on filter
func (u *AnalyticsUseCase) GetVideoViews(ctx context.Context, videoID uuid.UUID, filter *models.AnalyticsFilter) ([]*models.VideoView, error) {
	return u.repo.GetVideoViews(ctx, videoID, filter)
}

// StartWatchSession starts a new watch session
func (u *AnalyticsUseCase) StartWatchSession(ctx context.Context, videoID, userID uuid.UUID, sessionID string) (*models.VideoWatchSession, error) {
	session := &models.VideoWatchSession{
		VideoID:       videoID,
		UserID:        userID,
		SessionID:     sessionID,
		StartTime:     time.Now(),
		EndTime:       time.Now(), // Will be updated when session ends
		WatchDuration: 0,
		Completed:     false,
	}

	err := u.repo.CreateWatchSession(ctx, session)
	if err != nil {
		return nil, err
	}

	return session, nil
}

// EndWatchSession ends a watch session
func (u *AnalyticsUseCase) EndWatchSession(ctx context.Context, sessionID string, watchDuration int64, completed bool) error {
	// Get the session first to get the video ID
	filter := &models.AnalyticsFilter{
		Limit: 1,
	}

	// This is a simplification - in a real implementation, you'd query by session ID
	// For now, we'll assume the session ID is passed correctly
	sessions, err := u.repo.GetWatchSessions(ctx, uuid.Nil, filter)
	if err != nil || len(sessions) == 0 {
		return err
	}

	session := sessions[0]
	session.EndTime = time.Now()
	session.WatchDuration = watchDuration
	session.Completed = completed

	err = u.repo.UpdateWatchSession(ctx, session)
	if err != nil {
		return err
	}

	// Record the view with the watch duration
	view := &models.VideoView{
		VideoID:   session.VideoID,
		UserID:    session.UserID,
		Timestamp: session.StartTime,
		Duration:  watchDuration,
	}

	err = u.repo.CreateVideoView(ctx, view)
	if err != nil {
		return err
	}

	// Recalculate engagement metrics asynchronously
	go func() {
		// Create a new context since the original one might be canceled
		newCtx := context.Background()
		_, err := u.CalculateEngagement(newCtx, session.VideoID)
		if err != nil {
			u.logger.Errorf("Error calculating engagement after session: %v", err)
		}
	}()

	return nil
}

// CalculateEngagement calculates engagement metrics for a video
func (u *AnalyticsUseCase) CalculateEngagement(ctx context.Context, videoID uuid.UUID) (*models.VideoEngagement, error) {
	// Get total views
	totalViews, err := u.repo.GetTotalVideoViews(ctx, videoID)
	if err != nil {
		return nil, err
	}

	// Get unique views
	uniqueViews, err := u.repo.GetUniqueVideoViews(ctx, videoID)
	if err != nil {
		return nil, err
	}

	// Get watch sessions to calculate watch time and completion rate
	filter := &models.AnalyticsFilter{
		VideoID: videoID,
	}
	sessions, err := u.repo.GetWatchSessions(ctx, videoID, filter)
	if err != nil {
		return nil, err
	}

	// Calculate total watch time and completion rate
	var totalWatchTime int64
	var completedSessions int

	for _, session := range sessions {
		totalWatchTime += session.WatchDuration
		if session.Completed {
			completedSessions++
		}
	}

	// Calculate average watch time
	var avgWatchTime float64
	if totalViews > 0 {
		avgWatchTime = float64(totalWatchTime) / float64(totalViews)
	}

	// Calculate completion rate
	var completionRate float64
	if len(sessions) > 0 {
		completionRate = float64(completedSessions) / float64(len(sessions)) * 100
	}

	// Calculate engagement score (custom formula)
	// This is a simple example - you can create a more sophisticated formula
	engagementScore := calculateEngagementScore(totalViews, uniqueViews, avgWatchTime, completionRate)

	// Create engagement object
	engagement := &models.VideoEngagement{
		VideoID:          videoID,
		TotalViews:       totalViews,
		UniqueViews:      uniqueViews,
		TotalWatchTime:   totalWatchTime,
		AvgWatchTime:     avgWatchTime,
		CompletionRate:   completionRate,
		EngagementScore:  engagementScore,
		LastCalculatedAt: time.Now(),
	}

	// Update engagement in database
	err = u.repo.UpdateVideoEngagement(ctx, engagement)
	if err != nil {
		return nil, err
	}

	return engagement, nil
}

// calculateEngagementScore calculates an engagement score based on various metrics
func calculateEngagementScore(totalViews, uniqueViews int64, avgWatchTime, completionRate float64) float64 {
	// Normalize values to a 0-1 scale
	// These thresholds should be adjusted based on your specific use case
	normalizedViews := math.Min(float64(totalViews)/1000, 1.0)
	normalizedUniqueViews := math.Min(float64(uniqueViews)/500, 1.0)
	normalizedWatchTime := math.Min(avgWatchTime/300, 1.0) // Assuming 300 seconds is a good average
	normalizedCompletionRate := completionRate / 100

	// Weight factors (adjust based on importance)
	const (
		viewsWeight          = 0.2
		uniqueViewsWeight    = 0.2
		watchTimeWeight      = 0.3
		completionRateWeight = 0.3
	)

	// Calculate weighted score
	score := (normalizedViews * viewsWeight) +
		(normalizedUniqueViews * uniqueViewsWeight) +
		(normalizedWatchTime * watchTimeWeight) +
		(normalizedCompletionRate * completionRateWeight)

	// Scale to 0-100
	return score * 100
}

// GetVideoEngagement retrieves engagement metrics for a video
func (u *AnalyticsUseCase) GetVideoEngagement(ctx context.Context, videoID uuid.UUID) (*models.VideoEngagement, error) {
	return u.repo.GetVideoEngagement(ctx, videoID)
}

// GetVideoPerformance retrieves performance metrics for a video
func (u *AnalyticsUseCase) GetVideoPerformance(ctx context.Context, videoID uuid.UUID) (*models.VideoPerformance, error) {
	return u.repo.GetVideoPerformance(ctx, videoID)
}

// GetTopPerformingVideos retrieves top performing videos for a user
func (u *AnalyticsUseCase) GetTopPerformingVideos(ctx context.Context, userID uuid.UUID, limit int) ([]*models.VideoPerformance, error) {
	return u.repo.GetTopPerformingVideos(ctx, userID, limit)
}

// GetRecentVideos retrieves recent videos for a user
func (u *AnalyticsUseCase) GetRecentVideos(ctx context.Context, userID uuid.UUID, limit int) ([]*models.VideoPerformance, error) {
	return u.repo.GetRecentVideos(ctx, userID, limit)
}

// GetAnalyticsSummary retrieves analytics summary for a user
func (u *AnalyticsUseCase) GetAnalyticsSummary(ctx context.Context, userID uuid.UUID) (*models.AnalyticsSummary, error) {
	return u.repo.GetAnalyticsSummary(ctx, userID)
}

// GetTotalVideos gets the total number of videos for a user
func (u *AnalyticsUseCase) GetTotalVideos(ctx context.Context, userID uuid.UUID) (int64, error) {
	return u.repo.GetTotalVideos(ctx, userID)
}

// GetTotalWatchTime gets the total watch time for a user's videos
func (u *AnalyticsUseCase) GetTotalWatchTime(ctx context.Context, userID uuid.UUID) (int64, error) {
	return u.repo.GetTotalWatchTime(ctx, userID)
}
