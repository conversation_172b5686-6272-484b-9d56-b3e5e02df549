{"version": 3, "sources": ["../../@radix-ui/react-context/src/createContext.tsx", "../../@radix-ui/primitive/src/primitive.tsx", "../../@radix-ui/react-use-callback-ref/src/useCallbackRef.tsx", "../../@radix-ui/react-use-controllable-state/src/useControllableState.tsx", "../../@radix-ui/react-use-layout-effect/src/useLayoutEffect.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  function Provider(props: ContextValueType & { children: React.ReactNode }) {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  }\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  Provider.displayName = rootComponentName + 'Provider';\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    function Provider(\n      props: ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    ) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    }\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    Provider.displayName = rootComponentName + 'Provider';\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n", "function composeEventHandlers<E>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !((event as unknown) as Event).defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n", "import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n\n  return [value, setValue] as const;\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\n\nexport { useControllableState };\n", "import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = Boolean(globalThis?.document) ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n"], "mappings": ";;;;;;;;;;;AAAA,YAAuB;AAaZ,yBAAA;AAXX,SAASA,eACP,mBACA,gBACA;AACA,QAAM,UAAgB,oBAA4C,cAAc;AAEhF,WAAS,SAAS,OAAyD;AACzE,UAAM,EAAE,UAAU,GAAG,QAAQ,IAAI;AAGjC,UAAM,QAAc,cAAQ,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC;AACjE,eAAO,wBAAC,QAAQ,UAAR,EAAiB,OAAe,SAAA,CAAS;EACnD;AAEA,WAASC,YAAW,cAAsB;AACxC,UAAM,UAAgB,iBAAW,OAAO;AACxC,QAAI,QAAS,QAAO;AACpB,QAAI,mBAAmB,OAAW,QAAO;AAEzC,UAAM,IAAI,MAAM,KAAK,YAAY,4BAA4B,iBAAiB,IAAI;EACpF;AAEA,WAAS,cAAc,oBAAoB;AAC3C,SAAO,CAAC,UAAUA,WAAU;AAC9B;AAaA,SAAS,mBAAmB,WAAmB,yBAAwC,CAAC,GAAG;AACzF,MAAI,kBAAyB,CAAC;AAM9B,WAASD,eACP,mBACA,gBACA;AACA,UAAM,cAAoB,oBAA4C,cAAc;AACpF,UAAM,QAAQ,gBAAgB;AAC9B,sBAAkB,CAAC,GAAG,iBAAiB,cAAc;AAErD,aAAS,SACP,OACA;AACA,YAAM,EAAE,OAAO,UAAU,GAAG,QAAQ,IAAI;AACxC,YAAM,WAAU,+BAAQ,WAAW,WAAU;AAG7C,YAAM,QAAc,cAAQ,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC;AACjE,iBAAO,wBAAC,QAAQ,UAAR,EAAiB,OAAe,SAAA,CAAS;IACnD;AAEA,aAASC,YAAW,cAAsB,OAA4C;AACpF,YAAM,WAAU,+BAAQ,WAAW,WAAU;AAC7C,YAAM,UAAgB,iBAAW,OAAO;AACxC,UAAI,QAAS,QAAO;AACpB,UAAI,mBAAmB,OAAW,QAAO;AAEzC,YAAM,IAAI,MAAM,KAAK,YAAY,4BAA4B,iBAAiB,IAAI;IACpF;AAEA,aAAS,cAAc,oBAAoB;AAC3C,WAAO,CAAC,UAAUA,WAAU;EAC9B;AAMA,QAAM,cAA2B,MAAM;AACrC,UAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,aAAa,oBAAc,cAAc;IAC3C,CAAC;AACD,WAAO,SAAS,SAAS,OAAc;AACrC,YAAM,YAAW,+BAAQ,eAAc;AACvC,aAAa;QACX,OAAO,EAAE,CAAC,UAAU,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,GAAG,SAAS,EAAE;QACtE,CAAC,OAAO,QAAQ;MAClB;IACF;EACF;AAEA,cAAY,YAAY;AACxB,SAAO,CAACD,gBAAe,qBAAqB,aAAa,GAAG,sBAAsB,CAAC;AACrF;AAMA,SAAS,wBAAwB,QAAuB;AACtD,QAAM,YAAY,OAAO,CAAC;AAC1B,MAAI,OAAO,WAAW,EAAG,QAAO;AAEhC,QAAM,cAA2B,MAAM;AACrC,UAAM,aAAa,OAAO,IAAI,CAACE,kBAAiB;MAC9C,UAAUA,aAAY;MACtB,WAAWA,aAAY;IACzB,EAAE;AAEF,WAAO,SAAS,kBAAkB,gBAAgB;AAChD,YAAM,aAAa,WAAW,OAAO,CAACC,aAAY,EAAE,UAAU,UAAU,MAAM;AAI5E,cAAM,aAAa,SAAS,cAAc;AAC1C,cAAM,eAAe,WAAW,UAAU,SAAS,EAAE;AACrD,eAAO,EAAE,GAAGA,aAAY,GAAG,aAAa;MAC1C,GAAG,CAAC,CAAC;AAEL,aAAa,cAAQ,OAAO,EAAE,CAAC,UAAU,UAAU,SAAS,EAAE,GAAG,WAAW,IAAI,CAAC,UAAU,CAAC;IAC9F;EACF;AAEA,cAAY,YAAY,UAAU;AAClC,SAAO;AACT;;;ACjIA,SAAS,qBACP,sBACA,iBACA,EAAE,2BAA2B,KAAK,IAAI,CAAC,GACvC;AACA,SAAO,SAAS,YAAY,OAAU;AACpC,iEAAuB;AAEvB,QAAI,6BAA6B,SAAS,CAAG,MAA4B,kBAAkB;AACzF,aAAO,mDAAkB;IAC3B;EACF;AACF;;;ACZA,IAAAC,SAAuB;AAMvB,SAAS,eAAkD,UAA4B;AACrF,QAAM,cAAoB,cAAO,QAAQ;AAEnC,EAAA,iBAAU,MAAM;AACpB,gBAAY,UAAU;EACxB,CAAC;AAGD,SAAa,eAAQ,MAAO,IAAI,SAAA;;AAAS,6BAAY,YAAZ,qCAAsB,GAAG;KAAa,CAAC,CAAC;AACnF;;;ACfA,IAAAC,SAAuB;AAWvB,SAAS,qBAAwB;EAC/B;EACA;EACA,WAAW,MAAM;EAAC;AACpB,GAAkC;AAChC,QAAM,CAAC,kBAAkB,mBAAmB,IAAI,qBAAqB,EAAE,aAAa,SAAS,CAAC;AAC9F,QAAM,eAAe,SAAS;AAC9B,QAAM,QAAQ,eAAe,OAAO;AACpC,QAAM,eAAe,eAAe,QAAQ;AAE5C,QAAM,WAAsE;IAC1E,CAAC,cAAc;AACb,UAAI,cAAc;AAChB,cAAM,SAAS;AACf,cAAMC,SAAQ,OAAO,cAAc,aAAa,OAAO,IAAI,IAAI;AAC/D,YAAIA,WAAU,KAAM,cAAaA,MAAU;MAC7C,OAAO;AACL,4BAAoB,SAAS;MAC/B;IACF;IACA,CAAC,cAAc,MAAM,qBAAqB,YAAY;EACxD;AAEA,SAAO,CAAC,OAAO,QAAQ;AACzB;AAEA,SAAS,qBAAwB;EAC/B;EACA;AACF,GAAgD;AAC9C,QAAM,oBAA0B,gBAAwB,WAAW;AACnE,QAAM,CAAC,KAAK,IAAI;AAChB,QAAM,eAAqB,cAAO,KAAK;AACvC,QAAM,eAAe,eAAe,QAAQ;AAEtC,EAAA,iBAAU,MAAM;AACpB,QAAI,aAAa,YAAY,OAAO;AAClC,mBAAa,KAAU;AACvB,mBAAa,UAAU;IACzB;EACF,GAAG,CAAC,OAAO,cAAc,YAAY,CAAC;AAEtC,SAAO;AACT;;;ACtDA,IAAAC,SAAuB;AASvB,IAAMC,mBAAkB,QAAQ,yCAAY,QAAQ,IAAU,yBAAkB,MAAM;AAAC;", "names": ["createContext", "useContext", "createScope", "nextScopes", "React", "React", "value", "React", "useLayoutEffect"]}