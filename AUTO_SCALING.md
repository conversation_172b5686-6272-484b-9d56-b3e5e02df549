# StreamScale Auto-Scaling Worker Deployment

This document explains how the auto-scaling worker deployment works in StreamScale.

## Overview

The StreamScale worker auto-scaling system automatically scales worker pods up and down based on the number of jobs in the Redis queue. When there are no jobs, the system scales down to zero workers to save resources. When jobs are added to the queue, it automatically scales up workers to process them.

## Components

The auto-scaling system consists of the following components:

1. **Worker Deployment**: Kubernetes deployment for the worker containers that process video encoding jobs.
2. **Metrics Exporter**: A small service that monitors the Redis job queue and exposes metrics about queue length.
3. **Horizontal Pod Autoscaler (HPA)**: Kubernetes HPA that scales the worker deployment based on the queue metrics.
4. **Prometheus Adapter**: Converts the custom metrics from the metrics exporter into a format that Kubernetes can use for scaling.

## How It Works

1. The metrics exporter continuously monitors the Redis queue length and exposes it as a Prometheus metric.
2. The Prometheus Adapter makes this metric available to Kubernetes as a custom metric.
3. The HPA watches this metric and scales the worker deployment up or down based on the queue length.
4. When jobs are added to the queue, the HPA detects the increase in queue length and scales up worker pods.
5. When all jobs are processed and the queue is empty, the HPA scales down the worker pods to zero after a stabilization period.

## Configuration

The auto-scaling behavior can be configured through the following files:

- `k8s/worker-hpa.yaml`: Contains the HPA configuration, including scaling thresholds and behavior.
- `k8s/metrics-exporter.yaml`: Contains the deployment configuration for the metrics exporter.
- `k8s/prometheus-adapter-config.yaml`: Contains the configuration for the Prometheus Adapter.

Key configuration parameters:

- `minReplicas`: Minimum number of worker pods (0 for scale-to-zero).
- `maxReplicas`: Maximum number of worker pods.
- `averageValue`: Target value for the queue length per pod.
- `stabilizationWindowSeconds`: How long to wait before scaling down.

## Deployment

To deploy the auto-scaling system:

1. Make sure you have a Kubernetes cluster with the Prometheus Operator and Prometheus Adapter installed.
2. Copy the `.env.example` file to `.env.dev` (or another environment name) and fill in the required values.
3. Run the deployment script:

```bash
./scripts/deploy.sh
```

## Monitoring

You can monitor the auto-scaling behavior using the following commands:

```bash
# Check the HPA status
kubectl get hpa streamscale-worker-hpa

# Check the worker deployment
kubectl get deployment streamscale-worker

# Check the metrics exporter logs
kubectl logs -l app=streamscale,component=metrics-exporter

# Check the custom metrics
kubectl get --raw "/apis/custom.metrics.k8s.io/v1beta1/namespaces/default/pods/*/redis_queue_length"
```

## Troubleshooting

If the auto-scaling is not working as expected, check the following:

1. Make sure the metrics exporter can connect to Redis and is reporting metrics correctly.
2. Verify that the Prometheus Adapter is correctly configured and can see the metrics.
3. Check the HPA status for any error messages.
4. Ensure that the worker deployment is correctly configured and can be scaled by the HPA.
