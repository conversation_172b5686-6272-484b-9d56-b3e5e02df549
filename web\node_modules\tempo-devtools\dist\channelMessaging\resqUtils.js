"use strict";
// Code taken and adapted from the resq library: https://github.com/baruchvlz/resq
Object.defineProperty(exports, "__esModule", { value: true });
exports.findElementInTree = exports.getElementName = exports.getDomElementForReactNode = exports.buildNodeTree = exports.getElementNameString = exports.getRootReactElement = exports.findReactInstance = void 0;
/**
 * Taken from https://github.com/baruchvlz/resq/blob/master/src/utils.js
 * but improved to work with all versions of react
 */
const findReactInstance = (element) => {
    if (element.hasOwnProperty('_reactRootContainer')) {
        if (element._reactRootContainer._internalRoot) {
            return element._reactRootContainer._internalRoot.current;
        }
        else {
            return element._reactRootContainer.current;
        }
    }
    const instanceId = Object.keys(element).find((key) => key.startsWith('__reactInternalInstance') ||
        key.startsWith('__reactFiber') ||
        key.startsWith('__reactContainer'));
    if (instanceId) {
        return element[instanceId];
    }
};
exports.findReactInstance = findReactInstance;
//Returns true if it is a DOM element
function isElement(o) {
    return typeof HTMLElement === 'object'
        ? o instanceof HTMLElement //DOM2
        : o &&
            typeof o === 'object' &&
            o !== null &&
            o.nodeType === 1 &&
            typeof o.nodeName === 'string';
}
const getRootReactElement = () => {
    var _a;
    let rootSelector = '#root';
    if (!document.querySelector(rootSelector)) {
        rootSelector = '#__next';
    }
    const root = document.querySelector(rootSelector);
    let findInstance = null;
    if (root) {
        findInstance = (0, exports.findReactInstance)(root);
    }
    else {
        // hacky fallback; if there's no root element so we grab the first one recursively
        function findInstanceInNode(node) {
            var _a;
            if (findInstance) {
                return;
            }
            findInstance = (0, exports.findReactInstance)(node);
            if (findInstance) {
                return;
            }
            (_a = node.childNodes) === null || _a === void 0 ? void 0 : _a.forEach((childNode) => {
                findInstanceInNode(childNode);
            });
        }
        findInstanceInNode(document.getElementsByTagName('body')[0]);
    }
    // June 12 2024 fix:
    // Sometimes the react tree only loads correctly in the "alternate slot"
    // Replace the current tree with the alternate tree if that is the case
    if (findInstance && !findInstance.child && ((_a = findInstance.alternate) === null || _a === void 0 ? void 0 : _a.child)) {
        findInstance = findInstance.alternate;
    }
    return findInstance;
};
exports.getRootReactElement = getRootReactElement;
const removeChildrenFromProps = (props) => {
    // if the props is a string, we can assume that it's just the text inside a html element
    if (!props || typeof props === 'string') {
        return props;
    }
    const returnProps = Object.assign({}, props);
    delete returnProps.children;
    return returnProps;
};
const getElementState = (elementState) => {
    if (!elementState) {
        return undefined;
    }
    const { baseState } = elementState;
    if (baseState) {
        return baseState;
    }
    return elementState;
};
const getElementNameString = (element) => {
    var _a;
    const name = (0, exports.getElementName)(element.type);
    if (typeof name !== 'string') {
        return (_a = name === null || name === void 0 ? void 0 : name.toString) === null || _a === void 0 ? void 0 : _a.call(name);
    }
    return name;
};
exports.getElementNameString = getElementNameString;
const buildNodeTree = (element, parentTreeNode) => {
    let tree = { children: [] };
    tree.element = element;
    tree.parent = parentTreeNode;
    if (!element) {
        return tree;
    }
    tree.name = (0, exports.getElementNameString)(element);
    tree.props = removeChildrenFromProps(element.memoizedProps);
    tree.state = getElementState(element.memoizedState);
    let { child } = element;
    if (child) {
        tree.children.push(child);
        while (child.sibling) {
            tree.children.push(child.sibling);
            child = child.sibling;
        }
    }
    tree.children = tree.children.map((child) => (0, exports.buildNodeTree)(child, tree));
    return tree;
};
exports.buildNodeTree = buildNodeTree;
const getDomElementForReactNode = (node) => {
    var _a, _b;
    let stateNode = (_a = node === null || node === void 0 ? void 0 : node.element) === null || _a === void 0 ? void 0 : _a.stateNode;
    if (stateNode && ((_b = stateNode === null || stateNode === void 0 ? void 0 : stateNode.constructor) === null || _b === void 0 ? void 0 : _b.name) === 'FiberRootNode') {
        stateNode = stateNode.containerInfo;
    }
    if (isElement(stateNode)) {
        return stateNode;
    }
    return null;
};
exports.getDomElementForReactNode = getDomElementForReactNode;
const isFunction = (type) => {
    return typeof type === 'function';
};
const isObject = (type) => {
    return typeof type === 'object';
};
const getElementName = (type) => {
    var _a;
    if (!type) {
        return type;
    }
    if (isFunction(type) || isObject(type)) {
        if (type.displayName) {
            if (isFunction(type.displayName)) {
                return type.displayName();
            }
            else {
                return type.displayName;
            }
        }
        if (type.name) {
            if (isFunction(type.name)) {
                return type.name();
            }
            else {
                return type.name;
            }
        }
        if ((_a = type.render) === null || _a === void 0 ? void 0 : _a.name) {
            return type.render.name;
        }
        return null;
    }
    return type;
};
exports.getElementName = getElementName;
/**
 * @param tree
 * @param searchFn
 * @param firstOnly if set, returns only the first element in a breadth-firth search
 * @returns
 */
const findElementInTree = (tree, searchFn, firstOnly) => {
    let searchQueue = [tree];
    const foundNodes = [];
    while (searchQueue.length > 0) {
        const node = searchQueue.shift();
        if (searchFn(node)) {
            foundNodes.push(node);
            if (firstOnly) {
                break;
            }
        }
        searchQueue = searchQueue.concat(node.children || []);
    }
    return foundNodes;
};
exports.findElementInTree = findElementInTree;
//# sourceMappingURL=data:application/json;base64,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