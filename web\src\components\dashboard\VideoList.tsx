import { useState, useCallback, useMemo, memo } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { VideoFile, videoApi } from "@/lib/videoApi";
import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";
import { useVideos, useSearchVideos, useDeleteVideo } from "@/hooks/useVideos";
import VideoCard from "./VideoCard";
import {
  Upload,
  Search,
  Clock,
  CheckCircle2,
  AlertTriangle,
  VideoIcon,
  X,
  Loader2,
  FileVideo,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
const VideoList = memo(() => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("date");
  const [page] = useState(1);
  const { data: videosData, isLoading, error } = useVideos(page, 50);
  const { data: searchData, isLoading: isSearching } = useSearchVideos(
    searchQuery,
    1,
    20
  );
  const deleteVideoMutation = useDeleteVideo();
  const videos = videosData?.videos || [];
  const searchResults = searchData?.videos || [];

  // Handle errors
  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center gap-4">
          <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-red-500 to-red-600 p-0.5">
            <div className="w-full h-full rounded-[14px] bg-[#0f1729] flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-red-400" />
            </div>
          </div>
          <p className="text-slate-400">Failed to load videos</p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="text-white border-slate-700 hover:bg-slate-800"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }
  const sortedAndFilteredVideos = useMemo(() => {
    let filtered = videos.filter((video) => {
      return statusFilter === "all" || video.status === statusFilter;
    });
    switch (sortBy) {
      case "date":
        filtered.sort(
          (a, b) =>
            new Date(b.uploaded_at).getTime() -
            new Date(a.uploaded_at).getTime()
        );
        break;
      case "name":
        filtered.sort((a, b) => a.file_name.localeCompare(b.file_name));
        break;
      case "size":
        filtered.sort((a, b) => b.file_size - a.file_size);
        break;
      case "duration":
        filtered.sort((a, b) => b.duration - a.duration);
        break;
    }
    return filtered;
  }, [videos, statusFilter, sortBy]);
  const clearSearch = useCallback(() => {
    setSearchQuery("");
  }, []);

  const handleSearch = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled automatically by the useSearchVideos hook
    // when searchQuery changes, so we don't need to do anything here
  }, []);
  const handleVideoSelect = useCallback(
    (video: VideoFile) => {
      navigate(`/dashboard/play/${video.video_id}`);
    },
    [navigate]
  );
  const getStatusIcon = useCallback((status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="w-4 h-4 text-green-400" />;
      case "queued":
        return <Clock className="w-4 h-4 text-yellow-200 animate-spin" />;
      case "processing":
        return <Clock className="w-4 h-4 text-yellow-400 animate-spin" />;
      case "failed":
        return <AlertTriangle className="w-4 h-4 text-red-400" />;
      default:
        return null;
    }
  }, []);
  const handleVideoAction = useCallback(
    async (action: string, video: VideoFile) => {
      switch (action) {
        case "download":
          try {
            const playbackInfo = await videoApi.getPlaybackInfo(video.video_id);
            const qualities = Object.keys(playbackInfo.qualities) as Array<
              keyof typeof playbackInfo.qualities
            >;
            const highestQuality = qualities[0];
            const downloadUrl = playbackInfo.qualities[highestQuality].urls.hls;
            window.open(downloadUrl, "_blank");
            toast({
              title: "Download started",
              description: "Your video is being prepared for download",
              duration: 3000,
            });
          } catch (error) {
            toast({
              title: "Download failed",
              description: "There was an error preparing your download",
              variant: "destructive",
            });
          }
          break;
        case "share":
          try {
            const shareUrl = `${window.location.origin}/videos/${video.video_id}`;
            await navigator.clipboard.writeText(shareUrl);
            toast({
              title: "Link copied",
              description: "Video link copied to clipboard",
              duration: 2000,
            });
          } catch (error) {
            toast({
              title: "Copy failed",
              description: "Could not copy link to clipboard",
              variant: "destructive",
            });
          }
          break;
        case "delete":
          if (confirm("Are you sure you want to delete this video?")) {
            deleteVideoMutation.mutate(video.video_id);
          }
          break;
      }
    },
    [toast, deleteVideoMutation]
  );
  const formatDate = useCallback((date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  }, []);
  const formatDuration = useCallback((seconds: number) => {
    if (!seconds || seconds < 0) return "0:00";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    if (hours > 0) {
      return `${hours}:${String(minutes).padStart(2, "0")}:${String(
        remainingSeconds
      ).padStart(2, "0")}`;
    }
    return `${minutes}:${String(remainingSeconds).padStart(2, "0")}`;
  }, []);
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center gap-4">
          <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-indigo-500 to-violet-600 p-0.5 animate-pulse">
            <div className="w-full h-full rounded-[14px] bg-[#0f1729] flex items-center justify-center">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
          </div>
          <p className="text-slate-400">Loading your videos...</p>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl font-bold text-white">My Videos</h1>
        <p className="text-slate-400 text-sm">
          Manage and organize your video content
        </p>
      </div>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex-1 w-full md:w-auto">
          <form onSubmit={handleSearch} className="relative group">
            <div className="relative">
              <Search
                className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 transition-colors ${
                  isSearching ? "text-indigo-400" : "text-slate-400"
                }`}
              />
              <Input
                placeholder="Search videos..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`pl-10 pr-20 bg-[#0f1729]/50 border-slate-800/60 text-white w-full transition-all
                  ${
                    isSearching
                      ? "border-indigo-500/50"
                      : "hover:border-slate-700 focus:border-indigo-500/50"
                  }
                  ${
                    searchResults.length > 0
                      ? "rounded-t-md rounded-b-none"
                      : "rounded-md"
                  }`}
              />
              {searchQuery && (
                <button
                  type="button"
                  onClick={clearSearch}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
            <AnimatePresence>
              {searchQuery && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute w-full z-50 mt-[1px] bg-[#0f1729]/95 backdrop-blur-xl border border-t-0 border-slate-800/60 rounded-b-md shadow-lg max-h-[300px] overflow-y-auto"
                >
                  <div className="p-2 text-xs text-slate-400 border-b border-slate-800/60 flex justify-between items-center">
                    <span>Search Results</span>
                    <span className="text-indigo-400">
                      {searchResults.length} videos
                    </span>
                  </div>
                  {isSearching ? (
                    <div className="p-4 flex justify-center">
                      <Loader2 className="w-5 h-5 text-indigo-400 animate-spin" />
                    </div>
                  ) : searchResults.length === 0 ? (
                    <Card className="bg-gradient-to-br from-[#2c245d] via-[#3b2a82] to-[#4b2071] border border-violet-700/30 shadow-xl rounded-2xl mb-8">
                      <CardContent className="p-8">
                        <div className="flex flex-col md:flex-row items-center gap-6">
                          <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-indigo-500/40 to-violet-500/40 flex items-center justify-center border border-indigo-400/30 shadow-lg">
                            <FileVideo className="w-8 h-8 text-indigo-300" />
                          </div>
                          <div className="flex-1 text-center md:text-left">
                            <h3 className="text-2xl font-semibold text-white mb-2">
                              Welcome to{" "}
                              <span className="text-indigo-300">
                                StreamScale
                              </span>
                            </h3>
                            <p className="text-slate-300 text-sm mb-5 max-w-md mx-auto md:mx-0">
                              Start by uploading a video — we'll handle the
                              processing and streaming.
                            </p>
                            <Button
                              onClick={() => navigate("/dashboard/upload")}
                              className="bg-gradient-to-r from-indigo-500 to-violet-600 hover:from-indigo-400 hover:to-violet-500 text-white font-medium px-5 py-2 rounded-lg shadow-lg"
                            >
                              <Upload className="w-4 h-4 mr-2" />
                              Upload Your First Video
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <div>
                      {searchResults.map((video) => (
                        <motion.div
                          key={video.video_id}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="p-2 hover:bg-indigo-500/10 cursor-pointer flex items-center gap-3 rounded-md"
                          onClick={() => {
                            navigate(`/dashboard/play/${video.video_id}`);
                            clearSearch();
                          }}
                        >
                          <div className="w-16 h-9 bg-black/40 rounded flex items-center justify-center flex-shrink-0">
                            {video.playback_info?.thumbnail ? (
                              <img
                                src={video.playback_info.thumbnail}
                                alt={video.file_name}
                                className="w-full h-full object-cover rounded"
                              />
                            ) : (
                              <VideoIcon className="w-4 h-4 text-indigo-400/50" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-white truncate">
                              {video.file_name}
                            </div>
                            <div className="text-xs text-slate-400 flex items-center gap-2">
                              <span>{formatDuration(video.duration || 0)}</span>
                              <span className="text-slate-600">•</span>
                              <span
                                className={`capitalize ${
                                  video.status === "completed"
                                    ? "text-emerald-400"
                                    : video.status === "processing"
                                    ? "text-amber-400"
                                    : video.status === "queued"
                                    ? "text-blue-400"
                                    : "text-red-400"
                                }`}
                              >
                                {video.status || "unknown"}
                              </span>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </form>
        </div>
        <div className="flex items-center gap-3 w-full md:w-auto">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="bg-[#0f1729]/50 border-slate-800/60 text-white w-[140px] hover:border-slate-700 focus:border-indigo-500/50">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent className="bg-[#0f1729]/95 border-slate-800/60">
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="queued">Queued</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="bg-[#0f1729]/50 border-slate-800/60 text-white w-[140px] hover:border-slate-700 focus:border-indigo-500/50">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent className="bg-[#0f1729]/95 border-slate-800/60">
              <SelectItem value="date">Date Added</SelectItem>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="size">File Size</SelectItem>
              <SelectItem value="duration">Duration</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={() => navigate("/dashboard/upload")}
            className="bg-[#0f1729]/50 border-slate-800/60 text-white hover:bg-indigo-500/10 hover:text-white hover:border-indigo-500/50 ml-2"
          >
            <Upload className="w-4 h-4 mr-2" />
            Upload
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-4">
        <AnimatePresence>
          {sortedAndFilteredVideos.map((video, index) => {
            if (!video || !video.video_id) {
              console.warn("Invalid video data:", video);
              return null;
            }
            return (
              <VideoCard
                key={video.video_id}
                video={video}
                index={index}
                onVideoSelect={handleVideoSelect}
                onVideoAction={handleVideoAction}
                formatDate={formatDate}
                formatDuration={formatDuration}
                getStatusIcon={getStatusIcon}
              />
            );
          })}
        </AnimatePresence>
      </div>
      {sortedAndFilteredVideos.length === 0 && !isLoading && (
        <div className="text-center py-16 px-4 border border-dashed border-slate-800/60 rounded-xl bg-[#0f1729]/30 backdrop-blur-sm">
          <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-indigo-500/10 to-violet-500/10 mx-auto flex items-center justify-center mb-6 border border-indigo-500/20">
            <FileVideo className="w-10 h-10 text-indigo-400" />
          </div>
          <h3 className="text-xl font-medium text-white mb-3">
            No videos found
          </h3>
          <p className="text-slate-400 mb-8 max-w-md mx-auto">
            {searchQuery
              ? "No videos match your search criteria"
              : "Upload your first video to get started with StreamScale"}
          </p>
          <Button
            onClick={() => navigate("/dashboard/upload")}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-500 hover:to-violet-500 font-medium shadow-lg shadow-indigo-500/10 px-6 py-5 h-auto"
          >
            <Upload className="w-5 h-5 mr-2" />
            Upload Your First Video
          </Button>
        </div>
      )}
    </div>
  );
});
VideoList.displayName = "VideoList";
export default VideoList;
