{"version": 3, "sources": ["../../@vidstack/react/node_modules/media-captions/dist/dev/srt-parser.js"], "sourcesContent": ["import { V as VTTParser, a as VTT<PERSON><PERSON>, b as VTTCue } from './index.js';\n\nconst MILLISECOND_SEP_RE = /,/g, TIMESTAMP_SEP = \"-->\";\nclass SRTParser extends VTTParser {\n  parse(line, lineCount) {\n    if (line === \"\") {\n      if (this._cue) {\n        this._cues.push(this._cue);\n        this._init.onCue?.(this._cue);\n        this._cue = null;\n      }\n      this._block = VTTBlock.None;\n    } else if (this._block === VTTBlock.Cue) {\n      this._cue.text += (this._cue.text ? \"\\n\" : \"\") + line;\n    } else if (line.includes(TIMESTAMP_SEP)) {\n      const result = this._parseTimestamp(line, lineCount);\n      if (result) {\n        this._cue = new VTTCue(result[0], result[1], result[2].join(\" \"));\n        this._cue.id = this._prevLine;\n        this._block = VTTBlock.Cue;\n      }\n    }\n    this._prevLine = line;\n  }\n  _parseTimestamp(line, lineCount) {\n    return super._parseTimestamp(line.replace(MILLISECOND_SEP_RE, \".\"), lineCount);\n  }\n}\nfunction createSRTParser() {\n  return new SRTParser();\n}\n\nexport { SRTParser, createSRTParser as default };\n"], "mappings": ";;;;;;;;AAEA,IAAM,qBAAqB;AAA3B,IAAiC,gBAAgB;AACjD,IAAM,YAAN,cAAwB,UAAU;AAAA,EAChC,MAAM,MAAM,WAAW;AAJzB;AAKI,QAAI,SAAS,IAAI;AACf,UAAI,KAAK,MAAM;AACb,aAAK,MAAM,KAAK,KAAK,IAAI;AACzB,yBAAK,OAAM,UAAX,4BAAmB,KAAK;AACxB,aAAK,OAAO;AAAA,MACd;AACA,WAAK,SAAS,SAAS;AAAA,IACzB,WAAW,KAAK,WAAW,SAAS,KAAK;AACvC,WAAK,KAAK,SAAS,KAAK,KAAK,OAAO,OAAO,MAAM;AAAA,IACnD,WAAW,KAAK,SAAS,aAAa,GAAG;AACvC,YAAM,SAAS,KAAK,gBAAgB,MAAM,SAAS;AACnD,UAAI,QAAQ;AACV,aAAK,OAAO,IAAI,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,GAAG,CAAC;AAChE,aAAK,KAAK,KAAK,KAAK;AACpB,aAAK,SAAS,SAAS;AAAA,MACzB;AAAA,IACF;AACA,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,gBAAgB,MAAM,WAAW;AAC/B,WAAO,MAAM,gBAAgB,KAAK,QAAQ,oBAAoB,GAAG,GAAG,SAAS;AAAA,EAC/E;AACF;AACA,SAAS,kBAAkB;AACzB,SAAO,IAAI,UAAU;AACvB;", "names": []}