"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.STORYBOARD_HYDRATION_STATUS = exports.SELECT_OR_HOVER_STORYBOARD = exports.DELETE_STYLE_CONSTANT = exports.FIXED_IFRAME_MESSAGE_IDS = exports.INHERITABLE_CSS_PROPS = exports.CSS_VALUES_TO_COLLECT_FOR_PARENT = exports.CSS_VALUES_TO_COLLECT = void 0;
exports.CSS_VALUES_TO_COLLECT = new Set([
    'display',
    'flex-direction',
    'flex-grow',
    'flex-shrink',
    'font-family',
    'align-items',
    'justify-content',
    'column-gap',
    'row-gap',
    'flex-wrap',
    'align-content',
    'overflow',
    'text-align',
    'width',
    'max-width',
    'min-width',
    'height',
    'max-height',
    'min-height',
    'font-size',
    'line-height',
    'padding',
    'padding-top',
    'padding-left',
    'padding-right',
    'padding-bottom',
    'margin',
    'margin-top',
    'margin-left',
    'margin-right',
    'margin-bottom',
    'border-radius',
    'font-family',
    'font-weight',
    'object-fit',
    'background-clip',
    'border-left-style',
    'border-top-style',
    'border-right-style',
    'border-bottom-style',
    'border-left-width',
    'border-top-width',
    'border-right-width',
    'border-bottom-width',
    'border-left-color',
    'border-top-color',
    'border-right-color',
    'border-bottom-color',
    'background-color',
    'color',
    'transform',
    'border-top-left-radius',
    'border-top-right-radius',
    'border-bottom-right-radius',
    'border-bottom-left-radius',
    'letter-spacing',
    'opacity',
    'font-style',
    'text-decoration-line',
    'top',
    'left',
    'right',
    'bottom',
    'position',
    'background-image',
]);
exports.CSS_VALUES_TO_COLLECT_FOR_PARENT = new Set([
    'display',
    'flex-direction',
]);
// Taken from https://web.dev/learn/css/inheritance/
exports.INHERITABLE_CSS_PROPS = {
    azimuth: true,
    'border-collapse': true,
    'border-spacing': true,
    'caption-side': true,
    color: true,
    cursor: true,
    direction: true,
    'empty-cells': true,
    'font-family': true,
    'font-size': true,
    'font-style': true,
    'font-variant': true,
    'font-weight': true,
    font: true,
    'letter-spacing': true,
    'line-height': true,
    'list-style-image': true,
    'list-style-position': true,
    'list-style-type': true,
    'list-style': true,
    orphans: true,
    quotes: true,
    'text-align': true,
    'text-indent': true,
    'text-transform': true,
    visibility: true,
    'white-space': true,
    widows: true,
    'word-spacing': true,
};
// Matches the interface on the frontend
var FIXED_IFRAME_MESSAGE_IDS;
(function (FIXED_IFRAME_MESSAGE_IDS) {
    FIXED_IFRAME_MESSAGE_IDS["HOVERED_ELEMENT_KEY"] = "HOVERED_ELEMENT_KEY";
    FIXED_IFRAME_MESSAGE_IDS["SELECTED_ELEMENT_KEY"] = "SELECTED_ELEMENT_KEY";
    FIXED_IFRAME_MESSAGE_IDS["MULTI_SELECTED_ELEMENT_KEYS"] = "MULTI_SELECTED_ELEMENT_KEYS";
    FIXED_IFRAME_MESSAGE_IDS["CONTEXT_REQUESTED"] = "CONTEXT_REQUESTED";
    FIXED_IFRAME_MESSAGE_IDS["WHEEL_EVENT"] = "WHEEL_EVENT";
    FIXED_IFRAME_MESSAGE_IDS["NAV_TREE"] = "NAV_TREE";
    FIXED_IFRAME_MESSAGE_IDS["PROCESSED_CSS_RULES_FOR_ELEMENT"] = "PROCESSED_CSS_RULES_FOR_ELEMENT";
    FIXED_IFRAME_MESSAGE_IDS["CSS_EVALS_FOR_ELEMENT"] = "CSS_EVALS_FOR_ELEMENT";
    FIXED_IFRAME_MESSAGE_IDS["ELEMENT_CLASS_LIST"] = "ELEMENT_CLASS_LIST";
    FIXED_IFRAME_MESSAGE_IDS["KEY_DOWN_EVENT"] = "KEY_DOWN_EVENT";
    FIXED_IFRAME_MESSAGE_IDS["KEY_UP_EVENT"] = "KEY_UP_EVENT";
    FIXED_IFRAME_MESSAGE_IDS["MOUSE_MOVE_EVENT"] = "MOUSE_MOVE_EVENT";
    FIXED_IFRAME_MESSAGE_IDS["DRAG_START_EVENT"] = "DRAG_START_EVENT";
    FIXED_IFRAME_MESSAGE_IDS["DRAG_END_EVENT"] = "DRAG_END_EVENT";
    FIXED_IFRAME_MESSAGE_IDS["DRAG_CANCEL_EVENT"] = "DRAG_CANCEL_EVENT";
    FIXED_IFRAME_MESSAGE_IDS["LATEST_HREF"] = "LATEST_HREF";
    FIXED_IFRAME_MESSAGE_IDS["LATEST_HYDRATION_ERROR_STATUS"] = "LATEST_HYDRATION_ERROR_STATUS";
    FIXED_IFRAME_MESSAGE_IDS["START_EDITING_TEXT"] = "START_EDITING_TEXT";
    FIXED_IFRAME_MESSAGE_IDS["EDITED_TEXT"] = "EDITED_TEXT";
    FIXED_IFRAME_MESSAGE_IDS["INSTANT_UPDATE_DONE"] = "INSTANT_UPDATE_DONE";
    FIXED_IFRAME_MESSAGE_IDS["EDIT_DYNAMIC_TEXT"] = "EDIT_DYNAMIC_TEXT";
})(FIXED_IFRAME_MESSAGE_IDS || (exports.FIXED_IFRAME_MESSAGE_IDS = FIXED_IFRAME_MESSAGE_IDS = {}));
exports.DELETE_STYLE_CONSTANT = null;
exports.SELECT_OR_HOVER_STORYBOARD = 'SELECT_OR_HOVER_STORYBOARD';
var STORYBOARD_HYDRATION_STATUS;
(function (STORYBOARD_HYDRATION_STATUS) {
    STORYBOARD_HYDRATION_STATUS["OTHER_ERROR"] = "other_error";
    STORYBOARD_HYDRATION_STATUS["ERROR"] = "error";
    STORYBOARD_HYDRATION_STATUS["NO_ERROR"] = "no_error";
})(STORYBOARD_HYDRATION_STATUS || (exports.STORYBOARD_HYDRATION_STATUS = STORYBOARD_HYDRATION_STATUS = {}));
//# sourceMappingURL=data:application/json;base64,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